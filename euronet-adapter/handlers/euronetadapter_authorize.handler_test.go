package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	gdProxyAPI "gitlab.myteksi.net/dbmy/gd-proxy/api"
	gdProxyMock "gitlab.myteksi.net/dbmy/gd-proxy/api/mock"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transaction/authorize"
	txnrecorder "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transaction/recorder"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
)

func TestAuthorize(t *testing.T) {
	type ExpectedResponse struct {
		Code        string
		Description string
	}
	testReqID := uuid.NewString()
	testTime := time.Now()
	defaultHappyReq := &api.TransactionAPIRequest{
		TransactionRequest: &api.TransactionRequest{
			AppHeader: &api.AppHeader{
				ReqID:       testReqID,
				PartID:      logic.TransactionPartID,
				ServiceName: dto.TxnAuth,
			},
			TransactionInfo: &api.TransactionInfo{
				Mti:                         "0100",
				ProxyNumber:                 "1234567",
				ProcessingCode:              "001000",
				TransactionAmount:           10,
				CardHolderBillingAmount:     10,
				TransmissionDateTime:        testTime,
				SystemTraceAuditNumber:      "1234",
				LocalTransactionDateTime:    "**********",
				MerchantCategoryCode:        "1000",
				AcquirerCountryCode:         "100",
				PanEntryMode:                "810",
				NetworkID:                   "MCN",
				POSDataSF4:                  "1",
				POSDataSF5:                  "1",
				POSDataSF7:                  "1",
				POSDataSF10:                 "1",
				AcquirerID:                  "1000",
				RetrievalReferenceNumber:    "*********",
				AuthorizationID:             "12345",
				CardAcceptorTerminalID:      "1000",
				CardAcceptorMerchantID:      "1000",
				CardAcceptorTermName:        "TEST",
				TransactionCurrencyCode:     "702",
				CardHolderCurrencyCode:      "703",
				TransactionID:               "1000",
				OrgTransactionID:            "",
				ThreeDsAuthTID:              "Y",
				DCCIndicator:                "Y",
				ECOMIndicator:               "Y",
				UCAFIndicator:               "Y",
				ThreeDsValidation:           "Y",
				PANValidation:               "Y",
				ExpiryDateValidation:        "Y",
				PinValidation:               "Y",
				CVVValidation:               "Y",
				CVV2Validation:              "Y",
				EMVValidation:               "Y",
				ReplacementAmount:           0,
				CardHolderReplacementAmount: 0,
				DISecIssData:                "",
				DIFraudScore:                "",
				DIScoreReasonCode:           "",
				MSTransactionTypeID:         "",
				MSTransactionRefData:        "",
			},
		},
	}
	defaultHappyResp := &api.TransactionAPIResponse{
		TransactionResponse: &api.TransactionResponse{
			AppHeader: &api.AppHeader{
				ReqID:       testReqID,
				PartID:      logic.TransactionPartID,
				ServiceName: dto.TxnAuth,
			},
			Response: &api.Response{
				ResponseCode: logic.ApprovedCode,
				Description:  logic.Approved,
			},
			TransactionInfo: &api.TransactionInfo{
				Mti:                         "0110",
				ProxyNumber:                 "1234567",
				ProcessingCode:              "001000",
				TransactionAmount:           10,
				CardHolderBillingAmount:     10,
				TransmissionDateTime:        testTime,
				SystemTraceAuditNumber:      "1234",
				LocalTransactionDateTime:    "**********",
				MerchantCategoryCode:        "4111",
				AcquirerCountryCode:         "100",
				PanEntryMode:                "900",
				NetworkID:                   "MCN",
				POSDataSF4:                  "1",
				POSDataSF5:                  "1",
				POSDataSF7:                  "1",
				POSDataSF10:                 "1",
				AcquirerID:                  "1000",
				RetrievalReferenceNumber:    "*********",
				AuthorizationID:             "12345",
				CardAcceptorTerminalID:      "1000",
				CardAcceptorMerchantID:      "1000",
				CardAcceptorTermName:        "TEST",
				TransactionCurrencyCode:     "702",
				CardHolderCurrencyCode:      "703",
				TransactionID:               "1000",
				OrgTransactionID:            "",
				ThreeDsAuthTID:              "Y",
				DCCIndicator:                "Y",
				ECOMIndicator:               "Y",
				UCAFIndicator:               "Y",
				ThreeDsValidation:           "Y",
				PANValidation:               "Y",
				ExpiryDateValidation:        "Y",
				PinValidation:               "Y",
				CVVValidation:               "Y",
				CVV2Validation:              "Y",
				EMVValidation:               "Y",
				ReplacementAmount:           0,
				CardHolderReplacementAmount: 0,
				DISecIssData:                "",
				DIFraudScore:                "",
				DIScoreReasonCode:           "",
				MSTransactionTypeID:         "",
				MSTransactionRefData:        "",
			},
		},
	}
	testTxnSystemErrorResp := &api.TransactionAPIResponse{
		TransactionResponse: &api.TransactionResponse{
			AppHeader: &api.AppHeader{
				ReqID:       testReqID,
				PartID:      logic.TransactionPartID,
				ServiceName: dto.TxnAuth,
			},
			Response: &api.Response{
				ResponseCode: logic.ErrSystemMalfunction.Code,
				Description:  logic.ErrSystemMalfunction.Message,
			},
			TransactionInfo: &api.TransactionInfo{
				Mti:                         "0110",
				ProxyNumber:                 "1234567",
				ProcessingCode:              "001000",
				TransactionAmount:           10,
				CardHolderBillingAmount:     10,
				TransmissionDateTime:        testTime,
				SystemTraceAuditNumber:      "1234",
				LocalTransactionDateTime:    "**********",
				MerchantCategoryCode:        "1000",
				AcquirerCountryCode:         "100",
				PanEntryMode:                "810",
				NetworkID:                   "MCN",
				POSDataSF4:                  "1",
				POSDataSF5:                  "1",
				POSDataSF7:                  "1",
				POSDataSF10:                 "1",
				AcquirerID:                  "1000",
				RetrievalReferenceNumber:    "*********",
				AuthorizationID:             "12345",
				CardAcceptorTerminalID:      "1000",
				CardAcceptorMerchantID:      "1000",
				CardAcceptorTermName:        "TEST",
				TransactionCurrencyCode:     "702",
				CardHolderCurrencyCode:      "703",
				TransactionID:               "1000",
				OrgTransactionID:            "",
				ThreeDsAuthTID:              "Y",
				DCCIndicator:                "Y",
				ECOMIndicator:               "Y",
				UCAFIndicator:               "Y",
				ThreeDsValidation:           "Y",
				PANValidation:               "Y",
				ExpiryDateValidation:        "Y",
				PinValidation:               "Y",
				CVVValidation:               "Y",
				CVV2Validation:              "Y",
				EMVValidation:               "Y",
				ReplacementAmount:           0,
				CardHolderReplacementAmount: 0,
				DISecIssData:                "",
				DIFraudScore:                "",
				DIScoreReasonCode:           "",
				MSTransactionTypeID:         "",
				MSTransactionRefData:        "",
			},
		},
	}
	testAuthMarkupReq := &api.TransactionAPIRequest{
		TransactionRequest: &api.TransactionRequest{
			AppHeader: &api.AppHeader{
				ReqID:       testReqID,
				PartID:      logic.TransactionPartID,
				ServiceName: dto.TxnAuth,
			},
			TransactionInfo: &api.TransactionInfo{
				Mti:                         "0100",
				ProxyNumber:                 "1234567",
				ProcessingCode:              "001000",
				TransactionAmount:           10,
				CardHolderBillingAmount:     35,
				ConversionRate:              63500000,
				CardAcceptorTermName:        "BUS/MRT                SINGAPORE     SGP",
				TransmissionDateTime:        testTime,
				SystemTraceAuditNumber:      "1234",
				LocalTransactionDateTime:    "**********",
				MerchantCategoryCode:        "4111",
				AcquirerCountryCode:         "100",
				PanEntryMode:                "810",
				NetworkID:                   "MCN",
				POSDataSF4:                  "1",
				POSDataSF5:                  "1",
				POSDataSF7:                  "1",
				POSDataSF10:                 "1",
				AcquirerID:                  "1000",
				RetrievalReferenceNumber:    "*********",
				AuthorizationID:             "12345",
				CardAcceptorTerminalID:      "1000",
				CardAcceptorMerchantID:      "BUS",
				TransactionCurrencyCode:     "702",
				CardHolderCurrencyCode:      "703",
				TransactionID:               "1000",
				OrgTransactionID:            "",
				ThreeDsAuthTID:              "Y",
				DCCIndicator:                "Y",
				ECOMIndicator:               "Y",
				UCAFIndicator:               "Y",
				ThreeDsValidation:           "Y",
				PANValidation:               "Y",
				ExpiryDateValidation:        "Y",
				PinValidation:               "Y",
				CVVValidation:               "Y",
				CVV2Validation:              "Y",
				EMVValidation:               "Y",
				ReplacementAmount:           0,
				CardHolderReplacementAmount: 0,
				DISecIssData:                "",
				DIFraudScore:                "",
				DIScoreReasonCode:           "",
				MSTransactionTypeID:         "",
				MSTransactionRefData:        "",
			},
		},
	}
	testAuthMarkupResp := &api.TransactionAPIResponse{
		TransactionResponse: &api.TransactionResponse{
			AppHeader: &api.AppHeader{
				ReqID:       testReqID,
				PartID:      logic.TransactionPartID,
				ServiceName: dto.TxnAuth,
			},
			Response: &api.Response{
				ResponseCode: logic.ApprovedCode,
				Description:  logic.Approved,
			},
			TransactionInfo: &api.TransactionInfo{
				Mti:                         "0110",
				ProxyNumber:                 "12345678",
				ProcessingCode:              "001000",
				TransactionAmount:           15000,
				CardHolderBillingAmount:     5250,
				TransmissionDateTime:        testTime,
				SystemTraceAuditNumber:      "1234",
				LocalTransactionDateTime:    "**********",
				MerchantCategoryCode:        "4111",
				AcquirerCountryCode:         "100",
				PanEntryMode:                "900",
				NetworkID:                   "MCN",
				POSDataSF4:                  "1",
				POSDataSF5:                  "1",
				POSDataSF7:                  "1",
				POSDataSF10:                 "1",
				AcquirerID:                  "1000",
				RetrievalReferenceNumber:    "*********",
				AuthorizationID:             "12345",
				CardAcceptorTerminalID:      "1000",
				CardAcceptorMerchantID:      "1000",
				CardAcceptorTermName:        "TEST",
				TransactionCurrencyCode:     "702",
				CardHolderCurrencyCode:      "703",
				TransactionID:               "1000",
				OrgTransactionID:            "",
				ThreeDsAuthTID:              "Y",
				DCCIndicator:                "Y",
				ECOMIndicator:               "Y",
				UCAFIndicator:               "Y",
				ThreeDsValidation:           "Y",
				PANValidation:               "Y",
				ExpiryDateValidation:        "Y",
				PinValidation:               "Y",
				CVVValidation:               "Y",
				CVV2Validation:              "Y",
				EMVValidation:               "Y",
				ReplacementAmount:           0,
				CardHolderReplacementAmount: 0,
				DISecIssData:                "",
				DIFraudScore:                "",
				DIScoreReasonCode:           "",
				MSTransactionTypeID:         "",
				MSTransactionRefData:        "",
			},
		},
	}
	testCardMapping := []*storage.UserCardMapping{
		{
			ID: 1,
		},
	}
	scenarios := []struct {
		desc                      string
		req                       *api.TransactionAPIRequest
		resp                      *api.TransactionAPIResponse
		executeResp               *api.TransactionResponse
		mockRiskCheckResponse     *gdProxyAPI.InternalRiskCheckResponse
		featureFlags              *config.FeatureFlags
		preTransactionCheckConfig *config.PreTransactionCheckConfig
		expectedResponse          ExpectedResponse

		errExecute, expectedErr, mockRiskCheckError error
	}{
		{
			desc:        "happy path",
			req:         defaultHappyReq,
			resp:        defaultHappyResp,
			executeResp: defaultHappyResp.TransactionResponse,
			featureFlags: &config.FeatureFlags{
				EnablePreTransactionCheck: true,
			},
			errExecute:  nil,
			expectedErr: nil,
		},
		{
			desc:        "error",
			req:         defaultHappyReq,
			resp:        testTxnSystemErrorResp,
			executeResp: nil,
			featureFlags: &config.FeatureFlags{
				EnablePreTransactionCheck: true,
			},
			errExecute:  errors.New("simulate error"),
			expectedErr: nil,
		},
		{
			desc:        "happy path with auth markup",
			req:         testAuthMarkupReq,
			resp:        testAuthMarkupResp,
			executeResp: testAuthMarkupResp.TransactionResponse,
			featureFlags: &config.FeatureFlags{
				EnablePreTransactionCheck: true,
			},
			errExecute:  nil,
			expectedErr: nil,
		},
		{
			desc:        "happy path without pre transaction check",
			req:         testAuthMarkupReq,
			resp:        testAuthMarkupResp,
			executeResp: testAuthMarkupResp.TransactionResponse,
			featureFlags: &config.FeatureFlags{
				EnablePreTransactionCheck: false,
			},
			errExecute:  nil,
			expectedErr: nil,
		},
		{
			desc:        "happy path without pre transaction check but pre transaction check not in whitelist",
			req:         testAuthMarkupReq,
			resp:        testAuthMarkupResp,
			executeResp: testAuthMarkupResp.TransactionResponse,
			featureFlags: &config.FeatureFlags{
				EnablePreTransactionCheck: true,
			},
			mockRiskCheckResponse: &gdProxyAPI.InternalRiskCheckResponse{
				Recommendations: []string{},
			},
			errExecute:  nil,
			expectedErr: nil,
		},
		{
			desc:        "happy path with pre transaction check but pre transaction check allowed",
			req:         testAuthMarkupReq,
			resp:        testAuthMarkupResp,
			executeResp: testAuthMarkupResp.TransactionResponse,
			featureFlags: &config.FeatureFlags{
				EnablePreTransactionCheck: true,
			},
			preTransactionCheckConfig: &config.PreTransactionCheckConfig{
				AllowedProxyNumbers:         []string{"123"},
				AllowedProxyNumberThreshold: 100,
			},
			mockRiskCheckResponse: &gdProxyAPI.InternalRiskCheckResponse{
				Recommendations: []string{gdProxyAPI.RecommendationAllow},
			},
			mockRiskCheckError: nil,
			errExecute:         nil,
			expectedErr:        nil,
		},
		{
			desc:        "happy path with pre transaction check but pre transaction check NotAllow",
			req:         testAuthMarkupReq,
			resp:        testAuthMarkupResp,
			executeResp: testAuthMarkupResp.TransactionResponse,
			featureFlags: &config.FeatureFlags{
				EnablePreTransactionCheck: true,
			},
			preTransactionCheckConfig: &config.PreTransactionCheckConfig{
				AllowedProxyNumbers:         []string{"123"},
				AllowedProxyNumberThreshold: 100,
			},
			mockRiskCheckResponse: &gdProxyAPI.InternalRiskCheckResponse{
				Recommendations: []string{gdProxyAPI.RecommendationNotAllow},
			},
			mockRiskCheckError: nil,
			errExecute:         nil,
			expectedErr:        nil,
			expectedResponse:   ExpectedResponse{Code: "1329", Description: "Transaction is rejected"},
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			txnChannel := make(chan txnrecorder.TransactionRecord)

			mockClient := &authorize.MockWorkflow{}
			mockClient.On("ExecuteAuthorizeTransactionWorkflow", mock.Anything, mock.Anything, mock.Anything).
				Return(scenario.executeResp, scenario.errExecute)
			mockAllowTransactionTypes := &config.AllowTransactionTypes{Code: []string{"C07"}}

			// Mock GD Proxy Client
			mockGdProxyClient := &gdProxyMock.GdProxy{}
			rawJSON, _ := json.Marshal(scenario.req.TransactionRequest.TransactionInfo)
			var eventFeatures map[string]interface{}
			_ = json.Unmarshal(rawJSON, &eventFeatures)

			gdProxyRequest := &gdProxyAPI.InternalRiskCheckRequest{
				RiskCheckEvent: "CardTransaction",
				EventFeatures:  eventFeatures,
			}
			mockGdProxyClient.On("InternalRiskCheck", mock.Anything, gdProxyRequest).Return(scenario.mockRiskCheckResponse, scenario.mockRiskCheckError)

			// Mock pre transaction configs
			threshold := 0
			allowedProxyNumbers := []string{"123"}
			if scenario.preTransactionCheckConfig != nil {
				threshold = scenario.preTransactionCheckConfig.AllowedProxyNumberThreshold
				allowedProxyNumbers = scenario.preTransactionCheckConfig.AllowedProxyNumbers
			}

			testService := &EuronetAdapterService{
				AuthorizeWorkflow:     mockClient,
				AllowTransactionTypes: mockAllowTransactionTypes,
				CountryConfig: &config.CountryConfig{
					AppHeaderConfig: config.AppHeaderConfig{ParticipantIDs: []string{logic.TransactionPartID}},
				},
				TxnRecorderChannel:     txnChannel,
				AuthAmountMarkupConfig: []config.AuthAmountMarkupConfig{},
				FeatureFlags: &config.FeatureFlags{
					EnablePreTransactionCheck: scenario.featureFlags.EnablePreTransactionCheck,
				},
				PreTransactionCheckConfig: &config.PreTransactionCheckConfig{
					AllowedProxyNumberThreshold: threshold,
					AllowedProxyNumbers:         allowedProxyNumbers,
				},
				GdPRoxyClient: mockGdProxyClient,
				StatsD:        statsd.NewNoop(),
			}
			mockStorage := &storage.MockIUserCardMappingDAO{}
			mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(testCardMapping, nil)
			storage.UserCardMappingD = mockStorage

			mockCardTransaction := &storage.MockICardTransactionDAO{}
			storage.CardTransactionD = mockCardTransaction
			mockCardTransaction.On("Find", mock.Anything, mock.Anything).Return(nil, nil)

			resp, err := testService.Authorize(context.Background(), scenario.req)

			if scenario.expectedErr != nil {
				txnRecord := <-txnChannel
				assert.Equal(t, scenario.expectedErr, err, scenario.desc)
				assert.NotNil(t, txnRecord)
				assert.Equal(t, scenario.req, txnRecord.Request)
				assert.Equal(t, scenario.resp, txnRecord.Response)
			} else {
				if scenario.expectedResponse != (ExpectedResponse{}) {
					assert.Equal(t, scenario.expectedResponse.Code, resp.TransactionResponse.Response.ResponseCode, "Code does not match")
					assert.Equal(t, scenario.expectedResponse.Description, resp.TransactionResponse.Response.Description, "Description does not match")
				} else {
					txnRecord := <-txnChannel
					assert.Equal(t, scenario.resp.TransactionResponse.Response, resp.TransactionResponse.Response, scenario.desc)
					assert.Equal(t, scenario.resp.TransactionResponse.TransactionInfo, resp.TransactionResponse.TransactionInfo, scenario.desc)
					assert.Equal(t, scenario.resp.TransactionResponse.AppHeader, resp.TransactionResponse.AppHeader, scenario.desc)
					assert.NotNil(t, txnRecord)
					assert.Equal(t, scenario.req, txnRecord.Request)
					assert.Equal(t, scenario.resp, txnRecord.Response)
					assert.Equal(t, scenario.resp.TransactionResponse.TransactionInfo.CardHolderBillingAmount, resp.TransactionResponse.TransactionInfo.CardHolderBillingAmount) // request amount
					assert.Equal(t, scenario.resp.TransactionResponse.TransactionInfo.TransactionAmount, resp.TransactionResponse.TransactionInfo.TransactionAmount)             // original amount
				}

			}
		})
	}
}

func TestAuthMandatoryReqValidator(t *testing.T) {
	tests := []struct {
		name        string
		req         *api.TransactionAPIRequest
		expectedErr *logic.AdapterError
	}{
		{
			name:        "nil req",
			req:         nil,
			expectedErr: &logic.ErrInvalidRequest,
		},
		{
			name: "nil TransactionRequest",
			req: &api.TransactionAPIRequest{
				TransactionRequest: nil,
			},
			expectedErr: &logic.ErrInvalidRequest,
		},
		{
			name: "nil app header",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: nil,
				},
			},
			expectedErr: &logic.ErrInvalidAppHeader,
		},
		{
			name: "req ID too long",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						ReqID: uuid.NewString() + "a",
					},
				},
			},
			expectedErr: &logic.ErrInvalidRequestID,
		},
		{
			name: "invalid part ID",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						ReqID:  uuid.NewString(),
						PartID: "NONGXS",
					},
				},
			},
			expectedErr: &logic.ErrInvalidPartID,
		},
		{
			name: "invalid service name",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						ReqID:       uuid.NewString(),
						PartID:      logic.TransactionPartID,
						ServiceName: "TEST",
					},
				},
			},
			expectedErr: &logic.ErrInvalidServiceName,
		},
		{
			name: "nil transactionInfo",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: nil,
				},
			},
			expectedErr: &logic.ErrInvalidRequest,
		},
		{
			name: "missing MTI",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti: "",
					},
				},
			},
			expectedErr: &logic.ErrInvalidMTI,
		},
		{
			name: "missing ProxyNumber",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:         "0100",
						ProxyNumber: "",
					},
				},
			},
			expectedErr: &logic.ErrMissingProxyNumber,
		},
		{
			name: "missing ProcessingCode",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:            "0100",
						ProxyNumber:    "1234567",
						ProcessingCode: "",
					},
				},
			},
			expectedErr: &logic.ErrMissingProcessingCode,
		},
		{
			name: "missing SystemTraceAuditNumber",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                     "0100",
						ProxyNumber:             "1234567",
						ProcessingCode:          "90",
						TransactionAmount:       10,
						CardHolderBillingAmount: 10,
						TransmissionDateTime:    time.Now(),
						SystemTraceAuditNumber:  "",
					},
				},
			},
			expectedErr: &logic.ErrMissingSystemTraceAuditNumber,
		},
		{
			name: "missing MCC",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "",
					},
				},
			},
			expectedErr: &logic.ErrMissingMerchantCategoryCode,
		},
		{
			name: "missing PanEntryMode",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "",
					},
				},
			},
			expectedErr: &logic.ErrMissingPanEntryMode,
		},
		{
			name: "missing NetworkID",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "",
					},
				},
			},
			expectedErr: &logic.ErrMissingNetworkID,
		},
		{
			name: "missing PosDataSF4",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "",
					},
				},
			},
			expectedErr: &logic.ErrMissingPosDataSF4,
		},
		{
			name: "missing PosDataSF5",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "",
					},
				},
			},
			expectedErr: &logic.ErrMissingPosDataSF5,
		},
		{
			name: "missing PosDataSF7",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "",
					},
				},
			},
			expectedErr: &logic.ErrMissingPosDataSF7,
		},
		{
			name: "missing PosDataSF10",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "",
					},
				},
			},
			expectedErr: &logic.ErrMissingPosDataSF10,
		},
		{
			name: "missing AcquirerID",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "1",
						AcquirerID:               "",
					},
				},
			},
			expectedErr: &logic.ErrMissingAcquirerID,
		},
		{
			name: "missing RRN - removed validation, no error expected",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                         "0100",
						ProxyNumber:                 "1234567",
						ProcessingCode:              "90",
						TransactionAmount:           10,
						CardHolderBillingAmount:     10,
						TransmissionDateTime:        time.Now(),
						SystemTraceAuditNumber:      "1234",
						LocalTransactionDateTime:    "**********",
						MerchantCategoryCode:        "1000",
						AcquirerCountryCode:         "100",
						PanEntryMode:                "05",
						NetworkID:                   "MCN",
						POSDataSF4:                  "1",
						POSDataSF5:                  "1",
						POSDataSF7:                  "1",
						POSDataSF10:                 "1",
						AcquirerID:                  "1000",
						RetrievalReferenceNumber:    "",
						AuthorizationID:             "12345",
						CardAcceptorTerminalID:      "1000",
						CardAcceptorMerchantID:      "1000",
						CardAcceptorTermName:        "TEST",
						TransactionCurrencyCode:     "702",
						CardHolderCurrencyCode:      "703",
						TransactionID:               "1000",
						OrgTransactionID:            "",
						ThreeDsAuthTID:              "Y",
						DCCIndicator:                "Y",
						ECOMIndicator:               "Y",
						UCAFIndicator:               "Y",
						ThreeDsValidation:           "Y",
						PANValidation:               "Y",
						ExpiryDateValidation:        "Y",
						PinValidation:               "Y",
						CVVValidation:               "Y",
						CVV2Validation:              "Y",
						EMVValidation:               "Y",
						ReplacementAmount:           0,
						CardHolderReplacementAmount: 0,
						DISecIssData:                "",
						DIFraudScore:                "",
						DIScoreReasonCode:           "",
						MSTransactionTypeID:         "",
						MSTransactionRefData:        "",
					},
				},
			},
			expectedErr: nil,
		},
		{
			name: "missing AuthorizationID",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "1",
						AcquirerID:               "1000",
						RetrievalReferenceNumber: "*********",
						AuthorizationID:          "",
					},
				},
			},
			expectedErr: &logic.ErrMissingAuthorizationID,
		},
		{
			name: "missing CardAcceptorTermName",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "1",
						AcquirerID:               "1000",
						RetrievalReferenceNumber: "*********",
						AuthorizationID:          "12345",
						CardAcceptorTerminalID:   "1000",
						CardAcceptorMerchantID:   "1000",
						CardAcceptorTermName:     "",
					},
				},
			},
			expectedErr: &logic.ErrMissingCardAcceptorTermName,
		},
		{
			name: "missing TransactionCurrencyCode",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "1",
						AcquirerID:               "1000",
						RetrievalReferenceNumber: "*********",
						AuthorizationID:          "12345",
						CardAcceptorTerminalID:   "1000",
						CardAcceptorMerchantID:   "1000",
						CardAcceptorTermName:     "TEST",
						TransactionCurrencyCode:  "",
					},
				},
			},
			expectedErr: &logic.ErrMissingTransactionCurrencyCode,
		},
		{
			name: "missing CardHolderCurrencyCode",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "1",
						AcquirerID:               "1000",
						RetrievalReferenceNumber: "*********",
						AuthorizationID:          "12345",
						CardAcceptorTerminalID:   "1000",
						CardAcceptorMerchantID:   "1000",
						CardAcceptorTermName:     "TEST",
						TransactionCurrencyCode:  "702",
						CardHolderCurrencyCode:   "",
					},
				},
			},
			expectedErr: &logic.ErrMissingCardHolderCurrencyCode,
		},
		{
			name: "missing TransactionID",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "1",
						AcquirerID:               "1000",
						RetrievalReferenceNumber: "*********",
						AuthorizationID:          "12345",
						CardAcceptorTerminalID:   "1000",
						CardAcceptorMerchantID:   "1000",
						CardAcceptorTermName:     "TEST",
						TransactionCurrencyCode:  "702",
						CardHolderCurrencyCode:   "703",
						TransactionID:            "",
					},
				},
			},
			expectedErr: &logic.ErrMissingTransactionID,
		},
		{
			name: "missing PANValidation",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "1",
						AcquirerID:               "1000",
						RetrievalReferenceNumber: "*********",
						AuthorizationID:          "12345",
						CardAcceptorTerminalID:   "1000",
						CardAcceptorMerchantID:   "1000",
						CardAcceptorTermName:     "TEST",
						TransactionCurrencyCode:  "702",
						CardHolderCurrencyCode:   "703",
						TransactionID:            "1000",
						OrgTransactionID:         "1000",
						ThreeDsAuthTID:           "Y",
						DCCIndicator:             "Y",
						ECOMIndicator:            "Y",
						UCAFIndicator:            "Y",
						ThreeDsValidation:        "Y",
						PANValidation:            "",
					},
				},
			},
			expectedErr: &logic.ErrMissingPANValidation,
		},
		{
			name: "missing ExpiryDateValidation",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
						ReqID:       uuid.NewString(),
						ReqDateTime: time.Now().Format("2006-01-02 15:04:05"),
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                      "0100",
						ProxyNumber:              "1234567",
						ProcessingCode:           "90",
						TransactionAmount:        10,
						CardHolderBillingAmount:  10,
						TransmissionDateTime:     time.Now(),
						SystemTraceAuditNumber:   "1234",
						LocalTransactionDateTime: "**********",
						MerchantCategoryCode:     "1000",
						AcquirerCountryCode:      "100",
						PanEntryMode:             "05",
						NetworkID:                "MCN",
						POSDataSF4:               "1",
						POSDataSF5:               "1",
						POSDataSF7:               "1",
						POSDataSF10:              "1",
						AcquirerID:               "1000",
						RetrievalReferenceNumber: "*********",
						AuthorizationID:          "12345",
						CardAcceptorTerminalID:   "1000",
						CardAcceptorMerchantID:   "1000",
						CardAcceptorTermName:     "TEST",
						TransactionCurrencyCode:  "702",
						CardHolderCurrencyCode:   "703",
						TransactionID:            "1000",
						OrgTransactionID:         "1000",
						ThreeDsAuthTID:           "Y",
						DCCIndicator:             "Y",
						ECOMIndicator:            "Y",
						UCAFIndicator:            "Y",
						ThreeDsValidation:        "Y",
						PANValidation:            "Y",
						ExpiryDateValidation:     "",
					},
				},
			},
			expectedErr: &logic.ErrMissingExpiryDateValidation,
		},
		{
			name: "missing acquirerCountryCode should not be error",
			req: &api.TransactionAPIRequest{
				TransactionRequest: &api.TransactionRequest{
					AppHeader: &api.AppHeader{
						ReqID:       uuid.NewString(),
						PartID:      logic.TransactionPartID,
						ServiceName: dto.TxnAuth,
					},
					TransactionInfo: &api.TransactionInfo{
						Mti:                         "0100",
						ProxyNumber:                 "1234567",
						ProcessingCode:              "001000",
						TransactionAmount:           10,
						CardHolderBillingAmount:     10,
						TransmissionDateTime:        time.Now(),
						SystemTraceAuditNumber:      "1234",
						LocalTransactionDateTime:    "**********",
						MerchantCategoryCode:        "1000",
						AcquirerCountryCode:         "",
						PanEntryMode:                "810",
						NetworkID:                   "MCN",
						POSDataSF4:                  "1",
						POSDataSF5:                  "1",
						POSDataSF7:                  "1",
						POSDataSF10:                 "1",
						AcquirerID:                  "1000",
						RetrievalReferenceNumber:    "*********",
						AuthorizationID:             "12345",
						CardAcceptorTerminalID:      "1000",
						CardAcceptorMerchantID:      "1000",
						CardAcceptorTermName:        "TEST",
						TransactionCurrencyCode:     "702",
						CardHolderCurrencyCode:      "703",
						TransactionID:               "1000",
						OrgTransactionID:            "",
						ThreeDsAuthTID:              "Y",
						DCCIndicator:                "Y",
						ECOMIndicator:               "Y",
						UCAFIndicator:               "Y",
						ThreeDsValidation:           "Y",
						PANValidation:               "Y",
						ExpiryDateValidation:        "Y",
						PinValidation:               "Y",
						CVVValidation:               "Y",
						CVV2Validation:              "Y",
						EMVValidation:               "Y",
						ReplacementAmount:           0,
						CardHolderReplacementAmount: 0,
						DISecIssData:                "",
						DIFraudScore:                "",
						DIScoreReasonCode:           "",
						MSTransactionTypeID:         "",
						MSTransactionRefData:        "",
					},
				},
			},
			expectedErr: nil,
		},
	}
	cfg := &config.CountryConfig{
		AppHeaderConfig: config.AppHeaderConfig{
			ParticipantIDs: []string{logic.TransactionPartID},
		},
	}
	for _, test := range tests {
		tt := test
		mockCardTransaction := &storage.MockICardTransactionDAO{}
		storage.CardTransactionD = mockCardTransaction
		mockCardTransaction.On("Find", mock.Anything, mock.Anything).Return(nil, nil)
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expectedErr, authMandatoryReqValidator(context.Background(), tt.req, cfg), tt.name)
		})
	}
}

func Test_authReqValidator(t *testing.T) {
	tests := []struct {
		desc string
		req  *api.TransactionRequest
		resp *logic.Transaction
		err  *logic.AdapterError
	}{
		// ATM transaction Test cases
		{
			desc: "happy path - ATM Withdrawal (Cross Border)",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0200",
					ProxyNumber:              "1234567",
					ProcessingCode:           "01",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6011",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "05",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "ATM",
				TransactionSubCategory: "WITHDRAWAL",
				CurrencyConvType:       "DCC",
				TransactionType:        "DIRECT_DEBIT",
			},
		},
		{
			desc: "happy path - ATM Withdrawal (Cross Border) Local Currency",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0200",
					ProxyNumber:              "1234567",
					ProcessingCode:           "01",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6011",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "05",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "something something MYS",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "ATM",
				TransactionSubCategory: "WITHDRAWAL",
				CurrencyConvType:       "LOCAL",
				TransactionType:        "DIRECT_DEBIT",
			},
		},
		{
			desc: "happy path - ATM Balance Inquiry (Cross Border)",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0200",
					ProxyNumber:              "1234567",
					ProcessingCode:           "30",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6011",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "90",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "ATM",
				TransactionSubCategory: "BALANCEENQUIRY",
				CurrencyConvType:       "DCC",
				TransactionType:        "BALANCE_ENQUIRY",
			},
		},
		{
			desc: "happy path - ATM Fallback",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0200",
					ProxyNumber:              "1234567",
					ProcessingCode:           "30",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6011",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "80",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "ATM",
				TransactionSubCategory: "FALLBACK",
				CurrencyConvType:       "DCC",
				TransactionType:        "DIRECT_DEBIT",
			},
		},
		{
			desc: "happy path - ATM Withdrawal - Zero dollar",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0200",
					ProxyNumber:              "1234567",
					ProcessingCode:           "01",
					TransactionAmount:        10,
					CardHolderBillingAmount:  0,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6011",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "05",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "702",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: nil,
			err:  &logic.AdapterError{Code: "1209", Message: "Missing/Invalid card holder billing amount"},
		},
		// POS transaction Test cases
		{
			desc: "happy path - Pos Purchase (Swipe)",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "ANY MCC CODE",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "90",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "SWIPE",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			desc: "happy path - Pos Purchase (Swipe) Local currency",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "ANY MCC CODE",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "90",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "something something MYS",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "SWIPE",
				CurrencyConvType:       "LOCAL",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			desc: "happy path - Pos Purchase (Manual Entry)",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "ANY MCC CODE",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "01",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "MANUAL",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			desc: "happy path - Pos Purchase (AFD)",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "5542",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "05",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "AFD",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			desc: "happy path - Pos Purchase (Transit)",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "4111",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "90",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "TRANSIT",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			desc: "happy path - POS Fallback",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "some mcc",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "80",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "FALLBACK",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			//TODO: To clarify if card contactless or device contactless
			desc: "happy path - POS Contactless",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "some mcc",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "07",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "CONTACTLESS",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			desc: "happy path - Pos Purchase (Swipe) - Zero dollar",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  0,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "ANY MCC CODE",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "90",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "702",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: nil,
			err:  &logic.AdapterError{Code: "1209", Message: "Missing/Invalid card holder billing amount"},
		},
		// Ecommerce test cases
		{
			desc: "happy path - Ecommerce",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "199281994",
					ProcessingCode:           "001000",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "245165",
					LocalTransactionDateTime: "2022-09-02T10:42:37Z",
					MerchantCategoryCode:     "6655",
					AcquirerCountryCode:      "123",
					PanEntryMode:             "81",
					NetworkID:                "MCN",
					POSDataSF4:               "5",
					POSDataSF5:               "1",
					POSDataSF7:               "0",
					POSDataSF10:              "0",
					AcquirerID:               "35132237243",
					RetrievalReferenceNumber: "577570098959",
					AuthorizationID:          "294105",
					CardAcceptorTerminalID:   "X3p8b9rmjcF6ZvjE",
					CardAcceptorMerchantID:   "005tstc42eWJ7srY",
					CardAcceptorTermName:     "1588 Brigid Shores MYS",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "702",
					TransactionID:            "xG4sgdlVNb8WQQ4",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "",
					DCCIndicator:             "",
					ECOMIndicator:            "21",
					UCAFIndicator:            "1",
					ThreeDsValidation:        "N",
					PANValidation:            "P",
					ExpiryDateValidation:     "P",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "ECOM",
				TransactionSubCategory: "DEFAULT",
				CurrencyConvType:       "LOCAL",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			desc: "happy path - Recurring / Stand In Transactions",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "some mcc",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "01",
					NetworkID:                "MCN",
					POSDataSF4:               "4",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "MOTO",
				TransactionSubCategory: "RECURRING",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		{
			desc: "happy path - Ecommerce - Zero dollar",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "199281994",
					ProcessingCode:           "001000",
					TransactionAmount:        10,
					CardHolderBillingAmount:  0,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "245165",
					LocalTransactionDateTime: "2022-09-02T10:42:37Z",
					MerchantCategoryCode:     "6655",
					AcquirerCountryCode:      "123",
					PanEntryMode:             "81",
					NetworkID:                "MCN",
					POSDataSF4:               "5",
					POSDataSF5:               "1",
					POSDataSF7:               "0",
					POSDataSF10:              "0",
					AcquirerID:               "35132237243",
					RetrievalReferenceNumber: "577570098959",
					AuthorizationID:          "294105",
					CardAcceptorTerminalID:   "X3p8b9rmjcF6ZvjE",
					CardAcceptorMerchantID:   "005tstc42eWJ7srY",
					CardAcceptorTermName:     "1588 Brigid Shores SGP",
					TransactionCurrencyCode:  "702",
					CardHolderCurrencyCode:   "702",
					TransactionID:            "xG4sgdlVNb8WQQ4",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "",
					DCCIndicator:             "",
					ECOMIndicator:            "21",
					UCAFIndicator:            "1",
					ThreeDsValidation:        "N",
					PANValidation:            "P",
					ExpiryDateValidation:     "P",
				},
				DeclineInfo: nil,
			},
			resp: nil,
			err:  &logic.AdapterError{Code: "1209", Message: "Missing/Invalid card holder billing amount"},
		},
		// Mail Or Telephone Order test cases
		{
			desc: "happy path - MOTO*",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "some mcc",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "01",
					NetworkID:                "MCN",
					POSDataSF4:               "3",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "MOTO",
				TransactionSubCategory: "DEFAULT",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		// Credentials On File test cases (COF)
		{
			desc: "happy path - Credentials On File (COF)",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "some mcc",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "10",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "COF",
				TransactionSubCategory: "DEFAULT",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
		},
		// MoneySend test cases (Master Card)
		{
			desc: "happy path - Master Card MoneySend",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "28",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6536",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "81",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
					MSTransactionTypeID:      "C07",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "MONEYSEND",
				TransactionSubCategory: "DEFAULT",
				CurrencyConvType:       "DCC",
				TransactionType:        "DIRECT_CREDIT",
			},
		},
		// Account Status Inquiry test cases
		{
			desc: "happy path - Account Status Inquiry",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "20",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6536",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "09",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "8",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "STATUSENQUIRY",
				TransactionSubCategory: "STATUSENQUIRY",
				CurrencyConvType:       "DCC",
				TransactionType:        "STATUS_ENQUIRY",
			},
		},
		{
			desc: "Invalid MTI",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti: "0300",
				},
				DeclineInfo: nil,
			},
			resp: nil,
			err:  &logic.ErrInvalidMTI,
		},
		{
			desc: "panEntryMode too short",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0200",
					ProxyNumber:              "1234567",
					ProcessingCode:           "01",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6011",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "1",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "702",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: nil,
			err:  &logic.ErrInvalidPANEntryMode,
		},
		{
			desc: "invalid panEntryMode",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0200",
					ProxyNumber:              "1234567",
					ProcessingCode:           "01",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6011",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "99",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "702",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: nil,
			err:  &logic.ErrInvalidPANEntryMode,
		},
		{
			desc: "incremental auth decline",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "00",
					TransactionAmount:        10,
					CardHolderBillingAmount:  10,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "ANY MCC CODE",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "01",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "1",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "702",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "some_value",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: nil,
			err:  &logic.ErrNotSupportedTransaction,
		},
		{
			desc: "happy path - Account Status Inquiry - zero dollar",
			req: &api.TransactionRequest{
				AppHeader: nil,
				TransactionInfo: &api.TransactionInfo{
					Mti:                      "0100",
					ProxyNumber:              "1234567",
					ProcessingCode:           "20",
					TransactionAmount:        10,
					CardHolderBillingAmount:  0,
					TransmissionDateTime:     time.Now(),
					SystemTraceAuditNumber:   "1234",
					LocalTransactionDateTime: "**********",
					MerchantCategoryCode:     "6536",
					AcquirerCountryCode:      "100",
					PanEntryMode:             "09",
					NetworkID:                "MCN",
					POSDataSF4:               "1",
					POSDataSF5:               "1",
					POSDataSF7:               "8",
					POSDataSF10:              "1",
					AcquirerID:               "1000",
					RetrievalReferenceNumber: "*********",
					AuthorizationID:          "12345",
					CardAcceptorTerminalID:   "1000",
					CardAcceptorMerchantID:   "1000",
					CardAcceptorTermName:     "TEST",
					TransactionCurrencyCode:  "458",
					CardHolderCurrencyCode:   "703",
					TransactionID:            "1000",
					OrgTransactionID:         "",
					ThreeDsAuthTID:           "Y",
					DCCIndicator:             "Y",
					ECOMIndicator:            "Y",
					UCAFIndicator:            "Y",
					ThreeDsValidation:        "Y",
					PANValidation:            "Y",
					ExpiryDateValidation:     "",
				},
				DeclineInfo: nil,
			},
			resp: &logic.Transaction{
				TransactionCategory:    "STATUSENQUIRY",
				TransactionSubCategory: "STATUSENQUIRY",
				CurrencyConvType:       "DCC",
				TransactionType:        "STATUS_ENQUIRY",
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.desc, func(t *testing.T) {
			mockStorage := &storage.MockIUserCardMappingDAO{}
			mockStorage.On("Find", mock.Anything, mock.Anything).Return(nil, nil).Once()
			storage.UserCardMappingD = mockStorage
			ctx := context.Background()
			resp, err := authReqValidator(ctx, tt.req, []string{"C07"}, &config.CountryConfig{
				CurrencyCode:      "MYR",
				CurrencyNum:       "458",
				CountryCodeAlpha3: "MYS",
				CountryName:       "Malaysia",
				AppHeaderConfig: config.AppHeaderConfig{
					ParticipantID: "407",
					ChannelID:     "DBMYCHN",
					UserID:        "SYEWALE",
				},
			})
			assert.Equal(t, tt.resp, resp)
			if tt.err != nil {
				assert.Equal(t, tt.err, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func Test_txnParamsValidator(t *testing.T) {
	tests := []struct {
		desc string
		req  *logic.Transaction
		resp *logic.AdapterError
	}{
		{
			desc: "Happy path - txn allowed",
			req: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "SWIPE",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
			resp: nil,
		},
		{
			desc: "ATM Fallback - rejected",
			req: &logic.Transaction{
				TransactionCategory:    "ATM",
				TransactionSubCategory: "FALLBACK",
				CurrencyConvType:       "DCC",
				TransactionType:        "DIRECT_DEBIT",
			},
			resp: &logic.ErrRejectedTransaction,
		},
		{
			desc: "POS Fallback - rejected",
			req: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "FALLBACK",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
			resp: &logic.ErrRejectedTransaction,
		},
		{
			desc: "POS Transit - rejected",
			req: &logic.Transaction{
				TransactionCategory:    "POS",
				TransactionSubCategory: "FALLBACK",
				CurrencyConvType:       "DCC",
				TransactionType:        "HOLD_AND_POST",
			},
			resp: &logic.ErrRejectedTransaction,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.desc, func(t *testing.T) {
			assert.Equalf(t, tt.resp, txnParamsValidator(context.Background(), tt.req), "txnParamsValidator(%v)", tt.req)
		})
	}
}
