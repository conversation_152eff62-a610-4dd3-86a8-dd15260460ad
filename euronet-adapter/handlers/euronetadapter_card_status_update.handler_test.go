package handlers

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	statusUpdate "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/card/update_card_status"
)

func TestEuronetAdapterService_CardStatusUpdate(t *testing.T) {
	scenarios := []struct {
		errExecute, expectedErr error
	}{
		{
			errExecute:  errors.New("simulate error"),
			expectedErr: errors.New("simulate error"),
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		mockClient := &statusUpdate.MockWorkflow{}
		mockClient.On("ExecuteCardStatusUpdateWorkflow", mock.Anything, mock.Anything).
			Return(&api.UpdateCardStatusResponse{}, testcase.errExecute)
		testService := &EuronetAdapterService{CardStatusUpdateWorkflow: mockClient}

		_, err := testService.CardStatusUpdate(context.Background(), &api.UpdateCardStatusRequest{})
		assert.Equal(t, scenario.expectedErr, err, description)
	}
}
