package handlers

import (
	context "context"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/constants"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	api "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/notification"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
)

// Advice api for mastercard txn, Euronet should send the rejected txn such as failed pin verification txn info to DBMY through this api.
func (e *EuronetAdapterService) Advice(ctx context.Context, req *api.TransactionAPIRequest) (*api.TransactionAPIResponse, error) {
	ctx = addTxnHeadersToSlogCtx(ctx, req.TransactionRequest.AppHeader)
	if err := adviceRequestValidator(ctx, req, e.CountryConfig); err != nil {
		slog.FromContext(ctx).Error(mcAdviceEvTag, "invalid mc advice request",
			slog.CustomTag(constants.CodeTag, err.Code), slog.CustomTag(constants.MessageTag, err.Message))
		resp := convertResponse(req, err)
		e.publishTransactionRecord(ctx, req, resp)

		return resp, nil
	}
	txnInfo := req.TransactionRequest.TransactionInfo
	notificationInfo := &notification.RequestNotification{
		NetworkID:               txnInfo.NetworkID,
		ProcessingCode:          txnInfo.ProcessingCode,
		DeclineCode:             req.TransactionRequest.DeclineInfo.DeclineCode,
		ProxyNumber:             txnInfo.ProxyNumber,
		TransactionAmount:       txnInfo.TransactionAmount,
		CardHolderBillingAmount: txnInfo.CardHolderBillingAmount,
		CardAcceptorTermName:    txnInfo.CardAcceptorTermName,
	}
	e.PigeonService.SendNotification(ctx, notificationInfo)
	adviceResp, err := e.AdviceClient.SaveAdvice(ctx, req.TransactionRequest)
	if err != nil {
		slog.FromContext(ctx).Error(mcAdviceEvTag, "mc SaveAdvice failed", slog.Error(err))
		resp := convertResponse(req, &logic.ErrSystemMalfunction)
		e.publishTransactionRecord(ctx, req, resp)
		return resp, nil
	}
	resp := &api.TransactionAPIResponse{TransactionResponse: adviceResp}
	e.publishTransactionRecord(ctx, req, resp)
	return resp, nil
}

func adviceRequestValidator(ctx context.Context, req *api.TransactionAPIRequest, countryConfig *config.CountryConfig) *logic.AdapterError {
	if req == nil || req.TransactionRequest == nil {
		return &logic.ErrInvalidRequest
	}

	if err := txnAppHeadersValidation(ctx, req.TransactionRequest.AppHeader, dto.TxnAdvice, countryConfig); err != nil {
		return err
	}
	// advice api should only accept inquiry from mastercard network
	if req.TransactionRequest.TransactionInfo.NetworkID != constant.NETWORK_MASTERCARD {
		return &logic.ErrMissingNetworkID
	}
	return nil
}
