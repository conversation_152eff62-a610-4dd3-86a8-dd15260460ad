package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/digicard/common/recorder"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
)

// CardStatusUpdate ...
func (e *EuronetAdapterService) CardStatusUpdate(ctx context.Context, req *api.UpdateCardStatusRequest) (*api.UpdateCardStatusResponse, error) {
	recorder.RecordApiFuncName(ctx, common.CardStatusUpdate)
	updateCardStatusWorkflow := e.CardStatusUpdateWorkflow
	res, err := updateCardStatusWorkflow.ExecuteCardStatusUpdateWorkflow(ctx, req)
	if res != nil {
		recorder.RecordResponseCode(ctx, res.StatusReason)
	}

	return res, err
}
