package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ResumeFailedProcessing ...
func (e *EuronetAdapterService) ResumeFailedProcessing(ctx context.Context, req *api.ResumeFailedProcessingRequest) (*api.ResumeFailedProcessingResponse, error) {
	if err := resumeFailedProcessingValidator(req); err != nil {
		return nil, err
	}
	return e.TxnDetailsClient.ResumeFailedProcessing(ctx, req)
}

func resumeFailedProcessingValidator(req *api.ResumeFailedProcessingRequest) *servus.ServiceError {
	err := api.BadRequest
	if !constant.ValidResumeProcessingTypes[req.ProcessingType] {
		return &servus.ServiceError{
			Code:     string(err),
			Message:  "processingType is not supported",
			HTTPCode: err.HTTPStatusCode(),
		}
	}
	if req.ProcessingType == string(constant.Clearing) && (req.InternalTxnID == "" && (req.ApprovalCode == "" || req.AcquirerRefData == "")) {
		return &servus.ServiceError{
			Code:     string(err),
			Message:  "either internalTxnID or (approvalCode & acquirerRefData) is required for resuming clearing",
			HTTPCode: err.HTTPStatusCode(),
		}
	}
	if req.ProcessingType == string(constant.Adjustment2) && req.InternalTxnID == "" {
		return &servus.ServiceError{
			Code:     string(err),
			Message:  "internalTxnID is required for resuming auth expiry",
			HTTPCode: err.HTTPStatusCode(),
		}
	}
	return nil
}
