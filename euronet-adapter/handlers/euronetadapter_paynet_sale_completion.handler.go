package handlers

import (
	context "context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/recorder"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	utilContext "gitlab.com/gx-regional/dbmy/digicard/common/utils/context"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common/mapper"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/constants"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
)

const logCapture = "paynet_capture_transaction"

// PaynetSaleCompletion For Sale Completion Transactions
func (e *EuronetAdapterService) PaynetSaleCompletion(ctx context.Context, req *api.PaynetSaleCompletionRequest) (*api.PaynetSaleCompletionResponse, error) {
	recorder.RecordCommon(ctx, string(constant.Paynet), common.PaynetSaleCompletion)
	ctx = addTxnHeadersToSlogCtx(ctx, req.TransactionSaleCompletionRequest.AppHeader)
	if err := validatePaynetSaleCompletionMandatoryReqValidator(ctx, req, e.CountryConfig); err != nil {
		slog.FromContext(ctx).Error(pnSalesCompletionEvTag, "invalid pn sales completion request",
			slog.CustomTag(constants.CodeTag, err.Code), slog.CustomTag(constants.MessageTag, err.Message))

		resp := mapper.PaynetSaleCompletionConvertResponse(req, err)
		e.publishTransactionRecord(ctx, req, resp)

		return resp, nil
	}

	txnParams, err := paynetSaleCompletionReqValidator(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(pnSalesCompletionEvTag, "paynetSaleCompletionReqValidator error",
			slog.CustomTag(constants.CodeTag, err.Code), slog.CustomTag(constants.MessageTag, err.Message))
		recorder.RecordResponseCode(ctx, err.Code)
		resp := mapper.PaynetSaleCompletionConvertResponse(req, err)
		e.publishTransactionRecord(ctx, req, resp)

		return resp, nil
	}
	recorder.RecordTransactionCategoryValue(ctx, string(txnParams.TransactionCategory))
	recorder.RecordTransactionSubCategoryValue(ctx, string(txnParams.TransactionSubCategory))

	err = txnParamsValidator(ctx, txnParams)
	if err != nil {
		slog.FromContext(ctx).Error(pnSalesCompletionEvTag, "txnParamsValidator error",
			slog.CustomTag(constants.CodeTag, err.Code), slog.CustomTag(constants.MessageTag, err.Message))
		recorder.RecordResponseCode(ctx, err.Code)
		resp := mapper.PaynetSaleCompletionConvertResponse(req, err)
		e.publishTransactionRecord(ctx, req, resp)

		return resp, nil
	}

	// this timeout will be used to reverse the transaction if the bank host can't authorize the transaction within the paynet allowed timeout
	ctx, cancel := context.WithTimeout(ctx, getTimeoutSetting(dto.TxnSaleCompletion, constant.NETWORK_MYDEBIT, e.TransactionTimeoutInMiliseconds))
	defer cancel()

	var saleCompletionResp *api.PaynetSaleCompletionResponse_TransactionSaleCompletionResponse
	errChan := gconcurrent.Go(utilContext.NewCtxWithSpan(ctx), logCapture, func(c context.Context) error {
		resp, errResp := e.PaynetAuthorizeWorkflow.ExecuteSaleCompletionTransactionWorkflow(ctx, req.TransactionSaleCompletionRequest)
		if errResp != nil {
			slog.FromContext(ctx).Error(pnSalesCompletionEvTag, "ExecuteSaleCompletionTransactionWorkflow failed",
				slog.Error(errResp))
			return errResp
		}
		saleCompletionResp = resp
		return nil
	})

	select {
	case <-ctx.Done():
		slog.FromContext(ctx).Error(pnSalesCompletionEvTag, "ExecuteSaleCompletionTransactionWorkflow timeout")
		recorder.RecordResponseCode(ctx, logic.ErrAuthTimeout.Code)
		errResp := mapper.PaynetSaleCompletionConvertResponse(req, &logic.ErrAuthTimeout)
		e.publishTransactionRecord(ctx, req, errResp)
		return errResp, nil
	case apiErr := <-errChan:
		if apiErr != nil {
			slog.FromContext(ctx).Error(pnSalesCompletionEvTag, "error executing sale completion workflow", slog.Error(apiErr))
			if apiErr.(servus.ServiceError).Code == logic.ErrDuplicateRRN.Code {
				recorder.RecordResponseCode(ctx, logic.ErrInvalidRRN.Code)
				errResp := mapper.PaynetSaleCompletionConvertResponse(req, &logic.ErrInvalidRRN)
				e.publishTransactionRecord(ctx, req, errResp)
				return errResp, nil
			}
			errResp := mapper.PaynetSaleCompletionConvertResponse(req, &logic.ErrSystemMalfunction)
			e.publishTransactionRecord(ctx, req, errResp)
			return errResp, nil
		}

		recorder.RecordResponseCode(ctx, saleCompletionResp.Response.ResponseCode)
		resp := &api.PaynetSaleCompletionResponse{TransactionSaleCompletionResponse: saleCompletionResp}
		e.publishTransactionRecord(ctx, req, resp)
		return resp, nil
	}
}

func validatePaynetSaleCompletionMandatoryReqValidator(
	ctx context.Context,
	req *api.PaynetSaleCompletionRequest,
	countryConfig *config.CountryConfig,
) *logic.AdapterError {
	if req == nil || req.TransactionSaleCompletionRequest == nil {
		return &logic.ErrInvalidRequest
	}
	if err := txnAppHeadersValidation(ctx, req.TransactionSaleCompletionRequest.AppHeader, dto.TxnSaleCompletion, countryConfig); err != nil {
		return err
	}

	info := req.TransactionSaleCompletionRequest.TransactionInfo
	if info == nil {
		return &logic.ErrInvalidRequest
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.RRNTag, info.RetrievalReferenceNumber),
		slog.CustomTag(logic.ProxyNumberTag, info.ProxyNumber),
		slog.CustomTag(logic.OrigTransactionIDTag, info.OrigTransactionID))
	slog.FromContext(ctx).Info(pnSalesCompletionEvTag, "received sales completion` request")

	strValidators := []stringValidator{
		{field: info.Mti, max: 4, error: &logic.ErrInvalidPaynetMti},
		{field: info.ProxyNumber, max: 20, error: &logic.ErrInvalidPaynetProxyNumber},
		{field: info.ProcessingCode, max: 6, error: &logic.ErrInvalidPaynetProcessingCode},
		{field: info.SystemTraceAuditNumber, max: 6, error: &logic.ErrInvalidPaynetSystemTraceAuditNumber},
		{field: info.LocalTransactionDateTime, max: 10, error: &logic.ErrInvalidPaynetLocalTransactionDateTime},
		{field: info.MerchantCategoryCode, max: 4, error: &logic.ErrInvalidPaynetMerchantCategoryCode},
		{field: info.PosEntryMode, max: 3, error: &logic.ErrInvalidPaynetPosEntryMode},
		{field: info.PosConditionCode, max: 3, error: &logic.ErrInvalidPaynetPosConditionCode},
		{field: info.NetworkID, max: 3, error: &logic.ErrInvalidPaynetNetworkID},
		{field: info.AcquirerID, max: 11, error: &logic.ErrInvalidPaynetAcquirerID},
		{field: info.OrigTransactionID, max: 32, error: &logic.ErrInvalidPaynetOrigTransactionID},
		{field: info.RetrievalReferenceNumber, max: 12, error: &logic.ErrInvalidPaynetRetrievalReferenceNumber},
		{field: info.AuthorizationID, max: 6, error: &logic.ErrInvalidPaynetAuthorizationID},
		{field: info.CardAcceptorTermName, max: 40, error: &logic.ErrInvalidPaynetCardAcceptorTermName},
		{field: info.RetailerID, max: 20, error: &logic.ErrInvalidPaynetRetailerID},
		{field: info.TransactionCurrencyCode, max: 3, error: &logic.ErrInvalidPaynetTransactionCurrencyCode},
		{field: info.CardHolderCurrencyCode, max: 3, error: &logic.ErrInvalidPaynetCardHolderCurrencyCode},
		{field: info.PANValidation, max: 1, error: &logic.ErrInvalidPaynetPANValidation},
		{field: info.ExpiryDateValidation, max: 1, error: &logic.ErrInvalidPaynetExpiryDateValidation},
	}
	amountValidators := []integerValidator{
		{field: info.TransactionAmount, error: &logic.ErrInvalidPaynetTransactionAmount},
	}
	if err := bodyValidation(strValidators, amountValidators); err != nil {
		return err
	}

	// paynet sale completion api should only accept enquiry from MYDEBIT network
	if info.NetworkID != constant.NETWORK_MYDEBIT {
		return &logic.ErrMissingNetworkID
	}
	return nil
}

func paynetSaleCompletionReqValidator(
	ctx context.Context,
	req *api.PaynetSaleCompletionRequest,
) (*logic.Transaction, *logic.AdapterError) {
	info := req.TransactionSaleCompletionRequest.TransactionInfo
	if !dto.ValidateMTI(info.Mti, dto.SaleCompletion) {
		slog.FromContext(ctx).Warn(pnSalesCompletionEvTag, fmt.Sprintf("MTI is invalid for txnType: %s", dto.SaleCompletion))
		return nil, &logic.ErrInvalidPaynetMti
	}
	if err := validateCardFromProxyNumber(ctx, info.ProxyNumber, pnSalesCompletionEvTag); err != nil {
		return nil, err
	}

	transactionParams, err := logic.DetectPaynetTransactionCategory(
		info.Mti,
		info.ProcessingCode,
		info.PosConditionCode,
		info.PosEntryMode,
	)

	if err != nil {
		return nil, err
	}

	return transactionParams, nil
}
