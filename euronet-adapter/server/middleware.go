package server

import (
	"context"

	"gitlab.com/gx-regional/dbmy/digicard/common/recorder"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// WithCustomMonitoring middleware
func WithCustomMonitoring(statsContextKey string) servus.MiddlewareFunc {
	return func(h servus.HandlerFunc) servus.HandlerFunc {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			ctx = recorder.BindRecorder(ctx, statsContextKey)
			resp, err := h(ctx, req)

			recorder.PublishStats(ctx, internalStatsD)
			return resp, err
		}
	}
}
