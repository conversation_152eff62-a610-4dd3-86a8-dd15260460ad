package worker

import (
	"context"
	"errors"
	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transaction/authorize"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/cache"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
)

func TestReleaseAuthMastercard(t *testing.T) {
	errDummy := errors.New("simulate error")
	mockRedis := &mocks.Client{}
	mockLock := &mocks.Lock{}
	mockWf := &authorize.MockWorkflow{}
	mockCardTransactionDAO := &storage.MockICardTransactionDAO{}
	scenarios := []struct {
		errLock     error
		expectedErr error
		errExecute  error
		errBlackout bool
		mockFunc    func()
		description string
	}{
		{
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
					mockLock, errDummy).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Once()
			},
			description: "failed to get lock, skipping",
		},
		{
			expectedErr: errDummy,
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
					mockLock, nil).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Once()
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, errDummy).Once()
			},
			description: "failed to find card-transactions",
		},
		{
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
					mockLock, nil).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Once()
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{}, nil).Times(6) //will be called 6 times because 30 days / 5 days window
			},
			description: "empty result must be fine for func",
		},
		{
			expectedErr: data.ErrNoData,
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
					mockLock, nil).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Once()
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Times(6) //will be called 6 times because 30 days / 5 days window
			},
			description: "ErrNoData will continue to find over all windows but return err",
		},
		{
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
					mockLock, nil).Once()
				mockWf.On("Execute", mock.Anything, mock.Anything).Return(errDummy).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Once()
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{
						{
							RequestID: "dummy",
						},
					}, nil).Times(6) //will be called 6 times because 30 days / 5 days window
			},
			description: "workflow execute failed for one case must be fine for func",
		},
	}

	for _, scenario := range scenarios {
		testcase := scenario
		description := testcase.description
		cache.RedisClient = mockRedis

		t.Run(description, func(t *testing.T) {
			workerConfig := []config.ReleaseMastercardAuthCfg{
				{
					Name: "test",
					ConditionKeys: []string{
						"Status",
					},
					Conditions: []string{
						"EqualTo",
					},
					ConditionValues: [][]string{
						{
							"AUTHORISED",
						},
					},
					ExpiryField:           "CreatedAt",
					ExpiryDays:            30,
					ExpiryBackTrackDays:   30,
					ExpiryQueryWindowDays: 5,
					CronExpression:        []string{},
				},
				{
					Name: "test 2",
					ConditionKeys: []string{
						"Status",
					},
					Conditions: []string{
						"EqualTo",
					},
					ConditionValues: [][]string{
						{
							"AUTHORISED",
						},
					},
					ExpiryField:           "CreatedAt",
					ExpiryDays:            30,
					ExpiryBackTrackDays:   30,
					ExpiryQueryWindowDays: 5,
					CronExpression:        []string{},
				},
			}

			w := ReleaseMastercardAuthWorker{
				ReleaseMastercardAuthCfg: workerConfig,
			}
			testcase.mockFunc()
			storage.CardTransactionD = mockCardTransactionDAO

			ctx := context.Background()
			err := w.executor(ctx, workerConfig[0])

			if scenario.expectedErr != nil {
				assert.Error(t, err, description)
				assert.Equal(t, testcase.expectedErr, err, description)
			} else {
				assert.NoError(t, err, description)
			}
		})
	}
}
