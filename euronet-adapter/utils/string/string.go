package stringUtil

import (
	"crypto/md5"
)

// IsStringWithinThreshold Determines if a given value is in the group based on the percentage threshold.
func IsStringWithinThreshold(value string, threshold int) bool {
	if threshold >= 100 {
		return true
	}
	// Generate MD5 hash of the UUID
	hash := md5.Sum([]byte(value))

	// Assuming Percentage value is 0-99, which this value will be used as the threshold of the below formula
	// Example: Check if the first byte is less than 77 (which is approximately 30% of 256)
	// If true, assign to Group 1, else Group 2
	return hash[0] < byte(float64(256)*float64(threshold)/float64(100))
}
