// Code generated by mockery v2.43.2. DO NOT EDIT.

package data

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	storage "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
)

// mockCardTransactionFilter is an autogenerated mock type for the ICanFilterCardTransaction type
type mockCardTransactionFilter struct {
	mock.Mock
}

// Filter provides a mock function with given fields: ctx, logTag, input
func (_m *mockCardTransactionFilter) Filter(ctx context.Context, logTag string, input []*storage.CardTransaction) ([]*storage.CardTransaction, error) {
	ret := _m.Called(ctx, logTag, input)

	if len(ret) == 0 {
		panic("no return value specified for Filter")
	}

	var r0 []*storage.CardTransaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []*storage.CardTransaction) ([]*storage.CardTransaction, error)); ok {
		return rf(ctx, logTag, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, []*storage.CardTransaction) []*storage.CardTransaction); ok {
		r0 = rf(ctx, logTag, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*storage.CardTransaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, []*storage.CardTransaction) error); ok {
		r1 = rf(ctx, logTag, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// newMockCardTransactionFilter creates a new instance of mockCardTransactionFilter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newMockCardTransactionFilter(t interface {
	mock.TestingT
	Cleanup(func())
}) *mockCardTransactionFilter {
	mock := &mockCardTransactionFilter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
