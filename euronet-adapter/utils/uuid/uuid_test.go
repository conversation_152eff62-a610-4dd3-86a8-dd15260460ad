package uuid

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestStringToUUID(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Empty string",
			input:    "",
			expected: "14cdb9b4de013faaaff565bc2f771745",
		},
		{
			name:     "Simple string",
			input:    "test",
			expected: "1cf935508eb43c32a229826cf8c1be59",
		},
		{
			name:     "Complex string",
			input:    "more complex string with spaces, and punctuation.",
			expected: "2482cc378f1d3c158fc9cbccd5b37c35",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := StringToUUID(tt.input)
			assert.Equal(t, tt.expected, result, "StringToUUID(%s) = %s; want %s", tt.input, result, tt.expected)
			assert.Len(t, result, 32, "Result should always be 32 characters long")
			assert.NotContains(t, result, "-", "Result should not contain any hyphens")
		})
	}
}
