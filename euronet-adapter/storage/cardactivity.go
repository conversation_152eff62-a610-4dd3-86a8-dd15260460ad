package storage

import (
	"encoding/json"
	"time"
)

// CardActivity ...
type CardActivity struct {
	ID             uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
	RequestID      string          `sql-col:"request_id"`
	InternalCardID string          `sql-col:"internal_card_id"`
	ServiceName    string          `sql-col:"service_name"`
	RequestBody    json.RawMessage `sql-col:"request_body" sql-where:"false" data-type:"json"`
	ResponseBody   json.RawMessage `sql-col:"response_body" sql-where:"false" data-type:"json"`
	ResponseCode   string          `sql-col:"response_code"`
	Status         string          `sql-col:"status"`
	CreatedAt      time.Time       `sql-col:"created_at"`
	UpdatedAt      time.Time       `sql-col:"updated_at"`
}

//const (
//	logTagCardActivity = "storage.cardactivity"
//	ttlCardActivity    = 24 * 3600 // 1 day
//)
