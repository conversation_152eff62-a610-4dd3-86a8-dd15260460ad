package transactiondetails

import (
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
)

func convertClearingFailureStorageToAPI(txn *storage.ClearingFailure) *api.ClearingFailure {
	return &api.ClearingFailure{
		Type:                     txn.Type,
		ProxyNumber:              txn.ProxyNumber,
		ProcessingCode:           txn.ProcessingCode,
		SettlementAmount:         txn.SettlementAmount,
		SettlementCurrency:       txn.SettlementCurrency,
		OriginalAmount:           txn.OriginalAmount,
		OriginalCurrency:         txn.OriginalCurrency,
		AuthAmount:               txn.AuthAmount,
		AuthCurrency:             txn.AuthCurrency,
		LocalTxnDatetime:         txn.LocalTransactionDatetime,
		PanEntryMode:             txn.PanEntryMode,
		Mcc:                      txn.MCC,
		AcquirerRefData:          txn.AcquirerReferenceData,
		AcquirerID:               txn.AcquirerID,
		RetrievalRefNumber:       txn.RetrievalReferenceNumber,
		ApprovalCode:             txn.ApprovalCode,
		CardAcceptorTermID:       txn.CardAcceptorTerminalID,
		CardAcceptorID:           txn.CardAcceptorIDCode,
		CardAcceptorName:         txn.CardAcceptorName,
		CardSchemeTxnID:          txn.CardSchemeTransactionID,
		MessageReversalIndicator: txn.MessageReversalIndicator,
		MessageReasonCode:        txn.MessageReasonCode,
		CifNumber:                txn.CIFNumber,
		AccountNumber:            txn.AccountNumber,
		InternalTxnID:            txn.InternalTransactionID,
		Source:                   txn.Source,
		Status:                   txn.Status,
		StatusReason:             txn.StatusReason,
		CreatedAt:                txn.CreatedAt,
		UpdateAt:                 txn.UpdatedAt,
	}
}

func convertAdjustmentFailureStorageToAPI(txn *storage.AdjustmentScenarioFailure) *api.AdjustmentScenarioFailure {
	return &api.AdjustmentScenarioFailure{
		TransactionType:          txn.TransactionType,
		CardNumber:               txn.CardNumber,
		ProxyNumber:              txn.ProxyNumber,
		TxnAuditNumber:           txn.TxnAuditNumber,
		AuthCode:                 txn.AuthCode,
		RetrievalReferenceNumber: txn.RetrievalReferenceNumber,
		TxnCurrency:              txn.TxnCurrency,
		TxnAmount:                txn.TxnAmount,
		BillingCurrency:          txn.BillingCurrency,
		BillingAmount:            txn.BillingAmount,
		TxnDate:                  txn.TxnDate,
		MerchantNameLoc:          txn.MerchantNameLoc,
		JulianDate:               txn.JulianDate,
		Status:                   txn.Status,
		StatusReason:             txn.StatusReason,
		InternalTxnID:            txn.InternalTransactionID,
		Source:                   txn.Source,
		CreatedAt:                txn.CreatedAt,
		UpdatedAt:                txn.UpdatedAt,
	}
}
