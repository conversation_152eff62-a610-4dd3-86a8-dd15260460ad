package transactiondetails

import (
	context "context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	checkClearingFailureResumable = _checkClearingFailureResumable
)

func _checkClearingFailureResumable(ctx context.Context, logTag string, clearingFailure storage.ClearingFailure) (isResumable bool, err error) {
	defer func() {
		slog.FromContext(ctx).Info(logTag,
			fmt.Sprintf("[checkClearingFailureResumable] ended with isResumable=%v for clearingType=%v internalTxnID=%v",
				isResumable, clearingFailure.Type, clearingFailure.InternalTransactionID))
	}()

	// Only FAILED clearingFailure can be resumed
	if clearingFailure.Status != constant.Failed {
		return false, nil
	}

	// For refund clearing type, can always resume
	if clearingFailure.Type == string(constant.Refund) {
		return true, nil
	}
	if clearingFailure.Type == string(constant.Capture) && clearingFailure.InternalTransactionID != "" {
		query := []data.Condition{
			data.EqualTo("InternalTransactionID", clearingFailure.InternalTransactionID),
			data.Limit(1),
		}
		txns, findErr := storage.CardTransactionD.FindOnSlave(ctx, query...)
		if errors.Is(findErr, data.ErrNoData) {
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("[checkClearingFailureResumable] CardTransaction record not found by InternalTransactionID %v", clearingFailure.InternalTransactionID))
			return false, nil
		}
		if findErr != nil {
			slog.FromContext(ctx).Warn(logTag, "[checkClearingFailureResumable] CardTransactionD.FindOnSlave error", slog.Error(err))
			return false, err
		}
		txn := txns[0]
		if txn.Status == string(constant.TxnFailed) || txn.Status == string(constant.TxnAuthorised) {
			return true, nil
		}
		return false, nil
	}

	return false, nil
}
