package logic

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	gophersData "gitlab.myteksi.net/gophers/go/commons/data"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestBuildFindQueryByAc(t *testing.T) {
	dummyRRN := "rrn1234"
	dummyProxyNo := "prxy1234"
	dummyApprovalCode := "apprv1234"
	dummyCardSchemeTxnID := "txn234"
	errNoApplicable := errors.New("no applicable ACs found")
	tests := []struct {
		name     string
		params   ACQueryParam
		source   string
		expected []ACResult
		error    error
	}{
		{
			name:   "Clearing Refund:AC 1 requirements",
			params: ACQueryParam{RetrievalReferenceNumber: dummyRRN, ProxyNumber: dummyProxyNo, ApprovalCode: dummyApprovalCode, CardSchemeTransactionID: dummyCardSchemeTxnID},
			source: "logic.clearing",
			expected: []ACResult{
				{
					AC: "AC1",
					Conditions: []data.Condition{
						data.EqualTo("RetrievalReferenceNumber", dummyRRN),
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				},
				{
					AC: "AC2_AC5",
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				},
				{
					AC: "AC3",
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				},
				{
					AC: "AC4",
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
					},
				},
				{
					AC: "AC9",
					Conditions: []data.Condition{
						data.EqualTo("RetrievalReferenceNumber", dummyRRN),
						data.EqualTo("ProxyNumber", dummyProxyNo),
					},
				},
			},
			error: nil,
		},
		{
			name:   "Clearing Refund:All params available - AC1, AC2_AC5, AC3, AC4, AC9",
			params: ACQueryParam{RetrievalReferenceNumber: dummyRRN, ProxyNumber: dummyProxyNo, ApprovalCode: dummyApprovalCode, CardSchemeTransactionID: dummyCardSchemeTxnID},
			source: "logic.clearing",
			expected: []ACResult{
				{
					AC: AC1,
					Conditions: []data.Condition{
						data.EqualTo("RetrievalReferenceNumber", dummyRRN),
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				}, {
					AC: AC2AC5,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				}, {
					AC: AC3,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				}, {
					AC: AC4,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
					},
				}, {
					AC: AC9,
					Conditions: []data.Condition{
						data.EqualTo("RetrievalReferenceNumber", dummyRRN),
						data.EqualTo("ProxyNumber", dummyProxyNo),
					},
				},
			},
			error: nil,
		},
		{
			name:   "Clearing Refund:RetrievalReferenceNumber NOT available - AC2_AC5, AC3, AC4",
			params: ACQueryParam{ProxyNumber: dummyProxyNo, ApprovalCode: dummyApprovalCode, CardSchemeTransactionID: dummyCardSchemeTxnID},
			source: "logic.clearing",
			expected: []ACResult{
				{
					AC: AC2AC5,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				}, {
					AC: AC3,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				}, {
					AC: AC4,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
					},
				},
			},
			error: nil,
		},
		{
			name:   "Clearing Refund:RetrievalReferenceNumber and ApprovalCode NOT available - AC3",
			params: ACQueryParam{ProxyNumber: dummyProxyNo, CardSchemeTransactionID: dummyCardSchemeTxnID},
			source: "logic.clearing",
			expected: []ACResult{
				{
					AC: AC3,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				},
			},
			error: nil,
		},
		{
			name:   "Clearing Refund:RetrievalReferenceNumber and CardSchemeTransactionID NOT available - AC4",
			params: ACQueryParam{ProxyNumber: dummyProxyNo, ApprovalCode: dummyApprovalCode},
			source: "logic.clearing",
			expected: []ACResult{
				{
					AC: AC4,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("ApprovalCode", dummyApprovalCode),
					},
				},
			},
			error: nil,
		},
		{
			name:   "Clearing Refund:Only RetrievalReferenceNumber and ProxyNumber - AC9",
			params: ACQueryParam{ProxyNumber: dummyProxyNo, RetrievalReferenceNumber: dummyRRN},
			source: "logic.clearing",
			expected: []ACResult{
				{
					AC: AC9,
					Conditions: []data.Condition{
						data.EqualTo("RetrievalReferenceNumber", dummyRRN),
						data.EqualTo("ProxyNumber", dummyProxyNo),
					},
				},
			},
			error: nil,
		},
		{
			name:     "Clearing Refund:Only RetrievalReferenceNumber - no applicable AC",
			params:   ACQueryParam{RetrievalReferenceNumber: dummyRRN},
			source:   "logic.clearing",
			expected: []ACResult{},
			error:    errNoApplicable,
		},
		{
			name:     "Clearing Refund: Only ProxyNumber - no applicable AC",
			params:   ACQueryParam{ProxyNumber: dummyProxyNo},
			source:   "logic.clearing",
			expected: []ACResult{},
			error:    errNoApplicable,
		},
		{
			name:   "Cancel: Only ProxyNumber and CardSchemeTransactionID - AC3",
			params: ACQueryParam{ProxyNumber: dummyProxyNo, CardSchemeTransactionID: dummyCardSchemeTxnID},
			source: "cancel_transaction",
			expected: []ACResult{
				{
					AC: AC3,
					Conditions: []data.Condition{
						data.EqualTo("ProxyNumber", dummyProxyNo),
						data.EqualTo("CardSchemeTransactionID", dummyCardSchemeTxnID),
					},
				},
			},
			error: nil,
		},
		{
			name:     "Cancel: ProxyNumber - no applicable AC",
			params:   ACQueryParam{ProxyNumber: dummyProxyNo},
			source:   "cancel_transaction",
			expected: []ACResult{},
			error:    errNoApplicable,
		},
		{
			name:     "Empty params - no applicable AC",
			params:   ACQueryParam{},
			expected: []ACResult{},
			error:    errNoApplicable,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := buildFindQueryByAC(tt.params, tt.source)

			if err != nil {
				assert.Equal(t, tt.error, err)
			}

			require.Equal(t, len(tt.expected), len(result), "length mismatch")

			for i := range result {
				// Compare AC
				assert.Equal(t, tt.expected[i].AC, result[i].AC, "AC mismatch at index %d", i)

				// Compare Conditions length
				require.Equal(t, len(tt.expected[i].Conditions), len(result[i].Conditions),
					"Conditions length mismatch at index %d", i)

				// Compare each condition individually
				for j := range tt.expected[i].Conditions {
					expCond := tt.expected[i].Conditions[j]
					gotCond := result[i].Conditions[j]

					// Compare the actual fields of the Condition
					assert.Equal(t,
						fmt.Sprintf("%v", expCond),
						fmt.Sprintf("%v", gotCond),
						"Condition mismatch at index [%d][%d]", i, j)
				}
			}
		})
	}
}

func TestFindCardTransactionByAC(t *testing.T) {
	ctx := context.Background()
	mockTxnStorage := &storage.MockICardTransactionDAO{}
	storage.CardTransactionD = mockTxnStorage
	dummyRRN := "rrn1234"
	dummyProxyNo := "prxy1234"
	dummyApprovalCode := "apprv1234"
	dummyCardSchemeTxnID := "txn234"
	noDataErr := data.ErrNoData

	mockTxn := &storage.CardTransaction{
		ProxyNumber:              dummyRRN,
		AuthorizationID:          dummyApprovalCode,
		CardSchemeTransactionID:  dummyCardSchemeTxnID,
		RetrievalReferenceNumber: dummyRRN,
	}

	dummyFindTxnResp := []*storage.CardTransaction{
		mockTxn,
	}

	tests := []struct {
		name                string
		param               ACQueryParam
		additionalCondition func([]data.Condition) []data.Condition
		mockFindOnSlave     func()
		expectedResp        *storage.CardTransaction
		expectedError       error
	}{
		{
			name: "[CLEARING REFUND] - All param present | Txn found by first AC1",
			param: ACQueryParam{
				ProxyNumber:              dummyProxyNo,
				CardSchemeTransactionID:  dummyCardSchemeTxnID,
				RetrievalReferenceNumber: dummyRRN,
				ApprovalCode:             dummyApprovalCode,
			},
			additionalCondition: appendQueryCondition,
			mockFindOnSlave: func() {
				//	AC_1 - TXN FOUND
				mocksFindOnSlaveAC1(t, mockTxnStorage, dummyRRN, dummyProxyNo, dummyApprovalCode, dummyCardSchemeTxnID, dummyFindTxnResp, nil)
			},
			expectedResp: mockTxn,
		},
		{
			name: "[CLEARING REFUND] - All param present | Available AC should include AC1, AC2_AC5, AC3, AC4, AC9 | Txn found by AC9",
			param: ACQueryParam{
				ProxyNumber:              dummyProxyNo,
				CardSchemeTransactionID:  dummyCardSchemeTxnID,
				RetrievalReferenceNumber: dummyRRN,
				ApprovalCode:             dummyApprovalCode,
			},
			additionalCondition: appendQueryCondition,
			mockFindOnSlave: func() {
				//	AC_1 - TXN NOT FOUND
				mocksFindOnSlaveAC1(t, mockTxnStorage, dummyRRN, dummyProxyNo, dummyApprovalCode, dummyCardSchemeTxnID, nil, noDataErr)

				//	AC2_AC5 - TXN NOT FOUND
				mocksFindOnSlaveAC2AC5(t, mockTxnStorage, dummyProxyNo, dummyApprovalCode, dummyCardSchemeTxnID, nil, noDataErr)

				//	AC3 - TXN NOT FOUND
				mocksFindOnSlaveAC3(t, mockTxnStorage, dummyProxyNo, dummyCardSchemeTxnID, true, nil, noDataErr)

				//	AC4 - TXN NOT FOUND
				mocksFindOnSlaveAC4(t, mockTxnStorage, dummyProxyNo, dummyApprovalCode, nil, noDataErr)

				//	AC9 - TXN FOUND
				mocksFindOnSlaveAC9(t, mockTxnStorage, dummyRRN, dummyProxyNo, true, dummyFindTxnResp, nil)
			},
			expectedResp:  mockTxn,
			expectedError: nil,
		},
		{
			name: "[CLEARING REFUND] - RRN not present | Available AC should include AC2_AC5, AC3, AC4 | Txn found by AC4",
			param: ACQueryParam{
				ProxyNumber:             dummyProxyNo,
				CardSchemeTransactionID: dummyCardSchemeTxnID,
				ApprovalCode:            dummyApprovalCode,
			},
			additionalCondition: appendQueryCondition,
			mockFindOnSlave: func() {
				//	AC2_AC5 - TXN NOT FOUND
				mocksFindOnSlaveAC2AC5(t, mockTxnStorage, dummyProxyNo, dummyApprovalCode, dummyCardSchemeTxnID, nil, noDataErr)

				//	AC3 - TXN NOT FOUND
				mocksFindOnSlaveAC3(t, mockTxnStorage, dummyProxyNo, dummyCardSchemeTxnID, true, nil, noDataErr)

				//	AC4 - TXN FOUND
				mocksFindOnSlaveAC4(t, mockTxnStorage, dummyProxyNo, dummyApprovalCode, dummyFindTxnResp, nil)
			},
			expectedResp: mockTxn,
		},
		{
			name: "[CLEARING REFUND] - All param present | Available AC should include AC1, AC2_AC5, AC3, AC4, AC9 | No txn found",
			param: ACQueryParam{
				ProxyNumber:              dummyProxyNo,
				CardSchemeTransactionID:  dummyCardSchemeTxnID,
				RetrievalReferenceNumber: dummyRRN,
				ApprovalCode:             dummyApprovalCode,
			},
			additionalCondition: appendQueryCondition,
			mockFindOnSlave: func() {
				//	AC_1 - TXN NOT FOUND
				mocksFindOnSlaveAC1(t, mockTxnStorage, dummyRRN, dummyProxyNo, dummyApprovalCode, dummyCardSchemeTxnID, nil, noDataErr)

				//	AC2_AC5 - TXN NOT FOUND
				mocksFindOnSlaveAC2AC5(t, mockTxnStorage, dummyProxyNo, dummyApprovalCode, dummyCardSchemeTxnID, nil, noDataErr)

				//	AC3 - TXN NOT FOUND
				mocksFindOnSlaveAC3(t, mockTxnStorage, dummyProxyNo, dummyCardSchemeTxnID, true, nil, noDataErr)

				//	AC4 - TXN NOT FOUND
				mocksFindOnSlaveAC4(t, mockTxnStorage, dummyProxyNo, dummyApprovalCode, nil, noDataErr)

				//	AC9 - TXN NOT FOUND
				mocksFindOnSlaveAC9(t, mockTxnStorage, dummyRRN, dummyProxyNo, true, nil, noDataErr)
			},
			expectedError: noDataErr,
		},
		{
			name: "[AUTH CANCEL] - All param present | Txn found by first AC",
			param: ACQueryParam{
				ProxyNumber:              dummyProxyNo,
				CardSchemeTransactionID:  dummyCardSchemeTxnID,
				RetrievalReferenceNumber: dummyRRN,
			},
			additionalCondition: nil,
			mockFindOnSlave: func() {
				//	AC3 - TXN FOUND
				mocksFindOnSlaveAC3(t, mockTxnStorage, dummyProxyNo, dummyCardSchemeTxnID, false, dummyFindTxnResp, nil)
			},
			expectedResp:  mockTxn,
			expectedError: nil,
		},
		{
			name: "[AUTH CANCEL] - All param present | Available AC should include AC3, AC9 | Txn found by AC9",
			param: ACQueryParam{
				ProxyNumber:              dummyProxyNo,
				CardSchemeTransactionID:  dummyCardSchemeTxnID,
				RetrievalReferenceNumber: dummyRRN,
			},
			additionalCondition: nil,
			mockFindOnSlave: func() {

				//	AC3 - TXN NOT FOUND
				mocksFindOnSlaveAC3(t, mockTxnStorage, dummyProxyNo, dummyCardSchemeTxnID, false, nil, noDataErr)

				//	AC9 - TXN FOUND
				mocksFindOnSlaveAC9(t, mockTxnStorage, dummyRRN, dummyProxyNo, false, dummyFindTxnResp, nil)
			},
			expectedResp:  mockTxn,
			expectedError: nil,
		},
		{
			name: "[AUTH CANCEL] - All param present | Available AC should include AC3, AC9 | No txn found",
			param: ACQueryParam{
				ProxyNumber:              dummyProxyNo,
				CardSchemeTransactionID:  dummyCardSchemeTxnID,
				RetrievalReferenceNumber: dummyRRN,
			},
			additionalCondition: nil,
			mockFindOnSlave: func() {

				//	AC3 - TXN NOT FOUND
				mocksFindOnSlaveAC3(t, mockTxnStorage, dummyProxyNo, dummyCardSchemeTxnID, false, nil, noDataErr)

				//	AC9 - TXN FOUND
				mocksFindOnSlaveAC9(t, mockTxnStorage, dummyRRN, dummyProxyNo, false, nil, noDataErr)
			},
			expectedResp:  nil,
			expectedError: noDataErr,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockFindOnSlave != nil {
				tt.mockFindOnSlave()
			}
			result, err := FindCardTransactionByAC(ctx, tt.param, tt.additionalCondition, "logTag")

			if err != nil {
				assert.Error(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				// Check relevant fields only
				assert.Equal(t, tt.expectedResp.RetrievalReferenceNumber, result.RetrievalReferenceNumber)
				assert.Equal(t, tt.expectedResp.AuthorizationID, result.AuthorizationID)
				assert.Equal(t, tt.expectedResp.CardSchemeTransactionID, result.CardSchemeTransactionID)
				assert.Equal(t, tt.expectedResp.ProxyNumber, result.ProxyNumber)
				assert.Equal(t, tt.expectedResp.TransactionType, result.TransactionType)
				assert.Nil(t, err)
			}
		})
	}
}

func mocksFindOnSlaveAC1(t *testing.T, mockTxnStorage *storage.MockICardTransactionDAO, rrn, proxyNumber, approvalCode, cardSchemeTransactionID string, returnResult []*storage.CardTransaction, returnErr error) {
	mocksFindOnSlave(t, mockTxnStorage, []data.Condition{
		data.EqualTo(RRN, rrn),
		data.EqualTo(ProxyNo, proxyNumber),
		data.EqualTo(AuthorizationID, approvalCode),
		data.EqualTo(CardSchemeTransactionID, cardSchemeTransactionID),
		data.UnEqualTo("TransactionType", string(constant.TransactionTypeDirectDebit)),
	}, returnResult, returnErr)
}

func mocksFindOnSlaveAC2AC5(t *testing.T, mockTxnStorage *storage.MockICardTransactionDAO, proxyNumber, approvalCode, cardSchemeTransactionID string, returnResult []*storage.CardTransaction, returnErr error) {
	mocksFindOnSlave(t, mockTxnStorage, []data.Condition{
		data.EqualTo(ProxyNo, proxyNumber),
		data.EqualTo(AuthorizationID, approvalCode),
		data.EqualTo(CardSchemeTransactionID, cardSchemeTransactionID),
		data.UnEqualTo("TransactionType", string(constant.TransactionTypeDirectDebit)),
	}, returnResult, returnErr)
}

func mocksFindOnSlaveAC3(t *testing.T, mockTxnStorage *storage.MockICardTransactionDAO, proxyNumber, cardSchemeTransactionID string, filterTransactionType bool, returnResult []*storage.CardTransaction, returnErr error) {
	if filterTransactionType {
		mocksFindOnSlave(t, mockTxnStorage, []data.Condition{
			data.EqualTo(ProxyNo, proxyNumber),
			data.EqualTo(CardSchemeTransactionID, cardSchemeTransactionID),
			data.UnEqualTo("TransactionType", string(constant.TransactionTypeDirectDebit)),
		}, returnResult, returnErr)
	} else {
		mocksFindOnSlave(t, mockTxnStorage, []data.Condition{
			data.EqualTo(ProxyNo, proxyNumber),
			data.EqualTo(CardSchemeTransactionID, cardSchemeTransactionID),
		}, returnResult, returnErr)
	}
}

func mocksFindOnSlaveAC4(t *testing.T, mockTxnStorage *storage.MockICardTransactionDAO, proxyNumber, approvalCode string, returnResult []*storage.CardTransaction, returnErr error) {
	mocksFindOnSlave(t, mockTxnStorage, []data.Condition{
		data.EqualTo(ProxyNo, proxyNumber),
		data.EqualTo(AuthorizationID, approvalCode),
		data.UnEqualTo("TransactionType", string(constant.TransactionTypeDirectDebit)),
	}, returnResult, returnErr)
}

func mocksFindOnSlaveAC9(t *testing.T, mockTxnStorage *storage.MockICardTransactionDAO, rrn, proxyNumber string, filterTransactionType bool, returnResult []*storage.CardTransaction, returnErr error) {
	if filterTransactionType {
		mocksFindOnSlave(t, mockTxnStorage, []data.Condition{
			data.EqualTo(RRN, rrn),
			data.EqualTo(ProxyNo, proxyNumber),
			data.UnEqualTo("TransactionType", string(constant.TransactionTypeDirectDebit)),
		}, returnResult, returnErr)
	} else {
		mocksFindOnSlave(t, mockTxnStorage, []data.Condition{
			data.EqualTo(RRN, rrn),
			data.EqualTo(ProxyNo, proxyNumber),
		}, returnResult, returnErr)
	}
}

func mocksFindOnSlave(t *testing.T, mockTxnStorage *storage.MockICardTransactionDAO, conditions []data.Condition, returnResult []*storage.CardTransaction, returnErr error) {
	if len(conditions) == 5 {
		mockTxnStorage.On("FindOnSlave", mock.Anything,
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[0], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[1], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[2], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[3], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[4], condition)
			}),
		).Return(returnResult, returnErr).Once()
		return
	}

	if len(conditions) == 4 {
		mockTxnStorage.On("FindOnSlave", mock.Anything,
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[0], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[1], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[2], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[3], condition)
			}),
		).Return(returnResult, returnErr).Once()
		return
	}

	if len(conditions) == 3 {
		mockTxnStorage.On("FindOnSlave", mock.Anything,
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[0], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[1], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[2], condition)
			}),
		).Return(returnResult, returnErr).Once()
		return
	}

	if len(conditions) == 2 {
		mockTxnStorage.On("FindOnSlave", mock.Anything,
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[0], condition)
			}),
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[1], condition)
			}),
		).Return(returnResult, returnErr).Once()
		return
	}

	if len(conditions) == 1 {
		mockTxnStorage.On("FindOnSlave", mock.Anything,
			mock.MatchedBy(func(condition data.Condition) bool {
				return conditionsEqual(conditions[0], condition)
			}),
		).Return(returnResult, returnErr).Once()
		return
	}

	assert.Fail(t, "unsupported mock of FindOnSlave")
}

func conditionsEqual(condition1 data.Condition, condition2 data.Condition) bool {
	query := &gophersData.QueryNotImplement{}
	cond1Context := condition1(query)
	cond2Context := condition2(query)

	if len(cond1Context) != len(cond2Context) {
		return false
	}
	for i, cond1Detail := range cond1Context {
		if !reflect.DeepEqual(cond1Detail, cond2Context[i]) {
			return false
		}
	}
	return true
}

func appendQueryCondition(query []data.Condition) []data.Condition {
	query = append(query, data.UnEqualTo("TransactionType", string(constant.TransactionTypeDirectDebit)))
	return query
}
