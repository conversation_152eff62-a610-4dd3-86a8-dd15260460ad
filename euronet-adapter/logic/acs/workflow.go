package acs

import (
	"context"
	"fmt"

	identityExperience "gitlab.myteksi.net/dbmy/identity-experience/api"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/acsclient"

	acssecurity "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/acs/security"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	we "gitlab.myteksi.net/dakota/workflowengine"
)

var (
	wfInit    = we.InitExecution
	wfGet     = we.GetExecution
	wfExecute = we.Execute
)

// WorkflowImpl ...
type WorkflowImpl struct {
	EuronetACSClient         acsclient.EuronetACSClient `inject:"client.EuronetACS"`
	StatsD                   statsd.Client              `inject:"statsD"`
	ACSSecurityClient        acssecurity.Client         `inject:"client.acsSecurity"`
	IdentityExperienceClient identityExperience.Idexp   `inject:"client.identityExperience"`
	ByPassFinTrustFlag       bool                       `inject:"config.byPassFinTrust"`
}

const (
	workflowID = "acs_authenticate"
	logAuthn   = "acs_authn_transaction"
)

const (
	stInit                 = we.StateInit
	stRequestPersisted     = we.State(10)
	stPushedNotifFail      = we.State(20)
	stPushedNotifSuccess   = we.State(21)
	stAuthenticatedSuccess = we.State(31)
	stAuthenticatedFail    = we.State(32)
)

const (
	evNoNeed        = we.EventNoNeed
	evAuthnRequest  = we.Event(100)
	evAuthenticated = we.Event(200)
)

// RegisterWorkflow ...
func (w *WorkflowImpl) RegisterWorkflow() {
	wf := we.NewWorkflow(workflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})

	wf.AddTransition(logAuthn, stInit, evAuthnRequest, w.persistAuthnRequest, nil, stRequestPersisted)
	wf.AddTransition(logAuthn, stRequestPersisted, evNoNeed, w.pushNotif, nil, stPushedNotifSuccess, stPushedNotifFail)
	wf.AddTransition(logAuthn, stPushedNotifSuccess, evAuthenticated, w.callback, nil, stAuthenticatedSuccess, stAuthenticatedFail)

	we.RegisterWorkflow(wf)
}

// Stats ...
func (w *WorkflowImpl) Stats(ctx context.Context, status string, statusReason string) {
	slog.FromContext(ctx).Debug(workflowID, fmt.Sprintf("publishing %s metric", status))
	w.StatsD.Count1(logic.EuronetAdapter, workflowID,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}

// ConsumeFromFinTrustStream to consume message sent from FinTrust to us via kafka
func ConsumeFromFinTrustStream(ctx context.Context, data *dto.FinTrustStreamMessage) error {
	_, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		RequestID:      data.ACSTnxID,
		ExecutionEvent: evAuthenticated,
	}, data)

	if err != nil {
		return err
	}

	return nil
}
