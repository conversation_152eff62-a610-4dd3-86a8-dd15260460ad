package logic

import (
	"context"
	"errors"
	"os"
	"testing"
	"time"

	gdProxyAPI "gitlab.myteksi.net/dbmy/gd-proxy/api"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	mocks "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common/signature"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const (
	serviceEnvVar = "SERVICE_CONF"
	secretEnvVar  = "SECRET_CONF"
)

func TestStateToStatus(t *testing.T) {
	scenarios := []struct {
		desc     string
		input    workflowengine.State
		expected string
	}{
		{
			desc:     "Processing",
			input:    100,
			expected: string(Processing),
		},
		{
			desc:     "Failed",
			input:    400,
			expected: string(Failed),
		},
		{
			desc:     "Success",
			input:    600,
			expected: string(Success),
		},
		{
			desc:     "Processing",
			input:    9999,
			expected: string(Processing),
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			result := StateToStatus(scenario.input)
			assert.Equal(t, scenario.expected, result)
		})
	}
}

func TestGetResponseCode(t *testing.T) {
	scenarios := []struct {
		desc     string
		input    string
		expected *AdapterError
	}{
		{
			desc:     "InvalidCardID",
			input:    InvalidCardID,
			expected: &ErrInvalidProxyNumber,
		},
		{
			desc:     "Failed",
			input:    "unknown test error",
			expected: &ErrCardTransactionCommonError,
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			result := GetResponseCode(scenario.input)
			assert.Equal(t, scenario.expected, result)
		})
	}
}

func TestConvertExpiryDate(t *testing.T) {
	scenarios := []struct {
		desc     string
		input    string
		expected string
	}{
		{
			desc:     "format YYYYMMDD",
			input:    "20220910",
			expected: "09/22",
		},
		{
			desc:     "unknown format",
			input:    "19-10",
			expected: "19-10",
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			result := ConvertExpiryDate(scenario.input)
			assert.Equal(t, scenario.expected, result)
		})
	}
}

func TestConvertMtiRequestToResponse(t *testing.T) {
	scenarios := []struct {
		desc     string
		input    string
		expected string
	}{
		{
			desc:     "happy path",
			input:    "0100",
			expected: "0110",
		},
		{
			desc:     "happy path",
			input:    "0220",
			expected: "0230",
		},
		{
			desc:     "unknown",
			input:    "00000",
			expected: "",
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			result := ConvertMtiRequestToResponse(scenario.input)
			assert.Equal(t, scenario.expected, result)
		})
	}
}

func TestConvertMtiPaynetRequestToResponse(t *testing.T) {
	scenarios := []struct {
		desc     string
		input    string
		expected string
	}{
		{
			desc:     "happy path",
			input:    "0200",
			expected: "0210",
		},
		{
			desc:     "happy path",
			input:    "0220",
			expected: "0230",
		},
		{
			desc:     "unknown",
			input:    "00000",
			expected: "",
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			result := ConvertMtiPaynetRequestToResponse(scenario.input)
			assert.Equal(t, scenario.expected, result)
		})
	}
}

func TestSignENRequest(t *testing.T) {
	type testRequestBody struct {
		Name string `json:"name"`
		Addr string `json:"address"`
	}
	testBody := testRequestBody{
		Name: "test-name",
		Addr: "test-addr",
	}
	scenarios := []struct {
		desc                  string
		body                  interface{}
		expectedMarshalString string
		expectedSignature     string
		expectedErr           error
	}{
		{
			desc:                  "happy path",
			body:                  testBody,
			expectedMarshalString: "{\"name\":\"test-name\",\"address\":\"test-addr\"}",
			expectedSignature:     "test-signature",
		},
		{
			desc:                  "sad path - nil body",
			body:                  nil,
			expectedMarshalString: "null",
			expectedSignature:     "",
		},
		{
			desc:                  "sad path - sign error",
			body:                  testBody,
			expectedMarshalString: "{\"name\":\"test-name\",\"address\":\"test-addr\"}",
			expectedSignature:     "",
			expectedErr:           errors.New("unknown error"),
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			signingMethod := &signature.MockSigningMethod{}
			signingMethod.On("Sign", scenario.expectedMarshalString, mock.Anything).
				Return(scenario.expectedSignature, scenario.expectedErr).Once()
			result, err := SignENRequest(signingMethod, scenario.body, nil)
			assert.Equal(t, scenario.expectedSignature, result)
			assert.Equal(t, scenario.expectedErr, err)
		})
	}
}

func TestLoadFeatureConfig(t *testing.T) {
	oldServiceConf, existsService := os.LookupEnv(serviceEnvVar)
	oldSecretsDir, existsSecrets := os.LookupEnv(secretEnvVar)

	configFile, err := os.CreateTemp("", "test")
	assert.NoError(t, err)

	secretsDir, err := os.MkdirTemp("", "test")
	assert.NoError(t, err)

	os.Setenv(serviceEnvVar, configFile.Name())
	os.Setenv(secretEnvVar, secretsDir)
	defer func() {
		if existsService {
			os.Setenv(serviceEnvVar, oldServiceConf)
		} else {
			os.Unsetenv(serviceEnvVar)
		}
		if existsSecrets {
			os.Setenv(secretEnvVar, oldSecretsDir)
		} else {
			os.Unsetenv(secretEnvVar)
		}
	}()

	_, err = configFile.WriteString("{\"featureFlags\": {\"enableWorker\": true,\"enableActivationCodeCheck\": true}}")
	assert.NoError(t, err)

	err = configFile.Close()
	assert.NoError(t, err)

	tests := []struct {
		desc    string
		want    *config.FeatureFlags
		wantErr error
	}{
		{
			desc: "happy path",
			want: &config.FeatureFlags{
				EnableWorker:              true,
				EnableActivationCodeCheck: true,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.desc, func(t *testing.T) {
			got, err := LoadFeatureConfig()
			if tt.wantErr != nil {
				assert.Equalf(t, tt.wantErr, err, tt.desc)
				assert.Nil(t, got)
			} else {
				assert.Equalf(t, tt.want.EnableActivationCodeCheck, got.FeatureFlags.EnableActivationCodeCheck, tt.desc)
				assert.Equalf(t, tt.want.EnableWorker, got.FeatureFlags.EnableWorker, tt.desc)
				assert.Nil(t, err)
			}
		})
	}
}

func TestParseAcceptorCountryCode(t *testing.T) {
	scenarios := []struct {
		desc        string
		termName    string
		networkID   string
		expected    string
		expectedErr error
	}{
		{
			desc:      "SGP",
			networkID: constant.NETWORK_MASTERCARD,
			termName:  "XXX SGP",
			expected:  "SGP",
		},
		{
			desc:      "MYS",
			networkID: constant.NETWORK_SAN,
			termName:  "XXX SGP",
			expected:  "MYS",
		},
		{
			desc:      "empty",
			networkID: constant.NETWORK_MASTERCARD,
			termName:  "XX",
			expected:  "",
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			result := ParseAcceptorCountryCodeAlpha3(scenario.termName, scenario.networkID)
			assert.Equal(t, scenario.expected, result)
		})
	}
}

func TestRequestDateTime(t *testing.T) {
	// Get the current time
	expectedTime := time.Now()
	// Format it using the same format as the RequestDateTime function
	expectedFormattedTime := expectedTime.Format("2006-01-02 15:04:05")

	// Call the function
	actualFormattedTime := RequestDateTime()

	assert.Equal(t, expectedFormattedTime, actualFormattedTime)
}

func TestUpdateDBCardTxn1(t *testing.T) {
	tests := []struct {
		name     string
		mockFunc func()
		wantErr  error
	}{
		{
			name: "update failed",
			mockFunc: func() {
				mockStorageDAO := &storage.MockICardTransactionDAO{}
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("err")).Once()
				storage.CardTransactionD = mockStorageDAO
			},
			wantErr: errors.New("err"),
		},
		{
			name: "update successful",
			mockFunc: func() {
				mockStorageDAO := &storage.MockICardTransactionDAO{}
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				storage.CardTransactionD = mockStorageDAO
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			test.mockFunc()
			err := UpdateDBCardTxn(context.Background(), &storage.CardTransaction{}, &storage.CardTransaction{})
			assert.Equal(t, test.wantErr, err)
		})
	}
}

func TestFetchInternalCardID(t *testing.T) {
	tests := []struct {
		name     string
		mockFunc func()
		want     string
		wantErr  error
	}{
		{
			name: "happy case",
			mockFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{InternalCardID: "1"}}, nil)
			},
			want:    "1",
			wantErr: nil,
		}, {
			name: "err",
			mockFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("err"))
			},
			want:    "",
			wantErr: errors.New("err"),
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			test.mockFunc()
			resp, err := FetchInternalCardID(context.Background(), "1", "")
			if test.wantErr != nil {
				assert.Equal(t, test.wantErr, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.want, resp)
			}
		})
	}
}

func TestFetchCustomerSafeID(t *testing.T) {
	tests := []struct {
		name     string
		mockFunc func()
		want     string
		wantErr  error
	}{
		{
			name: "happy case",
			mockFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{InternalCustomerID: "1"}}, nil)
			},
			want:    "1",
			wantErr: nil,
		},
		{
			name: "len 0",
			mockFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{}, nil)
			},
			want:    "1",
			wantErr: ErrProxyNumberNotFound,
		},
		{
			name: "len 0",
			mockFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("err"))
			},
			want:    "1",
			wantErr: errors.New("err"),
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			test.mockFunc()
			resp, err := FetchCustomerSafeID(context.Background(), "1", "")
			if test.wantErr != nil {
				assert.Equal(t, test.wantErr, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.want, resp)
			}
		})
	}
}

func TestCheckAccountStatus(t *testing.T) {
	tests := []struct {
		name             string
		mockCardDBFunc   func()
		mockDcClientFunc func() api.DigicardCore
		wantErr          error
	}{
		{
			name: "happy case",
			mockCardDBFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{InternalCardID: "1"}}, nil)
			},
			mockDcClientFunc: func() api.DigicardCore {
				mockDigicardCore := &mocks.DigicardCore{}
				mockDigicardCore.On("AccountStatusEnquiry", mock.Anything, mock.Anything).Once().Return(&api.AccountStatusEnquiryResponse{Status: string(constant.CardStatusActive)}, nil)
				return mockDigicardCore
			},
			wantErr: nil,
		},
		{
			name: "ErrProxyNumberNotFound",
			mockCardDBFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, ErrProxyNumberNotFound)
			},
			mockDcClientFunc: func() api.DigicardCore {
				mockDigicardCore := &mocks.DigicardCore{}
				mockDigicardCore.On("AccountStatusEnquiry", mock.Anything, mock.Anything).Once().Return(&api.AccountStatusEnquiryResponse{Status: string(constant.CardStatusActive)}, nil)
				return mockDigicardCore
			},
			wantErr: ErrProxyNumberNotFound,
		},
		{
			name: "api err",
			mockCardDBFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{InternalCardID: "1"}}, nil)
			},
			mockDcClientFunc: func() api.DigicardCore {
				mockDigicardCore := &mocks.DigicardCore{}
				mockDigicardCore.On("AccountStatusEnquiry", mock.Anything, mock.Anything).Once().Return(&api.AccountStatusEnquiryResponse{Status: string(constant.CardStatusActive)}, errors.New("api err"))
				return mockDigicardCore
			},
			wantErr: errors.New("api err"),
		},
		{
			name: "err ACCOUNT_NOT_ACTIVE",
			mockCardDBFunc: func() {
				mockStorage := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorage
				mockStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{InternalCardID: "1"}}, nil)
			},
			mockDcClientFunc: func() api.DigicardCore {
				mockDigicardCore := &mocks.DigicardCore{}
				mockDigicardCore.On("AccountStatusEnquiry", mock.Anything, mock.Anything).Once().Return(&api.AccountStatusEnquiryResponse{Status: string(constant.TransactionTypeHoldAndPost)}, nil)
				return mockDigicardCore
			},
			wantErr: errors.New("ACCOUNT_NOT_ACTIVE"),
		},
	}
	for _, tc := range tests {
		test := tc
		t.Run(test.name, func(t *testing.T) {
			test.mockCardDBFunc()
			err := CheckAccountStatus(context.Background(), test.mockDcClientFunc(), "", constant.AccountStatusEnquiry, "")
			assert.Equal(t, test.wantErr, err)
		})
	}
}

func TestGetSuccessResponse(t *testing.T) {
	assert.Equal(t, ApprovedCode, GetSuccessResponse().ResponseCode)
}

func TestUpdateDBCardTxn(t *testing.T) {
	tests := []struct {
		name     string
		mockFunc func()
		wantErr  error
	}{
		{
			name: "happy case",
			mockFunc: func() {
				mockCardTransaction := &storage.MockICardTransactionDAO{}
				storage.CardTransactionD = mockCardTransaction
				mockCardTransaction.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			wantErr: nil,
		},
		{
			name: "err",
			mockFunc: func() {
				mockCardTransaction := &storage.MockICardTransactionDAO{}
				storage.CardTransactionD = mockCardTransaction
				mockCardTransaction.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("err"))
			},
			wantErr: errors.New("err"),
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			test.mockFunc()
			assert.Equal(t, test.wantErr, UpdateDBCardTxn(context.Background(), &storage.CardTransaction{}, &storage.CardTransaction{}))
		})
	}
}

func TestIdentifyAccountsFromDCIndicator(t *testing.T) {
	tests := []struct {
		name                        string
		dcIndicator                 string
		jvConfig                    config.JVProcessCfg
		expectedSource              string
		expectedDest                string
		expectedTransactionCodeType string
	}{
		{
			name:        "happy path - debit",
			dcIndicator: "DR",
			jvConfig: config.JVProcessCfg{
				DebitAccountID:      "123",
				CreditAccountID:     "321",
				TransactionCodeType: "test_debit",
			},
			expectedSource:              "123",
			expectedDest:                "321",
			expectedTransactionCodeType: "test_debit",
		},
		{
			name:        "happy path - credit",
			dcIndicator: "CR",
			jvConfig: config.JVProcessCfg{
				DebitAccountID:      "123",
				CreditAccountID:     "321",
				TransactionCodeType: "test_credit",
			},
			expectedSource:              "123",
			expectedDest:                "321",
			expectedTransactionCodeType: "test_credit",
		},
		{
			name:        "happy path - debit enabled EnableCreditDebitIndicator dr",
			dcIndicator: "dr",
			jvConfig: config.JVProcessCfg{
				EnableCreditDebitIndicator: true,
				DebitAccountID:             "123",
				CreditAccountID:            "321",
				TransactionCodeType:        "test_credit",
				CreditAccountMap:           config.JVAccountMap{SrcAccountID: "123", DstAccountID: "321", TransactionCodeType: "test_credit"},
				DebitAccountMap:            config.JVAccountMap{SrcAccountID: "321", DstAccountID: "123", TransactionCodeType: "test_debit"},
			},
			expectedSource:              "321",
			expectedDest:                "123",
			expectedTransactionCodeType: "test_debit",
		},
		{
			name:        "happy path - debit enabled EnableCreditDebitIndicator d",
			dcIndicator: "d",
			jvConfig: config.JVProcessCfg{
				EnableCreditDebitIndicator: true,
				DebitAccountID:             "123",
				CreditAccountID:            "321",
				TransactionCodeType:        "test_credit",
				CreditAccountMap:           config.JVAccountMap{SrcAccountID: "123", DstAccountID: "321", TransactionCodeType: "test_credit"},
				DebitAccountMap:            config.JVAccountMap{SrcAccountID: "321", DstAccountID: "123", TransactionCodeType: "test_debit"},
			},
			expectedSource:              "321",
			expectedDest:                "123",
			expectedTransactionCodeType: "test_debit",
		},
		{
			name:        "happy path - debit enabled EnableCreditDebitIndicator DR",
			dcIndicator: "DR",
			jvConfig: config.JVProcessCfg{
				EnableCreditDebitIndicator: true,
				DebitAccountID:             "123",
				CreditAccountID:            "321",
				TransactionCodeType:        "test_credit",
				CreditAccountMap:           config.JVAccountMap{SrcAccountID: "123", DstAccountID: "321", TransactionCodeType: "test_credit"},
				DebitAccountMap:            config.JVAccountMap{SrcAccountID: "321", DstAccountID: "123", TransactionCodeType: "test_debit"},
			},
			expectedSource:              "321",
			expectedDest:                "123",
			expectedTransactionCodeType: "test_debit",
		},
		{
			name:        "happy path - credit enabled EnableCreditDebitIndicator CR",
			dcIndicator: "CR",
			jvConfig: config.JVProcessCfg{
				EnableCreditDebitIndicator: true,
				DebitAccountID:             "123",
				CreditAccountID:            "321",
				TransactionCodeType:        "test",
				CreditAccountMap:           config.JVAccountMap{SrcAccountID: "123", DstAccountID: "321", TransactionCodeType: "test_credit"},
				DebitAccountMap:            config.JVAccountMap{SrcAccountID: "321", DstAccountID: "123", TransactionCodeType: "test_debit"},
			},
			expectedSource:              "123",
			expectedDest:                "321",
			expectedTransactionCodeType: "test_credit",
		},
	}
	for _, _tt := range tests {
		tt := _tt
		t.Run(tt.name, func(t *testing.T) {
			src, dst, transactionCodeType := IdentifyAccountsFromDCIndicator(tt.dcIndicator, tt.jvConfig)
			assert.Equalf(t, tt.expectedSource, src, tt.name)
			assert.Equalf(t, tt.expectedDest, dst, tt.name)
			assert.Equalf(t, tt.expectedTransactionCodeType, transactionCodeType, tt.name)
		})
	}
}

func TestAllValidRecommendations(t *testing.T) {
	tests := []struct {
		name            string
		recommendations []string
		expected        bool
	}{
		{
			name:            "All recommendations valid",
			recommendations: []string{gdProxyAPI.RecommendationAllow, gdProxyAPI.RecommendationNotAllow},
			expected:        true,
		},
		{
			name:            "One invalid recommendation",
			recommendations: []string{gdProxyAPI.RecommendationAllow, "InvalidValue"},
			expected:        false,
		},
		{
			name:            "All recommendations invalid",
			recommendations: []string{"InvalidValue1", "InvalidValue2"},
			expected:        false,
		},
		{
			name:            "Empty recommendations list",
			recommendations: []string{},
			expected:        false,
		},
		{
			name:            "Single valid recommendation",
			recommendations: []string{gdProxyAPI.RecommendationAllow},
			expected:        true,
		},
		{
			name:            "Single invalid recommendation",
			recommendations: []string{"InvalidValue"},
			expected:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsAllValidGDRiskCheckRecommendations(tt.recommendations)
			assert.Equal(t, tt.expected, result)
		})
	}
}
