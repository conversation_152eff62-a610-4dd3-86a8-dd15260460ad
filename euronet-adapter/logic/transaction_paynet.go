package logic

import (
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
)

// ChargeTypeAndCaptureMethod ...
type ChargeTypeAndCaptureMethod struct {
	ChargeType    string
	CaptureMethod dto.CaptureMethod
}

var (
	// PaynetTransactionTypeToChargeType this map define the ChargeType and CaptureMethod by Paynet TransactionType
	PaynetTransactionTypeToChargeType = map[constant.TransactionType]ChargeTypeAndCaptureMethod{
		constant.TransactionTypeHoldAndPost:  {"CHARGE", dto.Manual},
		constant.TransactionTypeDirectDebit:  {"CHARGE", dto.Automatic},
		constant.TransactionTypeDirectCredit: {"CHARGE", dto.Automatic},
	}
)

const (
	// PreAuthMTI ...
	PreAuthMTI = "0100"
	// AuthMTI ...
	AuthMTI = "0200"
	// SaleCompletionMTI ...
	SaleCompletionMTI = "0120"
	// SaleCompletionRepeatMTI ...
	SaleCompletionRepeatMTI = "0121"
	// AdjustmentMTI ...
	AdjustmentMTI = "0200"
	// ReversalMTI ...
	ReversalMTI = "0420"
	// ReversalRepeatMTI ...
	ReversalRepeatMTI = "0421"
)

// DetectPaynetTransactionCategory to detect transaction category
// Refer to https://docs.google.com/spreadsheets/d/1x3mZF52UeVcTawotAeBeZFND_-axq_sMy_gYq_F-3pU/edit#gid=0
func DetectPaynetTransactionCategory(mti, processingCode, posConditionCode, posEntryMode string) (*Transaction, *AdapterError) {
	transactionCode := processingCode[0:2]

	if mti == PreAuthMTI {
		return detectPaynetPreAuthTransactionCategory(transactionCode, posConditionCode, posEntryMode)
	}

	if mti == SaleCompletionMTI || mti == SaleCompletionRepeatMTI {
		return detectPaynetSaleCompletionTransactionCategory(transactionCode, posConditionCode, posEntryMode)
	}

	if mti == AuthMTI {
		return detectPaynetAuthTransactionCategory(transactionCode, posConditionCode, posEntryMode)
	}

	if common.ContainStringValue([]string{ReversalMTI, ReversalRepeatMTI}, mti) {
		return detectPaynetReversalTxnCategory(processingCode, posConditionCode, posEntryMode)
	}

	return nil, &ErrPaynetUnableDetectTransaction
}

// detectPaynetPreAuthTransactionCategory ...
func detectPaynetPreAuthTransactionCategory(transactionCode, posConditionCode, posEntryMode string) (*Transaction, *AdapterError) {
	if transactionCode != "00" {
		return nil, &ErrPaynetUnableDetectTransaction
	}
	// set currency conv type as local because paynet txn is domestic only
	transaction := &Transaction{
		CurrencyConvType: CurrencyConvTypeLocal,
	}
	switch posConditionCode + posEntryMode {
	case "06" + "270":
		transaction.TransactionCategory = constant.TransactionCategoryPOS
		transaction.TransactionSubCategory = TransactionSubCategoryContactless
		transaction.TransactionType = constant.TransactionTypeHoldAndPost
		return transaction, nil
	case "06" + "271":
		transaction.TransactionCategory = constant.TransactionCategoryPOS
		transaction.TransactionSubCategory = TransactionSubCategoryContactlessPin
		transaction.TransactionType = constant.TransactionTypeHoldAndPost
		return transaction, nil
	case "06" + "261":
		transaction.TransactionCategory = constant.TransactionCategoryPOS
		transaction.TransactionSubCategory = TransactionSubCategoryContact
		transaction.TransactionType = constant.TransactionTypeHoldAndPost
		return transaction, nil
	case "06" + "221":
		return nil, &ErrInvalidPANEntryMode
	case "56" + "010":
		transaction.TransactionCategory = constant.TransactionCategoryECOM
		transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
		transaction.TransactionType = constant.TransactionTypeHoldAndPost
		return transaction, nil
	case "57" + "010":
		transaction.TransactionCategory = constant.TransactionCategoryECOM
		transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
		transaction.TransactionType = constant.TransactionTypeHoldAndPost
		return transaction, nil
	default:
		return nil, &ErrPaynetUnableDetectTransaction
	}
}

// detectPaynetSaleCompletionTransactionCategory ...
func detectPaynetSaleCompletionTransactionCategory(transactionCode, posConditionCode, posEntryMode string) (*Transaction, *AdapterError) {
	if transactionCode != "00" {
		return nil, &ErrPaynetUnableDetectTransaction
	}
	// set currency conv type as local because paynet txn is domestic only
	transaction := &Transaction{
		CurrencyConvType: CurrencyConvTypeLocal,
	}
	switch posConditionCode + posEntryMode {
	case "06" + "310":
		transaction.TransactionCategory = constant.TransactionCategoryPOS
		transaction.TransactionSubCategory = TransactionSubCategoryCPNP
		transaction.TransactionType = constant.TransactionTypeDirectDebit
		return transaction, nil
	case "56" + "010":
		transaction.TransactionCategory = constant.TransactionCategoryECOM
		transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
		transaction.TransactionType = constant.TransactionTypeDirectDebit
		return transaction, nil
	case "57" + "010":
		transaction.TransactionCategory = constant.TransactionCategoryECOM
		transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
		transaction.TransactionType = constant.TransactionTypeDirectDebit
		return transaction, nil
	default:
		return nil, &ErrPaynetUnableDetectTransaction
	}
}

// detectPaynetAuthTransactionCategory ...
//
//nolint:dupl, funlen
func detectPaynetAuthTransactionCategory(transactionCode, posConditionCode, posEntryMode string) (*Transaction, *AdapterError) {
	// set currency conv type as local because paynet txn is domestic only
	transaction := &Transaction{
		CurrencyConvType: CurrencyConvTypeLocal,
	}
	mappingIdentifier := posEntryMode
	if posConditionCode != "" {
		mappingIdentifier = posConditionCode + posEntryMode
	}
	switch transactionCode {
	case "00":
		switch mappingIdentifier {
		case "270", "06" + "270":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactless
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "271", "06" + "271":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactlessPin
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "06" + "261", "261":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContact
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "06" + "221", "221":
			return nil, &ErrInvalidPANEntryMode
		case "59" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "60" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryDefault
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		}
	case "20": // cancellation
		switch mappingIdentifier {
		case "270", "06" + "270":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactless
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "271", "06" + "271":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactlessPin
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "06" + "261", "261":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContact
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "06" + "221", "221":
			return nil, &ErrInvalidPANEntryMode
		case "59" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "60" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryDefault
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		}
	case "02": // adjustment
		switch mappingIdentifier {
		case "53" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "54" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryDefault
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		}
	case "23": // adjustment
		switch mappingIdentifier {
		case "53" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "54" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryDefault
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		}
	case "01": // ATM Withdrawal
		if posConditionCode == "02" && posEntryMode == "261" {
			transaction.TransactionCategory = constant.TransactionCategoryATM
			transaction.TransactionSubCategory = TransactionSubCategoryWithdrawal
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		}
	case "30": // ATM balance enquiry
		if posConditionCode == "02" && posEntryMode == "261" {
			transaction.TransactionCategory = constant.TransactionCategoryATM
			transaction.TransactionSubCategory = TransactionSubCategoryBalanceEnquiry
			transaction.TransactionType = constant.TransactionTypeBalanceEnquiry
			return transaction, nil
		}
	default:
		return nil, &ErrPaynetUnableDetectTransaction
	}
	return nil, &ErrPaynetUnableDetectTransaction
}

// detectPaynetReversalTxnCategory ...
//
//nolint:funlen
func detectPaynetReversalTxnCategory(processingCode, posConditionCode, posEntryMode string) (*Transaction, *AdapterError) {
	tnxCode := processingCode[:2]
	transaction := &Transaction{
		CurrencyConvType: CurrencyConvTypeLocal,
	}
	mappingIdentifier := posEntryMode
	if posConditionCode != "" {
		mappingIdentifier = posConditionCode + posEntryMode
	}
	switch tnxCode {
	case "00":
		switch mappingIdentifier {
		case "06" + "310", "310":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryCPNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "06" + "221", "221":
			return nil, &ErrInvalidPANEntryMode
		case "06" + "261", "261":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContact
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "06" + "270", "270":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactless
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "06" + "271", "271":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactlessPin
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "56" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "57" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "59" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "60" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryDefault
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		default:
			return nil, &ErrPaynetUnableDetectTransaction
		}
	case "01":
		if mappingIdentifier == "02"+"261" {
			transaction.TransactionCategory = constant.TransactionCategoryATM
			transaction.TransactionSubCategory = TransactionSubCategoryWithdrawal
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		}
		return nil, &ErrPaynetUnableDetectTransaction
	case "20":
		switch mappingIdentifier {
		case "06" + "221", "221":
			return nil, &ErrInvalidPANEntryMode
		case "06" + "261", "261":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContact
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "06" + "270", "270":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactless
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "06" + "271", "271":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactlessPin
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "59" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "60" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryDefault
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		default:
			return nil, &ErrPaynetUnableDetectTransaction
		}
	case "02":
		switch mappingIdentifier {
		case "53" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "54" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		case "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryDefault
			transaction.TransactionType = constant.TransactionTypeDirectCredit
			return transaction, nil
		default:
			return nil, &ErrPaynetUnableDetectTransaction
		}
	case "23":
		switch mappingIdentifier {
		case "53" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "54" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		case "010":
			transaction.TransactionCategory = constant.TransactionCategoryAdjustment
			transaction.TransactionSubCategory = TransactionSubCategoryDefault
			transaction.TransactionType = constant.TransactionTypeDirectDebit
			return transaction, nil
		default:
			return nil, &ErrPaynetUnableDetectTransaction
		}
	default:
		return nil, &ErrPaynetUnableDetectTransaction
	}
}

// DetectPaynetReversalPreAuthTxnCategory ...
func DetectPaynetReversalPreAuthTxnCategory(processingCode, posConditionCode, posEntryMode string) (*Transaction, *AdapterError) {
	tnxCode := processingCode[:2]
	transaction := &Transaction{
		CurrencyConvType: CurrencyConvTypeLocal,
	}
	switch tnxCode {
	case "00":
		switch posConditionCode + posEntryMode {
		case "06" + "221":
			return nil, &ErrInvalidPANEntryMode
		case "06" + "261":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContact
			transaction.TransactionType = constant.TransactionTypeReleaseHold
			return transaction, nil
		case "06" + "270":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactless
			transaction.TransactionType = constant.TransactionTypeReleaseHold
			return transaction, nil
		case "06" + "271":
			transaction.TransactionCategory = constant.TransactionCategoryPOS
			transaction.TransactionSubCategory = TransactionSubCategoryContactlessPin
			transaction.TransactionType = constant.TransactionTypeReleaseHold
			return transaction, nil
		case "56" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryCITCNP
			transaction.TransactionType = constant.TransactionTypeReleaseHold
			return transaction, nil
		case "57" + "010":
			transaction.TransactionCategory = constant.TransactionCategoryECOM
			transaction.TransactionSubCategory = TransactionSubCategoryMITCNP
			transaction.TransactionType = constant.TransactionTypeReleaseHold
			return transaction, nil
		default:
			return nil, &ErrPaynetUnableDetectTransaction
		}
	default:
		return nil, &ErrPaynetUnableDetectTransaction
	}
}

// IsAdjustmentTxn ...
func IsAdjustmentTxn(mti string, processingCode string) bool {
	txnCode := processingCode[:2]
	return AdjustmentMTI == mti && (txnCode == "23" || txnCode == "02")
}
