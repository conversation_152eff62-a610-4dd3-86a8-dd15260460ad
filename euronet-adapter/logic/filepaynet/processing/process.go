package processing

import (
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/filepaynet"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transaction/authorize"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const logPersistFailure = "persist_failure"

type fileErrors map[string]string

func (w *WorkflowImpl) processFile(ctx context.Context, transitionID string, exec workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*filepaynet.ExecutionData)
	if !ok {
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()

	directory, ok := filepaynet.TypeToS3DirectoryMap[nextCtx.Req.Type]
	if !ok {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("critical: directory not found for job type %s.", nextCtx.Req.Type))
		// stats here
	}
	filePath := fmt.Sprintf("%s/%s", directory, nextCtx.Req.FileName)
	content, err := filepaynet.FetchFromS3(ctx, logTag, filePath)
	if err != nil {
		return nil, err
	}
	csvReader := csv.NewReader(bytes.NewBuffer(content))
	csvReader.FieldsPerRecord = -1 // file is not having constant number of columns
	fileErr := fileErrors(make(map[string]string))
	switch nextCtx.Req.Type {
	case constant.Adjustment2:
		fileErr = w.processAdjustmentScenario2File(ctx, csvReader, currCtx.Req.FileName)
	case constant.JV:
		fileErr = w.processJVFile(ctx, csvReader, currCtx.Req.FileName)
	}

	if len(fileErr) > 0 {
		nextCtx.File.Status = string(constant.FileProcessingPSuccess)
		converted, err := json.Marshal(fileErr)

		if err != nil {
			slog.FromContext(ctx).Error(logTag, "error when converting error into json", slog.Error(err))
		} else {
			nextCtx.File.StatusReason = converted

			w.StatsD.Count1(logic.EuronetAdapterTag, logTag,
				fmt.Sprintf("status:%s", nextCtx.File.Status),
				fmt.Sprintf("status_reason: total number of error lines%d", len(fileErr)))
		}
	} else {
		emptyError, _ := json.Marshal([]string{})
		nextCtx.File.StatusReason = emptyError
		nextCtx.File.Status = string(constant.FileProcessingSuccess)
	}

	nextCtx.State = stSuccess
	nextCtx.File.UpdatedAt = time.Now()

	if err := storage.FileD.UpdateEntity(ctx, &currCtx.File, &nextCtx.File); err != nil {
		slog.FromContext(ctx).Error(logTag, "error updating file processing status", slog.Error(err))
		return nil, err
	}
	return nextCtx, nil
}

func (w *WorkflowImpl) triggerCancelEvent(ctx context.Context, originalRequestID string, record *dto.AdjustmentScenario2Record, internalTxnID string) error {
	err := logic.CheckAccountStatus(ctx, w.DigicardCoreClient, record.ProxyNumber, constant.AuthExpiry, logTag)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "there is issue with account status before triggering cancel event", slog.Error(err))
		saveFailedAdjustmentScenario2(ctx, record, err.Error(), internalTxnID, originalRequestID)
		return err
	}
	err = authorize.TriggerCancelEvent(ctx, originalRequestID, authorize.CancelParams{
		OpParams: dto.OfflineProcessingParams{
			CancelSource: constant.FileSource,
		},
	})
	if err != nil {
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("error when triggering cancel event for requestID %s", originalRequestID), slog.Error(err))
		saveFailedAdjustmentScenario2(ctx, record, err.Error(), internalTxnID, originalRequestID)
		return err
	}
	return nil
}
