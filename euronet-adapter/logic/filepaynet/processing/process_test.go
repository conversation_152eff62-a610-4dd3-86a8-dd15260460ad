package processing

import (
	"context"
	"errors"
	"testing"
	"time"

	file "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/filepaynet"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/s3utils"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func TestFetchFromS3(t *testing.T) {
	mockS3Client := s3client.MockS3{}
	s3utils.S3Client = &mockS3Client

	tests := []struct {
		errS3       error
		expectedErr error
		description string
	}{
		{
			errS3:       nil,
			expectedErr: nil,
			description: "happy flow",
		},
		{
			errS3:       errors.New("simulate error"),
			expectedErr: errors.New("simulate error"),
			description: "s3 download failed",
		},
	}

	for _, test := range tests {
		description := test.description
		mockS3Client.On("GetObject", mock.Anything, mock.Anything).Return(nil, test.errS3).Once()
		nextCtx, err := file.FetchFromS3(context.Background(), "logTag", "filePath")
		if test.expectedErr == nil {
			assert.NoError(t, err, description)
		} else {
			assert.Equal(t, test.expectedErr, err, description)
			assert.Nil(t, nextCtx, description)
		}
	}
}

func TestProcessFile(t *testing.T) {
	dummyError := errors.New("simulate error")
	happyFile := "H,20220825,20220825,1\n" +
		"D,123456789013456,100010,60,60,60,220815000001,8080,1,5200,123456789,999,12345678012,000000,12345678,123456789012345,KFC Singapore,702,702,702,1234567890123456,N\n" +
		"F,1\n"
	workFlowObj := WorkflowImpl{
		StatsD: statsd.NewNoop(),
	}
	mockStorage := &storage.MockIFileDAO{}
	storage.FileD = mockStorage
	mockTxnStorage := &storage.MockICardTransactionDAO{}
	storage.CardTransactionD = mockTxnStorage

	mockS3Client := s3client.MockS3{}
	s3utils.S3Client = &mockS3Client

	tests := []struct {
		expectedErr error
		description string
		req         dto.FileRequest
		mockFunc    func()
	}{
		{
			mockFunc: func() {
				mockS3Client.On("GetObject", mock.Anything, mock.Anything).Return(nil, dummyError).Once()
			},
			req: dto.FileRequest{
				Type:           constant.Adjustment2,
				FileName:       "some_name",
				IdempotencyKey: uuid.NewString(),
			},
			expectedErr: errors.New("simulate error"),
			description: "Adjustment2 s3 download failed",
		},
		{
			mockFunc: func() {
				mockS3Client.On("GetObject", mock.Anything, mock.Anything).Return(nil, nil).Once()
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(dummyError).Once()
			},
			req: dto.FileRequest{
				Type:           constant.Adjustment2,
				FileName:       "some_name",
				IdempotencyKey: uuid.NewString(),
			},
			expectedErr: errors.New("simulate error"),
			description: "Adjustment2 db update failed",
		},
		{
			mockFunc: func() {
				mockS3Client.On("GetObject", mock.Anything, mock.Anything).Return([]byte(happyFile), nil).Once()
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockTxnStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, dummyError).Once()
			},
			req: dto.FileRequest{
				Type:           constant.Adjustment2,
				FileName:       "some_name",
				IdempotencyKey: uuid.NewString(),
			},
			expectedErr: nil,
			description: "Adjustment2 db FindOnSlave error",
		},
		{
			mockFunc: func() {
				mockS3Client.On("GetObject", mock.Anything, mock.Anything).Return([]byte(happyFile), nil).Once()
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockTxnStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, dummyError).Once()
			},
			req: dto.FileRequest{
				Type:           constant.JV,
				FileName:       "some_name",
				IdempotencyKey: uuid.NewString(),
			},
			expectedErr: nil,
			description: "JV db FindOnSlave error",
		},
	}

	for _, test := range tests {
		description := test.description
		test.mockFunc()
		nextCtx, err := workFlowObj.processFile(context.Background(), "", &file.ExecutionData{
			State: stFileFetched,
			Req:   &test.req,
			File: storage.File{
				ID:        123,
				Status:    string(constant.FileInProcessing),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
		}, nil)
		if test.expectedErr == nil {
			assert.NoError(t, err, description)
			assert.Equal(t, stSuccess, nextCtx.GetState(), description)
		} else {
			assert.Equal(t, test.expectedErr, err, description)
			assert.Nil(t, nextCtx, description)
		}
	}
}
