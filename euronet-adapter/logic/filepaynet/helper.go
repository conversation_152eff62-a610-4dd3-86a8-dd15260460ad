package filepaynet

import (
	"context"
	"fmt"
	"io"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/s3utils"
	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// TypeToS3DirectoryMap map file type to directory path in S3 server
var TypeToS3DirectoryMap = map[constant.FileType]string{
	constant.Clearing:      "Clearing",
	constant.Adjustment2:   "Unrecon",
	constant.CBSPaynetDump: "CBS",
	constant.FetchOnly:     "Unrecon",
	constant.JV:            "JV",
	constant.FetchOnlyS3:   "CBS/Dispute_Chargeback/Incoming",
}

// TypeToSFTPDirectoryMap map file type to directory path in SFTP server
var TypeToSFTPDirectoryMap = map[constant.FileType]string{
	constant.Clearing:      "Paynet/Clearing",
	constant.Adjustment2:   "Paynet/Unrecon",
	constant.CBSPaynetDump: "Paynet/CBS",
	constant.FetchOnly:     "Paynet/Unrecon",
	constant.JV:            "Paynet/JV",
	constant.FetchOnlyS3:   "Paynet/CBS/Dispute_Chargeback/Incoming",
}

// FetchFromS3 ...
func FetchFromS3(ctx context.Context, logTag string, key string) ([]byte, error) {
	body, err := s3utils.S3Client.GetObject(s3utils.ENAdapterS3Bucket, key)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error featching %s file from S3", key), slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("successfully featched %s file from S3", key))
	return body, nil
}

// PushToS3 ...
func PushToS3(ctx context.Context, logTag string, file io.ReadSeeker, key string) error {
	if err := s3utils.S3Client.PutObject(s3utils.ENAdapterS3Bucket, key, file, s3client.WithServerSideEncryptionForPutObject(s3client.ServerSideEncryptionAES256)); err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error pushing %s file to S3", key), slog.Error(err))
		return err
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("successfully pushed %s file to S3", key))
	return nil
}
