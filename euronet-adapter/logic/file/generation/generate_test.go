package generation

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"os"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage/dbtype"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestGenerate(t *testing.T) {
	configFile, err := os.CreateTemp("", "config")
	assert.NoError(t, err)
	os.Setenv("SERVICE_CONF", configFile.Name())
	_, err = configFile.WriteString("{\"featureFlags\":{\"enableWorker\":true,\"enableActivationCodeCheck\":true},\"timeZone\":\"Asia/Singapore\"}")
	assert.NoError(t, err)
	secretsDir, err := os.MkdirTemp("", "secret")
	assert.NoError(t, err)
	os.Setenv("SECRET_CONF", secretsDir)

	dummyFileName := "DBMY_CBS_YYYYMMDD.csv"
	mockCardTransaction := &storage.MockICardTransactionDAO{}
	storage.CardTransactionD = mockCardTransaction
	mockRefundTransaction := &storage.MockIRefundDAO{}
	storage.RefundD = mockRefundTransaction
	mockFileDao := &storage.MockIFileDAO{}
	storage.FileD = mockFileDao
	tests := []struct {
		execData    *file.ExecutionData
		expectedErr error
		description string
		mockFunc    func()
	}{
		{
			execData: &file.ExecutionData{
				Req: &dto.FileRequest{
					Type:           "random_type",
					FileName:       dummyFileName,
					IdempotencyKey: "",
				},
			},
			expectedErr: nil,
			description: "failed and skipped with wrong type",
			mockFunc:    func() {},
		},
		{
			execData: &file.ExecutionData{
				Req: &dto.FileRequest{
					Type:           constant.CBSMasterCardDump,
					FileName:       dummyFileName,
					IdempotencyKey: "",
				},
			},
			expectedErr: &time.ParseError{
				Layout: constant.FormatYYYYMMDD,
				Value:  "YYYMMDD",
			},
			description: "failed wth wrong file name",
			mockFunc:    func() {},
		},
		{
			execData: &file.ExecutionData{
				Req: &dto.FileRequest{
					Type:           constant.CBSMasterCardDump,
					FileName:       common.GenerateCBSDumpFileName(constant.Mastercard, ""),
					IdempotencyKey: "",
				},
			},
			description: "happy flow",
			mockFunc: func() {
				merchantInfo := &dbtype.JSONMerchantInfo{
					TerminalID:   "123",
					MerchantID:   "123",
					TerminalName: "123",
				}

				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{
						{
							ID:                       0,
							Category:                 string(constant.TransactionCategoryATM),
							SubCategory:              string(logic.TransactionSubCategoryDefault),
							TransactionType:          string(constant.TransactionTypeDirectDebit),
							RequestID:                uuid.NewString(),
							MerchantInfo:             merchantInfo,
							TransmissionDatetime:     time.Now(),
							LocalTransactionDatetime: time.Now(),
							CreatedAt:                time.Now(),
							UpdatedAt:                time.Now(),
							ValuedAt:                 time.Now(),
						},
					}, nil)
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
				mockFileDao.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
			},
		},
	}
	workflowImpl := &WorkflowImpl{}

	for _, test := range tests {
		description := test.description
		test.mockFunc()
		nextCtx, err := workflowImpl.generate(context.Background(), "", test.execData, nil)
		if test.expectedErr == nil {
			assert.NoError(t, err, description)
		} else {
			assert.NotNil(t, err, description)
			assert.Nil(t, nextCtx, description)
		}
		os.Remove(dummyFileName)
	}
}

func setDefaultConfig() {
	configFile, _ := os.CreateTemp("", "config")
	os.Setenv("SERVICE_CONF", configFile.Name())
	configFile.WriteString("{\"featureFlags\":{\"enableWorker\":true,\"enableActivationCodeCheck\":true},\"timeZone\":\"Asia/Singapore\"}")
	secretsDir, _ := os.MkdirTemp("", "secret")
	os.Setenv("SECRET_CONF", secretsDir)
}

func TestLoadCBSDataIntoTmp(t *testing.T) {
	setDefaultConfig()
	baseDate, _ := time.Parse(constant.FormatYYYYMMDD, "20240101")
	tests := []struct {
		description  string
		req          *dto.FileRequest
		expectedErr  error
		mockFunc     func(*storage.MockICardTransactionDAO, *storage.MockIRefundDAO)
		validateFunc func(*testing.T, [][]string)
	}{
		{
			req: &dto.FileRequest{
				Type:           constant.CBSMasterCardDump,
				FileName:       common.GenerateCBSDumpFileName(constant.Mastercard, ""),
				IdempotencyKey: uuid.NewString(),
			},
			expectedErr: nil,
			description: "happy flow",
			mockFunc: func(mockCardTransaction *storage.MockICardTransactionDAO, mockRefundTransaction *storage.MockIRefundDAO) {
				merchantInfo := &dbtype.JSONMerchantInfo{
					TerminalID:   "123",
					MerchantID:   "123",
					TerminalName: "123",
				}
				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{
						{
							ID:                       0,
							Category:                 string(constant.TransactionCategoryATM),
							SubCategory:              string(logic.TransactionSubCategoryDefault),
							TransactionType:          string(constant.TransactionTypeDirectDebit),
							RequestID:                uuid.NewString(),
							MerchantInfo:             merchantInfo,
							TransmissionDatetime:     time.Now(),
							LocalTransactionDatetime: time.Now(),
							CreatedAt:                time.Now(),
							UpdatedAt:                time.Now(),
							ValuedAt:                 time.Now(),
						},
					}, nil)
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
			},
		},
		{
			req: &dto.FileRequest{
				Type:           constant.CBSMasterCardDump,
				FileName:       "DBMY_CBS_YYYYMMDD.csv",
				IdempotencyKey: uuid.NewString(),
			},
			expectedErr: &time.ParseError{
				Layout: constant.FormatYYYYMMDD,
				Value:  "YYYMMDD",
			},
			description: "invalid filename",
			mockFunc: func(mockCardTransaction *storage.MockICardTransactionDAO, mockRefundTransaction *storage.MockIRefundDAO) {
				merchantInfo := &dbtype.JSONMerchantInfo{
					TerminalID:   "123",
					MerchantID:   "123",
					TerminalName: "123",
				}
				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{
						{
							ID:                       0,
							Category:                 string(constant.TransactionCategoryATM),
							SubCategory:              string(logic.TransactionSubCategoryDefault),
							TransactionType:          string(constant.TransactionTypeDirectDebit),
							RequestID:                uuid.NewString(),
							MerchantInfo:             merchantInfo,
							TransmissionDatetime:     time.Now(),
							LocalTransactionDatetime: time.Now(),
							CreatedAt:                time.Now(),
							UpdatedAt:                time.Now(),
							ValuedAt:                 time.Now(),
						},
					}, nil)
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
			},
		},
		{
			req: &dto.FileRequest{
				Type:           constant.CBSMasterCardDump,
				FileName:       "20240101/DBMY_CBS_20240101.csv",
				IdempotencyKey: uuid.NewString(),
			},
			expectedErr: nil,
			description: "happy flow with refund",
			mockFunc: func(mockCardTransaction *storage.MockICardTransactionDAO, mockRefundTransaction *storage.MockIRefundDAO) {
				merchantInfo := &dbtype.JSONMerchantInfo{
					TerminalID:   "123",
					MerchantID:   "123",
					TerminalName: "123",
				}
				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{
						{
							ID:                       0,
							Category:                 string(constant.TransactionCategoryATM),
							SubCategory:              string(logic.TransactionSubCategoryDefault),
							TransactionType:          string(constant.TransactionTypeDirectDebit),
							RequestID:                uuid.NewString(),
							MerchantInfo:             merchantInfo,
							TransmissionDatetime:     baseDate,
							CreatedAt:                baseDate.Add(time.Minute),
							UpdatedAt:                baseDate.Add(2 * time.Minute),
							ValuedAt:                 baseDate.Add(3 * time.Minute),
							LocalTransactionDatetime: baseDate.Add(24 * time.Hour),
						},
					}, nil).Times(5)
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.Refund{
						{
							ID:                       100,
							InternalTransactionID:    "refund-001",
							CreatedAt:                baseDate.Add(time.Minute),
							UpdatedAt:                baseDate.Add(2 * time.Minute),
							ValuedAt:                 baseDate.Add(3 * time.Minute),
							LocalTransactionDatetime: baseDate.Add(24 * time.Hour),
						},
					}, nil).Once()
			},
			validateFunc: func(t *testing.T, content [][]string) {
				assert.Equal(t, 8, len(content), "# of rows including header/footer")
				assert.Equal(t, "20240101", content[1][1], "record 1 (debit) settlement time")
				assert.Equal(t, "20240102", content[1][2], "record 1 (debit) local txn datetime")
				assert.Equal(t, "D", content[1][9], "record 1 (debit) C/D indicator")
				assert.Equal(t, "20240101", content[6][1], "record 6 (refund) settlement time")
				assert.Equal(t, "20240102", content[6][2], "record 6 (refund) local txn datetime")
				assert.Equal(t, "refund-001", content[6][3], "record 6 (refund) charge id")
				assert.Equal(t, "C", content[6][9], "record 6 (refund) C/D indicator")
			},
		},
	}
	for index, test := range tests {
		description := test.description
		scenario := test
		testIndex := index
		t.Run(description, func(t *testing.T) {
			dummyFileName := fmt.Sprintf("test_%d.csv", testIndex)
			dummyFile, _ := os.OpenFile(dummyFileName, os.O_TRUNC|os.O_WRONLY|os.O_CREATE, 0600)
			defer func(file *os.File) {
				file.Close()
				os.Remove(file.Name())
			}(dummyFile)

			mockCardTransaction := &storage.MockICardTransactionDAO{}
			storage.CardTransactionD = mockCardTransaction
			mockRefundTransaction := &storage.MockIRefundDAO{}
			storage.RefundD = mockRefundTransaction
			scenario.mockFunc(mockCardTransaction, mockRefundTransaction)

			err := loadCBSDataIntoTmp(context.Background(), dummyFile, scenario.req, []string{}, []string{})
			if scenario.expectedErr == nil {
				assert.NoError(t, err, description)
				if scenario.validateFunc != nil {
					reader, readErr := os.OpenFile(dummyFileName, os.O_APPEND|os.O_RDONLY, 0600)
					defer func(file *os.File) {
						reader.Close()
						os.Remove(reader.Name())
					}(reader)
					assert.NoError(t, readErr)
					csvReader := csv.NewReader(reader)
					csvReader.FieldsPerRecord = -1
					if content, csvErr := csvReader.ReadAll(); csvErr == nil {
						scenario.validateFunc(t, content)
					} else {
						assert.Fail(t, "unexpected error when reading dummy csv: %s", csvErr.Error())
					}
				}
			} else {
				assert.NotNil(t, err, description)
			}
		})
	}
}

func TestLoadDataIntoCSVFile(t *testing.T) {
	dummyErr := errors.New("simulate error")
	queries := [][]data.Condition{
		{ //ATM txns
			data.EqualTo("Category", constant.TransactionCategoryATM),
			data.EqualTo("TransactionType", constant.TransactionTypeDirectDebit),
			data.EqualTo("Status", logic.Completed),
			data.GreaterThanOrEqualTo("ValuedAt", time.Now().Add(-24*time.Hour)),
			data.LessThan("ValuedAt", time.Now()),
		},
	}
	treatmentArr := []string{
		file.Debit, //ATM txns
	}
	refundQueries := [][]data.Condition{
		{
			data.EqualTo("Status", logic.Completed),
			data.EqualTo("FromFile", false),
			data.GreaterThanOrEqualTo("ValuedAt", time.Now().Add(-24*time.Hour)),
			data.LessThan("ValuedAt", time.Now()),
		},
	}
	mockCardTransaction := &storage.MockICardTransactionDAO{}
	storage.CardTransactionD = mockCardTransaction
	mockRefundTransaction := &storage.MockIRefundDAO{}
	storage.RefundD = mockRefundTransaction
	tests := []struct {
		execData                       *file.ExecutionData
		expectedErr                    error
		description                    string
		mockFunc                       func()
		expectedInternalTransactionIds []string
	}{
		{
			expectedErr: dummyErr,
			description: "db error",
			mockFunc: func() {
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, dummyErr).Once()
			},
		},
		{
			expectedErr: nil,
			description: "no data",
			mockFunc: func() {
				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
			},
		},
		{
			expectedErr: nil,
			description: "deserialization error",
			mockFunc: func() {
				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{
						{
							ID:                       0,
							Category:                 string(constant.TransactionCategoryATM),
							SubCategory:              string(logic.TransactionSubCategoryDefault),
							TransactionType:          string(constant.TransactionTypeDirectDebit),
							RequestID:                uuid.NewString(),
							MTI:                      "",
							ProxyNumber:              "",
							ProcessingCode:           "",
							PanEntryMode:             "",
							RetrievalReferenceNumber: "",
							CardSchemeTransactionID:  "",
							EuronetTransactionID:     "",
							InternalTransactionID:    "",
							AuthorizationID:          "",
							MerchantInfo:             nil,
							TransmissionDatetime:     time.Now(),
							LocalTransactionDatetime: time.Now(),
							CreatedAt:                time.Now(),
							UpdatedAt:                time.Now(),
							ValuedAt:                 time.Now(),
						},
					}, nil).Once()
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(nil, data.ErrNoData).Once()
			},
			expectedInternalTransactionIds: []string{""},
		},
		{
			expectedErr: nil,
			description: "happy flow",
			mockFunc: func() {
				merchantInfo := &dbtype.JSONMerchantInfo{
					TerminalID:   "123",
					MerchantID:   "123",
					TerminalName: "123",
				}
				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{
						{
							ID:                       0,
							Category:                 string(constant.TransactionCategoryATM),
							SubCategory:              string(logic.TransactionSubCategoryDefault),
							TransactionType:          string(constant.TransactionTypeDirectDebit),
							RequestID:                uuid.NewString(),
							MTI:                      "",
							ProxyNumber:              "",
							ProcessingCode:           "",
							PanEntryMode:             "",
							RetrievalReferenceNumber: "",
							CardSchemeTransactionID:  "",
							EuronetTransactionID:     "",
							InternalTransactionID:    "charge-id",
							AuthorizationID:          "",
							MerchantInfo:             merchantInfo,
							TransmissionDatetime:     time.Now(),
							LocalTransactionDatetime: time.Now(),
							CreatedAt:                time.Now(),
							UpdatedAt:                time.Now(),
							ValuedAt:                 time.Now(),
						},
					}, nil).Once()
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.Refund{
						{
							ID:                            0,
							OriginalInternalTransactionID: "",
							ProxyNumber:                   "",
							RequestAmount:                 0,
							RequestCurrency:               "",
							OriginalAmount:                0,
							OriginalCurrency:              "",
							RefundAmount:                  0,
							RetrievalReferenceNumber:      "",
							CardSchemeTransactionID:       "",
							InternalTransactionID:         "refund-id",
							Status:                        "",
							StatusReason:                  "",
							RefundProperties:              nil,
							CreatedAt:                     time.Time{},
							UpdatedAt:                     time.Time{},
							ValuedAt:                      time.Time{},
							AcquirerReferenceData:         "",
							AcquirerID:                    "",
							MerchantInfo:                  nil,
							EuronetTransactionID:          "",
							AuthorizationID:               "",
							ProcessingCode:                "",
							RequestBody:                   nil,
							ResponseBody:                  nil,
							FromFile:                      false,
						},
					}, nil).Once()
			},
			expectedInternalTransactionIds: []string{"charge-id", "refund-id"},
		},
		{
			description: "auto-reversal refunds should be excluded",
			expectedErr: nil,
			mockFunc: func() {
				merchantInfo := &dbtype.JSONMerchantInfo{
					TerminalID:   "123",
					MerchantID:   "123",
					TerminalName: "123",
				}
				mockRefundTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.Refund{
						{
							ID:                            101,
							OriginalInternalTransactionID: "not-auto-reversal-debit-charge-id",
							ProxyNumber:                   "",
							RequestAmount:                 0,
							RequestCurrency:               "",
							OriginalAmount:                0,
							OriginalCurrency:              "",
							RefundAmount:                  0,
							RetrievalReferenceNumber:      "",
							CardSchemeTransactionID:       "",
							InternalTransactionID:         "not-auto-reversal-refund-id-1",
							Status:                        "",
							StatusReason:                  "",
							RefundProperties:              nil,
							CreatedAt:                     time.Time{},
							UpdatedAt:                     time.Time{},
							ValuedAt:                      time.Time{},
							AcquirerReferenceData:         "",
							AcquirerID:                    "",
							MerchantInfo:                  nil,
							EuronetTransactionID:          "",
							AuthorizationID:               "",
							ProcessingCode:                "",
							RequestBody:                   nil,
							ResponseBody:                  nil,
							FromFile:                      false,
						},
						{
							ID:                            102,
							OriginalInternalTransactionID: "auto-reversal-debit-charge-id",
							ProxyNumber:                   "",
							RequestAmount:                 0,
							RequestCurrency:               "",
							OriginalAmount:                0,
							OriginalCurrency:              "",
							RefundAmount:                  0,
							RetrievalReferenceNumber:      "",
							CardSchemeTransactionID:       "",
							InternalTransactionID:         "auto-reversal-refund-id-1",
							Status:                        "",
							StatusReason:                  "",
							RefundProperties:              &dbtype.RefundProperties{RefundSource: "AUTO_REVERSAL"},
							CreatedAt:                     time.Time{},
							UpdatedAt:                     time.Time{},
							ValuedAt:                      time.Time{},
							AcquirerReferenceData:         "",
							AcquirerID:                    "",
							MerchantInfo:                  nil,
							EuronetTransactionID:          "",
							AuthorizationID:               "",
							ProcessingCode:                "",
							RequestBody:                   nil,
							ResponseBody:                  nil,
							FromFile:                      false,
						},
					}, nil).Once()
				mockCardTransaction.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return([]*storage.CardTransaction{
						{
							ID:                       1,
							Category:                 string(constant.TransactionCategoryATM),
							SubCategory:              string(logic.TransactionSubCategoryDefault),
							TransactionType:          string(constant.TransactionTypeDirectDebit),
							RequestID:                uuid.NewString(),
							MTI:                      "",
							ProxyNumber:              "",
							ProcessingCode:           "",
							PanEntryMode:             "",
							RetrievalReferenceNumber: "",
							CardSchemeTransactionID:  "",
							EuronetTransactionID:     "",
							InternalTransactionID:    "auto-reversal-debit-charge-id",
							AuthorizationID:          "",
							MerchantInfo:             merchantInfo,
							TransmissionDatetime:     time.Now(),
							LocalTransactionDatetime: time.Now(),
							CreatedAt:                time.Now(),
							UpdatedAt:                time.Now(),
							ValuedAt:                 time.Now(),
						},
						{
							ID:                       2,
							Category:                 string(constant.TransactionCategoryATM),
							SubCategory:              string(logic.TransactionSubCategoryDefault),
							TransactionType:          string(constant.TransactionTypeDirectDebit),
							RequestID:                uuid.NewString(),
							MTI:                      "",
							ProxyNumber:              "",
							ProcessingCode:           "",
							PanEntryMode:             "",
							RetrievalReferenceNumber: "",
							CardSchemeTransactionID:  "",
							EuronetTransactionID:     "",
							InternalTransactionID:    "not-auto-reversal-debit-charge-id",
							AuthorizationID:          "",
							MerchantInfo:             merchantInfo,
							TransmissionDatetime:     time.Now(),
							LocalTransactionDatetime: time.Now(),
							CreatedAt:                time.Now(),
							UpdatedAt:                time.Now(),
							ValuedAt:                 time.Now(),
						},
					}, nil).Once()
			},
			expectedInternalTransactionIds: []string{"not-auto-reversal-debit-charge-id", "not-auto-reversal-refund-id-1"},
		},
	}

	timeZone, _ := time.LoadLocation(constant.TimeZoneMalaysia)
	for index, test := range tests {
		scenario := test
		description := scenario.description
		t.Run(description, func(t *testing.T) {
			dummyFileName := fmt.Sprintf("test_%d.csv", index)
			dummyFile, _ := os.OpenFile(dummyFileName, os.O_TRUNC|os.O_WRONLY|os.O_CREATE, 0600)
			defer func(file *os.File) {
				file.Close()
				os.Remove(file.Name())
			}(dummyFile)

			scenario.mockFunc()
			err := loadDataIntoCSVFile(context.Background(), dummyFile, queries, treatmentArr, refundQueries, timeZone)
			if scenario.expectedErr == nil {
				assert.NoError(t, err, description)
				reader, readErr := os.OpenFile(dummyFileName, os.O_APPEND|os.O_RDONLY, 0600)
				defer func(file *os.File) {
					file.Close()
				}(reader)
				assert.NoError(t, readErr)
				csvReader := csv.NewReader(reader)
				csvReader.FieldsPerRecord = -1
				if records, err := csvReader.ReadAll(); err != nil {
					assert.Fail(t, err.Error())
				} else {
					expectedRecords := len(scenario.expectedInternalTransactionIds) + 2
					assert.Equal(t, expectedRecords, len(records), "records length")
					if expectedRecords > 2 {
						for idx, id := range scenario.expectedInternalTransactionIds {
							assert.Equal(t, id, records[idx+1][3], fmt.Sprintf("internal_transaction_id for record %d", idx+1))
						}
					}
				}

			} else {
				assert.Equal(t, scenario.expectedErr, err, description)
			}
		})
	}
}
