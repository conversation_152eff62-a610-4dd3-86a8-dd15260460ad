package file

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetMerchantInfoFromMCAcceptorName(t *testing.T) {
	tests := []struct {
		name                    string
		cardAcceptorTermName    string
		wantMerchantName        string
		wantMerchantAddress     string
		wantMerchantCity        string
		wantMerchantCountryCode string
		wantNil                 bool
	}{
		{
			name:                    "happy path",
			cardAcceptorTermName:    "MUSOSHIN              \\FUNAIGUN KIYOUTANBACHIYOUKOMO                \\KIYOUTOFU    \\6220214   JPNJPN",
			wantMerchantName:        "MUSOSHIN",
			wantMerchantAddress:     "FUNAIGUN KIYOUTANBACHIYOUKOMO",
			wantMerchantCity:        "KIYOUTOFU",
			wantMerchantCountryCode: "JPN",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "MCDONALD'S HK 336\\UNIT NO  7T102 8T005A & 8T005 TERMINAL 1\\HONG KONG\\UNKNOWN      HKG",
			wantMerchantName:        "MCDONALD'S HK 336",
			wantMerchantAddress:     "UNIT NO  7T102 8T005A & 8T005 TERMINAL 1",
			wantMerchantCity:        "HONG KONG",
			wantMerchantCountryCode: "HKG",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "BUS/MRT 345493373\\9 MAZWELL ROAD\\SINGAPORE\\069112    SG SGP",
			wantMerchantName:        "BUS/MRT 345493373",
			wantMerchantAddress:     "9 MAZWELL ROAD",
			wantMerchantCity:        "SINGAPORE",
			wantMerchantCountryCode: "SGP",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "STEAM PURCHASE        \\Rodingsmarkt 9                               \\SEATTLE      \\20459     HH DEU",
			wantMerchantName:        "STEAM PURCHASE",
			wantMerchantAddress:     "Rodingsmarkt 9",
			wantMerchantCity:        "SEATTLE",
			wantMerchantCountryCode: "DEU",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "GENTING PREMIUM O\\Suite Nos.224 High Street Genting Premium\\Pahang\\69000     MYSMYS",
			wantMerchantName:        "GENTING PREMIUM O",
			wantMerchantAddress:     "Suite Nos.224 High Street Genting Premium",
			wantMerchantCity:        "Pahang",
			wantMerchantCountryCode: "MYS",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "GetGo\\1094 Lower Delta Road\\INTERNET\\169205       SGP",
			wantMerchantName:        "GetGo",
			wantMerchantAddress:     "1094 Lower Delta Road",
			wantMerchantCity:        "INTERNET",
			wantMerchantCountryCode: "SGP",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "U STARS SUPERMARKET (8\\(#01-65 BLK 878C TAMPINES AVE 8)\\Singapore\\758273       SGP",
			wantMerchantName:        "U STARS SUPERMARKET (8",
			wantMerchantAddress:     "(#01-65 BLK 878C TAMPINES AVE 8)",
			wantMerchantCity:        "Singapore",
			wantMerchantCountryCode: "SGP",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "AEON CO-QUEENSBAY MALL\\1F-61 QUEENSBAY MALL\\BAYAN LEPAS\\11900     03 MYS",
			wantMerchantName:        "AEON CO-QUEENSBAY MALL",
			wantMerchantAddress:     "1F-61 QUEENSBAY MALL",
			wantMerchantCity:        "BAYAN LEPAS",
			wantMerchantCountryCode: "MYS",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "PAYPAL *MOBOREADERT\\100 N HOWARD ST STE R, SP\\13665057167\\992010508 WA USA",
			wantMerchantName:        "PAYPAL *MOBOREADERT",
			wantMerchantAddress:     "100 N HOWARD ST STE R, SP",
			wantMerchantCity:        "13665057167",
			wantMerchantCountryCode: "USA",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "HK DISNEYLAND       56\\FRONT DESK\\HONG KONG\\UNKNOWN      HKG",
			wantMerchantName:        "HK DISNEYLAND       56",
			wantMerchantAddress:     "FRONT DESK",
			wantMerchantCity:        "HONG KONG",
			wantMerchantCountryCode: "HKG",
		},
		{
			name:                    "happy path",
			cardAcceptorTermName:    "MAXIM'S CAKES (3511)  \\(MAXMI'S CAKES)(3511)              \\DISNEYLAND   \\             HKG",
			wantMerchantName:        "MAXIM'S CAKES (3511)",
			wantMerchantAddress:     "(MAXMI'S CAKES)(3511)",
			wantMerchantCity:        "DISNEYLAND",
			wantMerchantCountryCode: "HKG",
		},
		{
			name:    "happy path - no term name",
			wantNil: true,
		},
		{
			name:                 "happy path - bad term name delimiting",
			cardAcceptorTermName: "SOMEBAD TERMNAME \\ BADBAD SGP",
			wantNil:              true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotMerchantInfo := GetMerchantInfoFromClearingAcceptorName(tt.cardAcceptorTermName)
			if tt.wantNil {
				assert.Nil(t, gotMerchantInfo, tt.name)
			}
			if gotMerchantInfo != nil {
				assert.Equalf(t, tt.wantMerchantName, gotMerchantInfo.MerchantName, "GetMerchantInfoFromMCAcceptorName(%v)", tt.name)
				assert.Equalf(t, tt.wantMerchantAddress, gotMerchantInfo.MerchantAddress, "GetMerchantInfoFromMCAcceptorName(%v)", tt.name)
				assert.Equalf(t, tt.wantMerchantCity, gotMerchantInfo.MerchantCity, "GetMerchantInfoFromMCAcceptorName(%v)", tt.name)
				assert.Equalf(t, tt.wantMerchantCountryCode, gotMerchantInfo.MerchantCardAcceptorCountryCode, "GetMerchantInfoFromMCAcceptorName(%v)", tt.name)
			}
		})
	}
}
