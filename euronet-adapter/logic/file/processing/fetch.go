package processing

import (
	"bytes"
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/stats"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/constants"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

// fetchFile ...
func (w *WorkflowImpl) fetchFile(ctx context.Context, transitionID string, exec workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*file.ExecutionData)
	if !ok {
		return nil, logic.ErrInvalidContext
	}

	fileType := currCtx.Req.Type
	switch fileType {
	case constant.FetchOnlyS3:
		nextCtx, err := w.fetchFromS3(ctx, exec)
		if err != nil {
			slog.FromContext(ctx).Error(logTag, "failed to upload file from s3 server", slog.Error(err))
			return nil, err
		}
		return nextCtx, nil

	default:
		nextCtx, err := w.fetchFromSFTP(ctx, exec)
		if err != nil {
			slog.FromContext(ctx).Error(logTag, "failed to upload file from sftp server", slog.Error(err))
			return nil, err
		}
		return nextCtx, nil
	}
}

// fetchFromS3 Fetch from S3, push to SFTP
func (w *WorkflowImpl) fetchFromS3(ctx context.Context, exec workflowengine.ExecutionData) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*file.ExecutionData)
	if !ok {
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()

	// DOWNLOAD FROM S3
	s3Directory := nextCtx.Req.S3Directory
	if s3Directory == "" {
		dir, found := file.TypeToS3DirectoryMap[nextCtx.Req.Type]
		if found {
			s3Directory = dir
		} else {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("S3 directory not found for job type %s.", nextCtx.Req.Type))
		}
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("S3 directory found for %s.", nextCtx.Req.FileName), slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))

	// sample: /CBS/Dispute_Chargeback/Incoming/YYYYMMDD/MC_daily_chargeback_tracker_response_YYYYMMDD.csv
	filePath := fmt.Sprintf("%s/%s", s3Directory, nextCtx.Req.FileName)
	content, err := file.FetchFromS3(ctx, logTag, filePath)
	if err != nil {
		if err == file.ErrFileNotFound {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("%s file not found, skipping ....", filePath),
				slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))
			nextCtx.State = stSkipped
			return nextCtx, nil
		}
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("failed to fetch file %s from S3", filePath), slog.Error(err))
		w.StatsD.Count1(logTag, string(nextCtx.Req.Type), fmt.Sprintf("%s:yes", constants.NotFoundTag))
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("file %s fetch with size %d", filePath, len(content)))

	// PUSH TO SFTP
	enSFTPDirectory := nextCtx.Req.ENSFTPDirectory
	// follow standard directoy convention if EN sftp directoy isn't set in config
	if enSFTPDirectory == "" {
		dir, found := file.TypeToSFTPDirectoryMap[nextCtx.Req.Type]
		if found {
			enSFTPDirectory = dir
		} else {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("EN SFTP directory not found for %s.", nextCtx.Req.FileName), slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))
		}
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("EN SFTP directory found for %s.", nextCtx.Req.FileName), slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))

	sftpFilePath := fmt.Sprintf("%s/%s", enSFTPDirectory, nextCtx.Req.FileName)
	if err = w.SftpClient.Upload(ctx, content, sftpFilePath); err != nil {
		stats.StatsDClient.Count1(
			logPersistFailure,
			string(constant.FetchOnly),
			fmt.Sprintf("failed pushing to SFTP path: %s", sftpFilePath),
		)
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("failed to upload file %s to sftp server", sftpFilePath), slog.Error(err))
		return nil, err
	}
	nextCtx.State = stSuccess
	nextCtx.File.Status = string(constant.FileProcessingSuccess)
	nextCtx.File.UpdatedAt = time.Now()

	if err = storage.FileD.UpdateEntity(ctx, &currCtx.File, &nextCtx.File); err != nil {
		slog.FromContext(ctx).Error(logTag, "error updating file processing status", slog.Error(err))
		return nil, err
	}

	return nextCtx, nil
}

// fetchFromSFTP Fetch from SFTP, push to S3
func (w *WorkflowImpl) fetchFromSFTP(ctx context.Context, exec workflowengine.ExecutionData) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*file.ExecutionData)
	if !ok {
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	enSFTPDirectory := nextCtx.Req.ENSFTPDirectory
	// follow standard directoy convention if EN sftp directoy isn't set in config
	if enSFTPDirectory == "" {
		dir, found := file.TypeToSFTPDirectoryMap[nextCtx.Req.Type]
		if found {
			enSFTPDirectory = dir
		} else {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("EN SFTP directory not found for %s.", nextCtx.Req.FileName), slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))
		}
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("EN SFTP directory found for %s.", nextCtx.Req.FileName), slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))

	// sample: Mastercard/Unrecon/YYYYMMDD/filename_YYYYMMDD.csv
	filePath := fmt.Sprintf("%s/%s", enSFTPDirectory, nextCtx.Req.FileName)
	content, err := w.SftpClient.Download(ctx, filePath)
	if err != nil {
		if err == file.ErrFileNotFound && (nextCtx.Req.Type == constant.Adjustment2 || nextCtx.Req.Type == constant.FetchOnly) { // no occurrence
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("%s file not found, skipping ....", filePath),
				slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))
			nextCtx.State = stSkipped
			return nextCtx, nil
		}
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("failed to fetch %s file from sftp server", filePath), slog.Error(err))
		w.StatsD.Count1(logTag, string(nextCtx.Req.Type), fmt.Sprintf("%s:yes", constants.NotFoundTag))
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("file %s fetch with size %d", filePath, len(content)))
	reader := bytes.NewReader(content)

	s3Directory := nextCtx.Req.S3Directory
	if s3Directory == "" {
		dir, found := file.TypeToS3DirectoryMap[nextCtx.Req.Type]
		if found {
			s3Directory = dir
		} else {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("S3 directory not found for job type %s.", nextCtx.Req.Type))
		}
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("S3 directory found for %s.", nextCtx.Req.FileName), slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))

	s3FilePath := fmt.Sprintf("%s/%s", s3Directory, nextCtx.Req.FileName)
	if err = file.PushToS3(ctx, logTag, reader, s3FilePath); err != nil {
		// error is logged inside PushToS3 fn
		return nil, err
	}
	nextCtx.State = stFileFetched
	if nextCtx.Req.Type == constant.FetchOnly {
		nextCtx.State = stSuccess
		nextCtx.File.Status = string(constant.FileProcessingSuccess)
		nextCtx.File.UpdatedAt = time.Now()

		if err = storage.FileD.UpdateEntity(ctx, &currCtx.File, &nextCtx.File); err != nil {
			slog.FromContext(ctx).Error(logTag, "error updating file processing status", slog.Error(err), slog.CustomTag(constants.FileProcessTypeTag, nextCtx.Req.Type))
			return nil, err
		}
	}
	return nextCtx, nil
}
