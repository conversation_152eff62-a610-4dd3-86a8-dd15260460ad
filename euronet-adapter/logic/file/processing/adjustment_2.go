package processing

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"math"
	"strconv"
	"strings"
	"time"

	"gitlab.myteksi.net/dakota/common/currency"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/stats"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

func (w *WorkflowImpl) processAdjustmentScenario2File(ctx context.Context, reader io.Reader, fileName string) fileErrors {
	// https://gxsbank.atlassian.net/wiki/spaces/Digibank/pages/********/Adjustment+file+scenario+2
	csvReader := csv.NewReader(reader)
	csvReader.FieldsPerRecord = -1 // file is not having constant number of columns
	var lineNumber int
	fileErr := fileErrors(make(map[string]string))

	for {
		lineNumber++
		record, err := csvReader.Read()
		if err != nil {
			if err == io.EOF {
				break
			}
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error reading line %d from csv file", lineNumber), slog.Error(err))
			fileErr[strconv.Itoa(lineNumber)] = err.Error()
			// TODO: add stats
			continue
		}
		if record[0] == "Switch_Transaction Type" {
			slog.FromContext(ctx).Info(logTag, "reading header row")
			continue
		}
		adjustment2Record, err := constructAdjustmentScenario2Record(record, fileName)
		if err != nil {
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("could not contruct adjustment scenario 2 record at line %d", lineNumber), slog.Error(err))
			fileErr[strconv.Itoa(lineNumber)] = err.Error()
		} else if err1 := w.processAdjustmentScenario2Record(ctx, adjustment2Record); err1 != nil {
			//stats failed
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("could not process adjustment scenario 2 record at line %d", lineNumber), slog.Error(err1))
			fileErr[strconv.Itoa(lineNumber)] = err1.Error()
		}
		time.Sleep(time.Second) //TODO: to refactor to a better method
	}
	slog.FromContext(ctx).Info(logTag, "finished processing adjustment scenario 2 file")
	return fileErr
}

func constructAdjustmentScenario2Record(record []string, fileName string) (*dto.AdjustmentScenario2Record, error) {
	if len(record) != file.Adjustment2FileRecordLength {
		return nil, errors.New("wrong number of columns")
	}
	txnCurrencyNumeric := record[6]
	txnAmountCents, err := convertAmountToCents(record[7], txnCurrencyNumeric)
	if err != nil {
		return nil, errors.New("failed to parse txn amount")
	}
	billingCurrencyNumeric := record[8]
	billingAmountCents, err := convertAmountToCents(record[9], billingCurrencyNumeric)
	if err != nil {
		return nil, errors.New("failed to parse billing amount")
	}
	return &dto.AdjustmentScenario2Record{
		FileName:        fileName,
		TransactionType: record[0],
		CardNumber:      record[1],
		ProxyNumber:     record[2],
		AuditNumber:     record[3],
		AuthCode:        record[4],
		RRN:             record[5],
		TxnCurrency:     txnCurrencyNumeric,
		TxnAmount:       txnAmountCents,
		BillingCurrency: billingCurrencyNumeric,
		BillingAmount:   billingAmountCents,
		TxnDate:         record[10],
		MerchantNameLoc: record[11],
		JulianDate:      record[12],
	}, nil
}

func (w *WorkflowImpl) processAdjustmentScenario2Record(ctx context.Context, record *dto.AdjustmentScenario2Record) error {
	query := []data.Condition{
		data.EqualTo("RetrievalReferenceNumber", strings.TrimSpace(record.RRN)),
		data.EqualTo("ProxyNumber", strings.TrimSpace(record.ProxyNumber)),
		data.EqualTo("AuthorizationID", strings.TrimSpace(record.AuthCode)),
		data.Limit(1),
	}
	originalTxn, err := storage.CardTransactionD.FindOnSlave(ctx, query...)
	if err != nil {
		if err == data.ErrNoData {
			slog.FromContext(ctx).Error(logTag, "original auth not found", slog.Error(err))
			//TODO: stats here
			return err
		}
		slog.FromContext(ctx).Error(logTag, "CardTransactionD.FindOnSlave error", slog.Error(err))
		return err
	}
	return w.triggerCancelEvent(ctx, originalTxn[0].RequestID, record, originalTxn[0].InternalTransactionID)
}

func saveFailedAdjustmentScenario2(ctx context.Context, record *dto.AdjustmentScenario2Record, statusReason string, internalTxnID string, originalRequestID string) {
	newEntry := &storage.AdjustmentScenarioFailure{
		TransactionType:          record.TransactionType,
		CardNumber:               record.CardNumber,
		ProxyNumber:              record.ProxyNumber,
		TxnAuditNumber:           record.AuditNumber,
		AuthCode:                 record.AuthCode,
		RetrievalReferenceNumber: record.RRN,
		TxnCurrency:              record.TxnCurrency,
		TxnAmount:                record.TxnAmount,
		BillingCurrency:          record.BillingCurrency,
		BillingAmount:            record.BillingAmount,
		TxnDate:                  record.TxnDate,
		MerchantNameLoc:          record.MerchantNameLoc,
		JulianDate:               record.JulianDate,
		Status:                   constant.Failed,
		StatusReason:             statusReason,
		InternalTransactionID:    internalTxnID,
		OriginalRequestID:        originalRequestID,
		Source:                   record.FileName,
		CreatedAt:                time.Now(),
		UpdatedAt:                time.Now(),
	}

	if err := storage.AdjustmentScenarioFailureD.Save(ctx, newEntry); err != nil {
		slog.FromContext(ctx).Warn(logPersistFailure, "failed to save auth expiry failure", slog.Error(err), slog.CustomTag("record", record),
			slog.CustomTag("internal_txn_id", internalTxnID), slog.CustomTag("original_request_id", originalRequestID))
		stats.StatsDClient.Count1(logPersistFailure, string(constant.Adjustment2), fmt.Sprintf("failure_reason:%s", statusReason),
			fmt.Sprintf("persist_status:%s", constant.Failed), fmt.Sprintf("error:%s", err.Error()))
	} else {
		stats.StatsDClient.Count1(logPersistFailure, string(constant.Adjustment2), fmt.Sprintf("failure_reason:%s", statusReason),
			fmt.Sprintf("persist_status:%s", constant.Success))
	}
}

func convertAmountToCents(amount, currencyNumeric string) (int64, error) {
	txnCurrency := currency.GetByNumericCode(currencyNumeric)
	txnAmount, err := strconv.ParseFloat(amount, 64)
	if err != nil {
		return 0, errors.New("failed to parse txn amount")
	}
	txnAmountCents := txnAmount * math.Pow(10, float64(txnCurrency.Exponent))
	return int64(txnAmountCents), nil
}
