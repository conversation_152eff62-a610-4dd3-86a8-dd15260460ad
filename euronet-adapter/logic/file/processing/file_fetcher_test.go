package processing

import (
	"context"
	"errors"
	"fmt"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/service/s3"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/mocks"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/sftputils"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/s3utils"
	"gitlab.myteksi.net/dakota/common/aws/s3client"
)

func TestSftpToS3FileFetcher(t *testing.T) {

	//scenarios
	today := time.Now().Format(constant.FormatYYYYMMDD)
	getTodayFileName := func(base string, ext string) string {
		if ext != "" {
			return fmt.Sprintf("%s_%s.%s", base, today, ext)
		}
		return fmt.Sprintf("%s_%s", base, today)
	}

	errDummy := errors.New("simulate error")
	scenarios := []struct {
		req                       dto.FileRequestV2
		listFilesNames            []string
		errListFiles              error
		errDownload               map[string]error
		errPutObject              map[string]error
		expectingListFiles        bool
		expectingDownload         bool
		expectingPutObject        bool
		expectedDownloadFileNames []string
		expectedErr               error
		expectedStatDTag          string
		description               string
	}{
		{
			req: dto.FileRequestV2{
				Name:                 "sftp dir not set",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:     nil,
			errListFiles:       nil,
			errDownload:        nil,
			errPutObject:       nil,
			expectingListFiles: false,
			expectingDownload:  false,
			expectingPutObject: false,
			expectedErr:        fmt.Errorf("EN sftp directory not set"),
			description:        "sftp dir not set",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "s3 dir not set",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:     nil,
			errListFiles:       nil,
			errDownload:        nil,
			errPutObject:       nil,
			expectingListFiles: false,
			expectingDownload:  false,
			expectingPutObject: false,
			expectedErr:        fmt.Errorf("s3 directory not set"),
			description:        "s3 dir not set",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3 directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:     nil,
			errListFiles:       nil,
			errDownload:        nil,
			errPutObject:       nil,
			expectingListFiles: false,
			expectingDownload:  false,
			expectingPutObject: false,
			expectedErr:        fmt.Errorf("name not set"),
			description:        "name not set",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "SFTP ListFiles failed",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:     nil,
			errListFiles:       errDummy,
			errDownload:        nil,
			errPutObject:       nil,
			expectingListFiles: true,
			expectingDownload:  false,
			expectingPutObject: false,
			expectedErr:        errDummy,
			expectedStatDTag:   "error:simulate error",
			description:        "SFTP ListFiles failed",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "SFTP ListFiles no file",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:     []string{},
			errListFiles:       nil,
			errDownload:        nil,
			errPutObject:       nil,
			expectingListFiles: true,
			expectingDownload:  false,
			expectingPutObject: false,
			expectedErr:        nil,
			expectedStatDTag:   "not_found:yes",
			description:        "SFTP ListFiles no file",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "FTP file not found during download",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:            []string{getTodayFileName("filename", "ext")},
			errListFiles:              nil,
			errDownload:               map[string]error{getTodayFileName("filename", "ext"): file.ErrFileNotFound},
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        false,
			expectedDownloadFileNames: []string{getTodayFileName("filename", "ext")},
			expectedErr:               nil,
			expectedStatDTag:          "not_found:yes",
			description:               "SFTP file not found during download",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "download from SFTP failed",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:            []string{getTodayFileName("filename", "ext")},
			errListFiles:              nil,
			errDownload:               map[string]error{getTodayFileName("filename", "ext"): errDummy},
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        false,
			expectedDownloadFileNames: []string{getTodayFileName("filename", "ext")},
			expectedErr:               errDummy,
			expectedStatDTag:          "error:simulate error",
			description:               "download from SFTP failed",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "put object to S3 failed",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:            []string{getTodayFileName("filename", "ext")},
			errListFiles:              nil,
			errDownload:               nil,
			errPutObject:              map[string]error{getTodayFileName("filename", "ext"): errDummy},
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{getTodayFileName("filename", "ext")},
			expectedErr:               errDummy,
			expectedStatDTag:          "error:simulate error",
			description:               "put object to S3 failed",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "empty file extension",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:            []string{getTodayFileName("filename", "csv"), getTodayFileName("filename", "")},
			errListFiles:              nil,
			errDownload:               nil,
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{getTodayFileName("filename", "")},
			expectedErr:               nil,
			description:               "empty file extension",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "single file happy flow",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:            []string{getTodayFileName("filename", "ext")},
			errListFiles:              nil,
			errDownload:               nil,
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{getTodayFileName("filename", "ext")},
			expectedErr:               nil,
			description:               "single file happy flow",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "multiple files happy flow",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename*",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:            []string{getTodayFileName("filename", "ext"), getTodayFileName("file-name-not-matched", "ext"), getTodayFileName("filename-2", "ext")},
			errListFiles:              nil,
			errDownload:               nil,
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{getTodayFileName("filename", "ext"), getTodayFileName("filename-2", "ext")},
			expectedErr:               nil,
			description:               "multiple files happy flow",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "multiple files none matches pattern",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename*",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames:            []string{getTodayFileName("filename-1", "txt"), getTodayFileName("totally-different-2", "ext"), getTodayFileName("Filename-3", "ext")},
			errListFiles:              nil,
			errDownload:               nil,
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         false,
			expectingPutObject:        false,
			expectedDownloadFileNames: []string{},
			expectedErr:               nil,
			expectedStatDTag:          "not_found:yes",
			description:               "multiple files none matches pattern",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "multiple files failed with one of the download",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename*",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames: []string{getTodayFileName("filename-1", "ext"), getTodayFileName("filename-2", "ext"), getTodayFileName("filename-3", "ext")},
			errListFiles:   nil,
			errDownload: map[string]error{
				getTodayFileName("filename-1", "ext"): nil,
				getTodayFileName("filename-2", "ext"): errDummy,
				getTodayFileName("filename-3", "ext"): nil,
			},
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{getTodayFileName("filename-1", "ext"), getTodayFileName("filename-3", "ext")},
			expectedErr:               fmt.Errorf("one or more fetch operation failed"),
			expectedStatDTag:          "error:simulate error",
			description:               "multiple files failed with one of the download",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "multiple files failed with one of the put",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename*",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    true,
				Source:               constant.Sftp,
			},
			listFilesNames: []string{getTodayFileName("filename-1", "ext"), getTodayFileName("filename-2", "ext"), getTodayFileName("filename-3", "ext")},
			errListFiles:   nil,
			errDownload:    nil,
			errPutObject: map[string]error{
				getTodayFileName("filename-1", "ext"): nil,
				getTodayFileName("filename-2", "ext"): errDummy,
				getTodayFileName("filename-3", "ext"): nil,
			},
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{getTodayFileName("filename-1", "ext"), getTodayFileName("filename-3", "ext")},
			expectedErr:               fmt.Errorf("one or more fetch operation failed"),
			expectedStatDTag:          "error:simulate error",
			description:               "multiple files failed with one of the put",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "not appendTodaySuffix only matches exact filename",
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				AllowWildcard:        false,
				AppendTodaySuffix:    false,
				Source:               constant.Sftp,
			},
			listFilesNames:            nil,
			errListFiles:              nil,
			errDownload:               nil,
			errPutObject:              nil,
			expectingListFiles:        false,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{"filename.ext"},
			expectedErr:               nil,
			description:               "not appendTodaySuffix only matches exact filename",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "MaxConcurrentFetches < 0",
				Type:                 constant.FetchOnlyV2,
				Source:               constant.Sftp,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "file*",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    false,
				MaxConcurrentFetches: -1,
			},
			listFilesNames:            []string{"file1.ext", "file2.ext", "file3.ext", "file4.ext", "file5.ext", "file6.ext"},
			errListFiles:              nil,
			errDownload:               nil,
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{"file1.ext", "file2.ext", "file3.ext", "file4.ext", "file5.ext", "file6.ext"},
			expectedErr:               nil,
			description:               "MaxConcurrentFetches < 0 will use default",
		},
		{
			req: dto.FileRequestV2{
				Name:                 "MaxConcurrentFetches < 0",
				Type:                 constant.FetchOnlyV2,
				Source:               constant.Sftp,
				SourceDirectory:      "EN-SFTP-directory",
				DestinationDirectory: "S3-directory",
				FileName:             "file*",
				FileExtension:        "ext",
				AllowWildcard:        true,
				AppendTodaySuffix:    false,
				MaxConcurrentFetches: 999999,
			},
			listFilesNames:            []string{"file1.ext", "file2.ext", "file3.ext", "file4.ext", "file5.ext", "file6.ext"},
			errListFiles:              nil,
			errDownload:               nil,
			errPutObject:              nil,
			expectingListFiles:        true,
			expectingDownload:         true,
			expectingPutObject:        true,
			expectedDownloadFileNames: []string{"file1.ext", "file2.ext", "file3.ext", "file4.ext", "file5.ext", "file6.ext"},
			expectedErr:               nil,
			description:               "MaxConcurrentFetches > max will use default",
		},
	}

	for _, scenario := range scenarios {
		test := scenario
		description := test.description

		t.Run(description, func(t *testing.T) {
			//arrange
			mockSftp := &sftputils.MockClient{}
			mockSftp.On("ListFiles", mock.Anything, mock.Anything).Return(test.listFilesNames, test.errListFiles)
			if test.errDownload != nil {
				for fileName, err := range test.errDownload {
					mockSftp.On("DownloadAndLock", mock.Anything, fmt.Sprintf("%s/%s/%s", test.req.SourceDirectory, today, fileName)).Return([]byte{}, err)
				}
			} else {
				mockSftp.On("DownloadAndLock", mock.Anything, mock.Anything).Return([]byte{}, nil)
			}
			mockStatsd := &mocks.StatsdClient{}
			mockStatsd.On("Count1", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			mockS3 := &s3client.MockS3{}
			if test.errPutObject != nil {
				for fileName, err := range test.errPutObject {
					mockS3.On("PutObject", mock.Anything, fmt.Sprintf("%s/%s/%s", test.req.DestinationDirectory, today, fileName), mock.Anything, mock.Anything).Return(err)
				}
			} else {
				mockS3.On("PutObject", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
			}
			s3utils.S3Client = mockS3
			s3utils.ENAdapterS3Bucket = "EN-Adapter-S3-bucket"

			//act
			f := FileFetcherImpl{
				SftpClient: mockSftp,
				StatsD:     mockStatsd,
			}
			err := f.Fetch(context.Background(), test.req)

			//assert
			if test.expectedErr != nil {
				assert.Error(t, err, description)
				assert.Equal(t, test.expectedErr, err, description)
			} else {
				assert.NoError(t, err, description)
			}

			if test.expectingListFiles {
				mockSftp.AssertCalled(t, "ListFiles", mock.Anything, fmt.Sprintf("%s/%s", test.req.SourceDirectory, today))
			} else {
				mockSftp.AssertNotCalled(t, "ListFiles", mock.Anything, mock.Anything)
			}

			if test.expectingDownload {
				for _, filename := range test.expectedDownloadFileNames {
					mockSftp.AssertCalled(t, "DownloadAndLock", mock.Anything, fmt.Sprintf("%s/%s/%s", test.req.SourceDirectory, today, filename))
				}
			} else {
				mockSftp.AssertNotCalled(t, "DownloadAndLock", mock.Anything, mock.Anything)
			}

			if test.expectingPutObject {
				for _, filename := range test.expectedDownloadFileNames {
					mockS3.AssertCalled(t, "PutObject", "EN-Adapter-S3-bucket", fmt.Sprintf("%s/%s/%s", test.req.DestinationDirectory, today, filename), mock.Anything, mock.Anything)
				}
			} else {
				mockS3.AssertNotCalled(t, "PutObject", mock.Anything, mock.Anything, mock.Anything)
			}

			if test.expectedStatDTag != "" {
				mockStatsd.AssertCalled(t, "Count1", "file_processing", "FETCH_ONLY_V2", test.expectedStatDTag)
			} else {
				mockStatsd.AssertNotCalled(t, "Count1", mock.Anything, mock.Anything, mock.Anything)
			}
		})
	}
}

func TestS3ToSftpFileFetched(t *testing.T) {

	errDummy := errors.New("simulate error")
	scenarios := []struct {
		req          dto.FileRequestV2
		errDownload  error
		errPutObject error
		expectedErr  error
		description  string
	}{
		{
			description: "happy flow",
			req: dto.FileRequestV2{
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "S3-directory",
				DestinationDirectory: "EN-SFTP-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				Source:               constant.S3,
			},
			errDownload:  nil,
			errPutObject: nil,
			expectedErr:  nil,
		},
		{
			description: "destination sftp dir not set",
			req: dto.FileRequestV2{
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "S3-directory",
				DestinationDirectory: "",
				FileName:             "filename",
				FileExtension:        "ext",
				Source:               constant.S3,
			},
			errDownload:  nil,
			errPutObject: nil,
			expectedErr:  fmt.Errorf("EN sftp directory not set"),
		},
		{
			description: "source s3 dir not set",
			req: dto.FileRequestV2{
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "",
				DestinationDirectory: "EN-SFTP-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				Source:               constant.S3,
			},
			errDownload:  nil,
			errPutObject: nil,
			expectedErr:  fmt.Errorf("s3 directory not set"),
		},
		{
			description: "S3 file not found during download",
			req: dto.FileRequestV2{
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "S3-directory",
				DestinationDirectory: "EN-SFTP-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				Source:               constant.S3,
			},
			errDownload:  awserr.New(s3.ErrCodeNoSuchKey, "The specified key does not exist.", nil),
			errPutObject: nil,
			expectedErr:  nil,
		},
		{
			description: "download from s3 failed",
			req: dto.FileRequestV2{
				Type:                 constant.FetchOnlyV2,
				SourceDirectory:      "S3-directory",
				DestinationDirectory: "EN-SFTP-directory",
				FileName:             "filename",
				FileExtension:        "ext",
				Source:               constant.S3,
			},
			errDownload:  errDummy,
			errPutObject: nil,
			expectedErr:  errDummy,
		},
	}

	for _, scenario := range scenarios {
		test := scenario
		description := test.description

		t.Run(description, func(t *testing.T) {
			//arrange
			mockS3Client := s3client.MockS3{}
			s3utils.S3Client = &mockS3Client
			s3utils.ENAdapterS3Bucket = "EN-Adapter-S3-bucket"
			mockS3Client.On("GetObject", mock.Anything, mock.Anything).Return([]byte{}, test.errDownload).Once()

			mockStatsd := &mocks.StatsdClient{}
			mockStatsd.On("Count1", mock.Anything, mock.Anything, mock.Anything).Return(nil)

			mockSftp := &sftputils.MockClient{}
			mockSftp.On("Upload", mock.Anything, mock.Anything, mock.Anything).Return(test.errPutObject)

			//act
			f := FileFetcherImpl{
				SftpClient: mockSftp,
				StatsD:     mockStatsd,
			}
			err := f.Fetch(context.Background(), test.req)

			//assert
			if test.expectedErr != nil {
				assert.Error(t, err, description)
				assert.Equal(t, test.expectedErr, err, description)
			} else {
				assert.NoError(t, err, description)
			}

		})
	}
}
