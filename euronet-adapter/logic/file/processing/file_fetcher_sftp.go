package processing

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/constants"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"path/filepath"
	"sync"
	"time"
)

const (
	maxConcurrentFileFetches     = 10
	defaultConcurrentFileFetches = 3
)

type fetchFilesParams struct {
	FileType             constant.FileType
	FileNamePattern      string
	SourceDirectory      string
	DestinationDirectory string
	MaxConcurrentFetches int
}

func (f *FileFetcherImpl) fetchSftp(ctx context.Context, req dto.FileRequestV2) error {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constants.FileProcessTypeTag, req.Type), slog.CustomTag(constants.FileProcessNameTag, req.Name))
	if err := f.validateFetchSftpRequest(ctx, req); err != nil {
		return err
	}

	filesParams := f.determineFetchFilesParams(req)
	fileNames, err := f.getMatchingFileNames(ctx, req.Type, filesParams.SourceDirectory, filesParams.FileNamePattern, req.AllowWildcard)
	if err != nil {
		//logs and metrics are done in getMatchingFileNames
		return err
	}
	return f.fetchFiles(ctx, filesParams, fileNames)
}

func (f *FileFetcherImpl) fetchFiles(ctx context.Context, filesParams *fetchFilesParams, fileNames []string) error {
	if len(fileNames) <= 0 {
		//logs and metrics are done in getMatchingFileNames
		return nil
	}

	if len(fileNames) == 1 {
		//logs and metrics are done in fetchSingle
		return f.fetchSingle(ctx, filesParams.FileType, fmt.Sprintf("%s/%s", filesParams.SourceDirectory, fileNames[0]), filesParams.DestinationDirectory)
	}

	return f.fetchMultiple(ctx, filesParams, fileNames)
}

func (f *FileFetcherImpl) fetchMultiple(ctx context.Context, filesParams *fetchFilesParams, fileNames []string) error {
	errors := make([]error, len(fileNames))
	wg := &sync.WaitGroup{}
	guard := make(chan struct{}, filesParams.MaxConcurrentFetches)
	for i, filename := range fileNames {
		guard <- struct{}{}
		wg.Add(1)
		go func(filename string, err *error) {
			defer func() {
				<-guard
				wg.Done()
			}()
			*err = f.fetchSingle(ctx, filesParams.FileType, fmt.Sprintf("%s/%s", filesParams.SourceDirectory, filename), filesParams.DestinationDirectory)
		}(filename, &errors[i])
	}
	wg.Wait()

	for _, err := range errors {
		if err != nil {
			return fmt.Errorf("one or more fetch operation failed")
		}
	}
	return nil
}

func (f *FileFetcherImpl) fetchSingle(ctx context.Context, fileType constant.FileType, filePath string, destinationDirectory string) error {
	content, err := f.SftpClient.DownloadAndLock(ctx, filePath)
	if err != nil {
		if err.Error() == file.ErrFileNotFound.Error() {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("%s file not found, skipping ....", filePath))
			f.StatsD.Count1(logTag, string(fileType), fmt.Sprintf("%s:yes", constants.NotFoundTag))
			return nil
		}
		if errors.Is(err, redis.ErrLockOccupied) {
			//skip if the file is locked
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("%s file is locked, skipping ....", filePath))
			return nil
		}
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("failed to fetch %s file from sftp server", filePath), slog.Error(err))
		f.StatsD.Count1(logTag, string(fileType), fmt.Sprintf("%s:%s", constants.ErrorTag, err.Error()))
		return err
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("file %s fetch with size %d", filePath, len(content)))

	reader := bytes.NewReader(content)
	s3FilePath := fmt.Sprintf("%s/%s", destinationDirectory, filepath.Base(filePath))
	if err = file.PushToS3(ctx, logTag, reader, s3FilePath); err != nil {
		// error is logged inside PushToS3 fn
		f.StatsD.Count1(logTag, string(fileType), fmt.Sprintf("%s:%s", constants.ErrorTag, err.Error()))
		return err
	}

	return nil
}

func (f *FileFetcherImpl) validateFetchSftpRequest(ctx context.Context, req dto.FileRequestV2) error {
	if req.Name == "" {
		slog.FromContext(ctx).Warn(logTag, "name not set.")
		return fmt.Errorf("name not set")
	}
	if req.SourceDirectory == "" {
		slog.FromContext(ctx).Warn(logTag, "EN sftp directory not set.")
		return fmt.Errorf("EN sftp directory not set")
	}
	if req.DestinationDirectory == "" {
		slog.FromContext(ctx).Warn(logTag, "S3 directory not set.")
		return fmt.Errorf("s3 directory not set")
	}
	return nil
}

func (f *FileFetcherImpl) determineFetchFilesParams(req dto.FileRequestV2) *fetchFilesParams {
	today := time.Now().Format(constant.FormatYYYYMMDD)
	result := &fetchFilesParams{
		FileType:             req.Type,
		SourceDirectory:      fmt.Sprintf("%s/%s", req.SourceDirectory, today),
		DestinationDirectory: fmt.Sprintf("%s/%s", req.DestinationDirectory, today),
		FileNamePattern:      req.FileName,
		MaxConcurrentFetches: req.MaxConcurrentFetches,
	}
	if req.AppendTodaySuffix {
		result.FileNamePattern = fmt.Sprintf("%s_%s", result.FileNamePattern, today)
	}
	if req.FileExtension != "" {
		result.FileNamePattern = fmt.Sprintf("%s.%s", result.FileNamePattern, req.FileExtension)
	}
	if req.MaxConcurrentFetches < 0 {
		result.MaxConcurrentFetches = 1 //negative means no concurrency
	} else if req.MaxConcurrentFetches == 0 || req.MaxConcurrentFetches > maxConcurrentFileFetches {
		result.MaxConcurrentFetches = defaultConcurrentFileFetches
	}
	return result
}

func (f *FileFetcherImpl) getMatchingFileNames(ctx context.Context, fileType constant.FileType, sourceDirectory string, searchPattern string, allowWildcard bool) ([]string, error) {
	var result []string

	if allowWildcard {
		fileNames, err := f.SftpClient.ListFiles(ctx, sourceDirectory)
		if err != nil && err.Error() != file.ErrFileNotFound.Error() {
			slog.FromContext(ctx).Error(logTag, fmt.Sprintf("failed to list files in %s from sftp server", sourceDirectory), slog.Error(err))
			f.StatsD.Count1(logTag, string(fileType), fmt.Sprintf("%s:%s", constants.ErrorTag, err.Error()))
			return result, err
		}

		for _, filename := range fileNames {
			if f.fileNameMatches(filename, searchPattern) {
				result = append(result, filename)
			}
		}

		if len(result) == 0 {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("file not found in %s, skipping ....", sourceDirectory))
			f.StatsD.Count1(logTag, string(fileType), fmt.Sprintf("%s:yes", constants.NotFoundTag))
		}
	} else {
		result = append(result, searchPattern)
	}

	return result, nil
}

func (f *FileFetcherImpl) fileNameMatches(fileName string, fileNamePattern string) bool {
	matched, err := filepath.Match(fileNamePattern, fileName)
	return matched && err == nil
}
