package authorize

import (
	"context"
	"errors"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	digicardTxnAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	digicardTxnMock "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	we "gitlab.myteksi.net/dakota/workflowengine"
)

func TestPrepareCapture(t *testing.T) {
	mockStorageDAO := &storage.MockICardTransactionDAO{}
	mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
	storage.CardTransactionD = mockStorageDAO
	exeData := &ExecutionData{
		CardTransaction: storage.CardTransaction{},
		State:           stAuthorized,
	}
	w := WorkflowImpl{}
	captureParams := ClearingCaptureParams{
		CaptureAmount: 100,
		ClearingRecord: dto.ClearingRecord{
			MRC: "1404",
		},
	}
	updatedCtx, err := w.prepareCapture(context.Background(), uuid.NewString(), exeData, captureParams)
	assert.NoError(t, err)
	assert.Equal(t, stCapturePrepared, updatedCtx.GetState())
}

func TestPreparePartialCapture(t *testing.T) {
	mockStorageDAO := &storage.MockICardTransactionDAO{}
	mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
	storage.CardTransactionD = mockStorageDAO
	exeData := &ExecutionData{
		CardTransaction: storage.CardTransaction{},
		State:           stAuthorized,
	}
	w := WorkflowImpl{StatsD: statsd.NewNoop()}
	captureParams := ClearingCaptureParams{
		CaptureAmount: 100,
		ClearingRecord: dto.ClearingRecord{
			MRC: "1403",
		},
	}
	updatedCtx, err := w.prepareCapture(context.Background(), uuid.NewString(), exeData, captureParams)
	assert.NoError(t, err)
	assert.Equal(t, stPartialCapturePrepared, updatedCtx.GetState())
}

func TestCapture(t *testing.T) {
	mockStorageDAO := &storage.MockICardTransactionDAO{}
	storage.CardTransactionD = mockStorageDAO
	exeData := &ExecutionData{
		CardTransaction: storage.CardTransaction{},
		State:           stCapturePrepared,
	}
	mockDigicardTxnClient := &digicardTxnMock.DigicardTransaction{}
	w := WorkflowImpl{
		DigicardTxnClient: mockDigicardTxnClient,
	}

	t.Run("capture success", func(t *testing.T) {
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		resp := &digicardTxnAPI.ChargeResponse{Data: &digicardTxnAPI.ChargeResource{
			Status: string(logic.Success),
		}}
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(resp, nil).Once()
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureCompleted, updatedCtx.GetState())
	})

	t.Run("capture failed", func(t *testing.T) {
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		resp := &digicardTxnAPI.ChargeResponse{Data: &digicardTxnAPI.ChargeResource{
			Status: string(logic.Failed),
		}}
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(resp, nil).Once()
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureFailed, updatedCtx.GetState())
	})

	t.Run("capture processing", func(t *testing.T) {
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		resp := &digicardTxnAPI.ChargeResponse{Data: &digicardTxnAPI.ChargeResource{
			Status: string(logic.Processing),
		}}
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(resp, nil).Once()
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureProcessing, updatedCtx.GetState())
	})

	t.Run("capture timeout", func(t *testing.T) {
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(nil, errors.New("simulate error"))
		expectedErr := errors.New("simulate error")
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, updatedCtx)
	})

	t.Run("db update failed", func(t *testing.T) {
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("simulate error")).Once()
		resp := &digicardTxnAPI.ChargeResponse{Data: &digicardTxnAPI.ChargeResource{
			Status: string(logic.Success),
		}}
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(resp, nil).Once()
		expectedErr := errors.New("simulate error")
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, updatedCtx)
	})
}

func TestPersistCapture(t *testing.T) {
	exeData := &ExecutionData{
		CardTransaction: storage.CardTransaction{},
		State:           stCaptureProcessing,
	}
	w := WorkflowImpl{}
	streamMessage := dto.StreamMessage{}
	updatedCtx, err := w.persistCaptureStream(context.Background(), uuid.NewString(), exeData, &streamMessage)
	assert.NoError(t, err)
	assert.Equal(t, stCaptureStreamPersisted, updatedCtx.GetState())
}

func TestCompleteCapture(t *testing.T) {
	mockStorageDAO := &storage.MockICardTransactionDAO{}
	storage.CardTransactionD = mockStorageDAO
	exeData := &ExecutionData{
		CardTransaction: storage.CardTransaction{},
		State:           stCaptureStreamPersisted,
		AuthorizeRequest: &api.TransactionRequest{
			AppHeader: &api.AppHeader{
				ServiceName: dto.TxnAuth,
			},
		},
	}
	w := WorkflowImpl{}

	t.Run("capture success", func(t *testing.T) {
		localExeData := exeData
		localExeData.StreamMessage = &dto.StreamMessage{
			InternalType: string(constant.CaptureTx),
			Status:       string(constant.TxnCompleted),
		}
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		updatedCtx, err := w.completeCapture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureCompleted, updatedCtx.GetState())
	})

	t.Run("capture failed", func(t *testing.T) {
		localExeData := exeData
		localExeData.StreamMessage = &dto.StreamMessage{
			InternalType: string(constant.CaptureTx),
			Status:       string(constant.TxnFailed),
		}
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		updatedCtx, err := w.completeCapture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureFailed, updatedCtx.GetState())
	})
}

func TestWorkflowImpl_captureTypeHandler(t *testing.T) {
	execData := &ExecutionData{
		CardTransaction: storage.CardTransaction{},
		State:           stAuthorized,
	}
	w := WorkflowImpl{StatsD: statsd.NewNoop()}
	captureParams := ClearingCaptureParams{
		CaptureAmount: 100,
		ClearingRecord: dto.ClearingRecord{
			MRC: "",
		},
	}
	testCases := []struct {
		name  string
		mrc   string
		final we.State
	}{
		{
			"partial",
			"1403",
			stPartialCapturePrepared,
		},
		{
			"final",
			"1404",
			stCapturePrepared,
		},
		{
			"others",
			"1400",
			stCapturePrepared,
		},
	}
	for _, test := range testCases {
		captureParams.ClearingRecord.MRC = test.mrc
		w.captureTypeHandler(context.Background(), execData, &captureParams)
		assert.Equal(t, test.final, execData.GetState(), test.name)
		execData.State = stAuthorized
	}
}
