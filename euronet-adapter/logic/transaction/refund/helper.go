package refund

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// UpdateDBRefund update the refund table using updateEntity method
func UpdateDBRefund(ctx context.Context, currRefund *storage.Refund, nextRefund *storage.Refund) error {
	nextRefund.UpdatedAt = time.Now()
	if err := storage.RefundD.UpdateEntity(ctx, currRefund, nextRefund); err != nil {
		slog.FromContext(ctx).Warn(logRefund, "RefundD.UpdateEntity error", slog.Error(err))
		return err
	}
	slog.FromContext(ctx).Debug(logRefund, fmt.Sprintf("successfully updated nextState status for %s from %s to %s)", nextRefund.InternalTransactionID, currRefund.Status, nextRefund.Status))
	return nil
}
