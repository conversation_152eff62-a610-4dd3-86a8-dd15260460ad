package authorizepaynet

import (
	"context"
	"errors"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	digicardTxnAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	digicardTxnMock "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
)

var saleCompletionReq = &api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest{
	TransactionInfo: &api.PaynetSaleCompletionRequest_SaleCompletionReq{
		OrigTransactionID: uuid.NewString(),
		TransactionAmount: 100,
	},
}

var transactionAuthReq = &api.PaynetAuthorizeRequest_TransactionAuthorizeRequest{
	TransactionInfo: &api.PaynetAuthorizeRequest_AuthorizeReq{
		RetrievalReferenceNumber: uuid.NewString(),
		TransactionAmount:        100,
	},
}

func TestPrepareCapture(t *testing.T) {
	mockStorageDAO := &storage.MockICardTransactionDAO{}
	mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransaction{{}}, nil).Once()
	mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
	storage.CardTransactionD = mockStorageDAO
	exeData := &ExecutionData{
		PaynetSaleCompletionRequest: saleCompletionReq,
		TransactionAuthorizeRequest: transactionAuthReq,
		State:                       stAuthorized,
	}
	w := WorkflowImpl{}
	updatedCtx, err := w.prepareCapture(context.Background(), uuid.NewString(), exeData, saleCompletionReq)
	assert.NoError(t, err)
	assert.Equal(t, stCapturePrepared, updatedCtx.GetState())
}

func TestCapture(t *testing.T) {
	mockStorageDAO := &storage.MockICardTransactionDAO{}
	storage.CardTransactionD = mockStorageDAO
	exeData := &ExecutionData{
		PaynetSaleCompletionRequest: saleCompletionReq,
		State:                       stCapturePrepared,
	}
	mockDigicardTxnClient := &digicardTxnMock.DigicardTransaction{}
	w := WorkflowImpl{
		DigicardTxnClient: mockDigicardTxnClient,
	}

	t.Run("capture success", func(t *testing.T) {
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		resp := &digicardTxnAPI.ChargeResponse{Data: &digicardTxnAPI.ChargeResource{
			Status: string(logic.Completed),
		}}
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(resp, nil).Once()
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureCompleted, updatedCtx.GetState())
	})

	t.Run("capture failed", func(t *testing.T) {
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		resp := &digicardTxnAPI.ChargeResponse{Data: &digicardTxnAPI.ChargeResource{
			Status: string(logic.Failed),
		}}
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(resp, nil).Once()
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stFailed, updatedCtx.GetState())
	})

	t.Run("capture processing", func(t *testing.T) {
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		resp := &digicardTxnAPI.ChargeResponse{Data: &digicardTxnAPI.ChargeResource{
			Status: string(logic.Processing),
		}}
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(resp, nil).Once()
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureProcessing, updatedCtx.GetState())
	})

	t.Run("capture timeout", func(t *testing.T) {
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(nil, errors.New("simulate error"))
		expectedErr := errors.New("simulate error")
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, updatedCtx)
	})

	t.Run("db update failed", func(t *testing.T) {
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("simulate error")).Once()
		resp := &digicardTxnAPI.ChargeResponse{Data: &digicardTxnAPI.ChargeResource{
			Status: string(logic.Success),
		}}
		mockDigicardTxnClient.On("CaptureCharge", mock.Anything, mock.Anything).Return(resp, nil).Once()
		expectedErr := errors.New("simulate error")
		updatedCtx, err := w.capture(context.Background(), uuid.NewString(), exeData, nil)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, updatedCtx)
	})
}

func TestPersistCapture(t *testing.T) {
	exeData := &ExecutionData{
		CardTransaction: storage.CardTransaction{},
		State:           stCaptureProcessing,
	}
	w := WorkflowImpl{}
	streamMessage := dto.StreamMessage{}
	updatedCtx, err := w.persistCaptureStream(context.Background(), uuid.NewString(), exeData, &streamMessage)
	assert.NoError(t, err)
	assert.Equal(t, stCapturePersisted, updatedCtx.GetState())
}

func TestCompleteCapture(t *testing.T) {
	mockStorageDAO := &storage.MockICardTransactionDAO{}
	storage.CardTransactionD = mockStorageDAO
	exeData := &ExecutionData{
		CardTransaction: storage.CardTransaction{},
		State:           stCapturePersisted,
		//AuthorizeRequest: &api.TransactionRequest{
		//	AppHeader: &api.AppHeader{
		//		ServiceName: dto.TxnAuth,
		//	},
		//},
	}
	w := WorkflowImpl{}

	t.Run("capture success", func(t *testing.T) {
		localExeData := exeData
		localExeData.StreamMessage = &dto.StreamMessage{
			InternalType: string(constant.CaptureTx),
			Status:       string(constant.TxnCompleted),
		}
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		updatedCtx, err := w.completeCapture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureCompleted, updatedCtx.GetState())
	})

	t.Run("capture failed", func(t *testing.T) {
		localExeData := exeData
		localExeData.StreamMessage = &dto.StreamMessage{
			InternalType: string(constant.CaptureTx),
			Status:       string(constant.TxnFailed),
		}
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		updatedCtx, err := w.completeCapture(context.Background(), uuid.NewString(), exeData, nil)
		assert.NoError(t, err)
		assert.Equal(t, stCaptureFailed, updatedCtx.GetState())
	})
}
