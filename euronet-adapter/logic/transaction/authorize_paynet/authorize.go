package authorizepaynet

import (
	"context"
	"errors"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common/mapper"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
)

// ExecuteAuthorizeTransactionWorkflow ...
func (w *WorkflowImpl) ExecuteAuthorizeTransactionWorkflow(
	ctx context.Context,
	req *api.PaynetAuthorizeRequest_TransactionAuthorizeRequest,
	params *logic.Transaction,
) (*api.PaynetAuthorizeResponse_TransactionAuthorizeResponse, error) {
	data := &ExecutionData{
		TransactionAuthorizeRequest: req,
		State:                       stInit,
	}
	requestID := CreateWorkflowRunID(
		req.TransactionInfo.RetrievalReferenceNumber,
		req.TransactionInfo.NetworkID,
		req.TransactionInfo.ProxyNumber,
	)
	ctx = slog.AddTagsToContext(ctx,
		slog.CustomTag(logic.RequestIDTag, requestID),
		slog.CustomTag(logic.ServiceNameTag, req.AppHeader.ServiceName),
		slog.CustomTag(logic.TransactionIDTag, req.TransactionInfo.TransactionID),
		slog.CustomTag(logic.RRNTag, req.TransactionInfo.RetrievalReferenceNumber),
		slog.CustomTag(logic.ProxyNumberTag, req.TransactionInfo.ProxyNumber),
	)

	if err := wfInit(ctx, workflowengine.Execution{
		WorkflowID: workflowID,
		RequestID:  requestID,
	}, data); err != nil {
		if !errors.Is(err, workflowengine.ErrExecutionAlreadyExists) {
			slog.FromContext(ctx).Warn(logAuth, "error when initiating workflow", slog.Error(err))
			return nil, logic.ErrGenericServer
		}

		// attempt to check existing workflow if already exists
		execData, getErr := wfGet(ctx, workflowengine.Execution{
			WorkflowID: workflowID,
			RequestID:  requestID,
		})
		if getErr != nil {
			slog.FromContext(ctx).Warn(logAuth, "error when getting existing workflow", slog.Error(err))
			return nil, logic.ErrGenericServer
		}

		if execData.(*ExecutionData).State != stInit {
			return w.convertResponse(execData.(*ExecutionData)), nil
		}
	}

	execData, err := wfExecute(ctx, workflowengine.Execution{
		WorkflowID:     workflowID,
		RequestID:      requestID,
		ExecutionEvent: evPersistRequest,
	}, params)
	if err != nil {
		slog.FromContext(ctx).Warn(logAuth, "error when running workflow", slog.Error(err))
		if err.Error() == constant.TransactionSLABreach {
			return nil, logic.ErrAuthTimeOut
		}
		return nil, logic.ErrGenericServer
	}
	return w.convertResponse(execData.(*ExecutionData)), nil
}

func (w *WorkflowImpl) convertResponse(data *ExecutionData) *api.PaynetAuthorizeResponse_TransactionAuthorizeResponse {
	req := data.TransactionAuthorizeRequest

	res := mapper.PaynetAuthConvertResponse(&api.PaynetAuthorizeRequest{TransactionAuthorizeRequest: req}, nil)
	res.TransactionAuthorizeResponse.Response.ResponseCode = data.ResponseCode
	res.TransactionAuthorizeResponse.Response.Description = data.Description
	if data.ChargeResponse != nil && data.ChargeResponse.Data != nil && data.ChargeResponse.Data.AmountParams != nil {
		balanceInfo := &api.BalanceInfo{
			WithdrawalLimit: data.ChargeResponse.Data.AmountParams.RemainingLimitAmount,
		}
		if data.ChargeResponse.Data.AmountParams.AvailableBalance != nil {
			balanceInfo.AvailableBalance = data.ChargeResponse.Data.AmountParams.AvailableBalance.Amount
			balanceInfo.CurrencyCode = data.ChargeResponse.Data.AmountParams.AvailableBalance.Currency
		}
		res.TransactionAuthorizeResponse.BalanceInfo = balanceInfo
	}

	return res.TransactionAuthorizeResponse
}
