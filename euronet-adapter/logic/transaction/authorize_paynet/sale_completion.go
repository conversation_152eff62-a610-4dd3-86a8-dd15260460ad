package authorizepaynet

import (
	"context"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common/mapper"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
)

// ExecuteSaleCompletionTransactionWorkflow ...
func (w *WorkflowImpl) ExecuteSaleCompletionTransactionWorkflow(
	ctx context.Context,
	req *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest,
) (*api.PaynetSaleCompletionResponse_TransactionSaleCompletionResponse, error) {
	requestID := CreateWorkflowRunID(
		req.TransactionInfo.OrigTransactionID,
		req.TransactionInfo.NetworkID,
		req.TransactionInfo.ProxyNumber,
	)

	ctx = slog.AddTagsToContext(ctx,
		slog.CustomTag(logic.RequestIDTag, requestID),
		slog.CustomTag(logic.ServiceNameTag, req.AppHeader.ServiceName),
		slog.CustomTag(logic.TransactionIDTag, req.TransactionInfo.TransactionID),
		slog.CustomTag(logic.RRNTag, req.TransactionInfo.RetrievalReferenceNumber),
		slog.CustomTag(logic.ProxyNumberTag, req.TransactionInfo.ProxyNumber),
		slog.CustomTag(logic.OriginalRequestIDTag, req.TransactionInfo.OrigTransactionID),
	)

	_, err := getCardOriginTxn(ctx, req.TransactionInfo)
	if err != nil {
		return nil, logic.ErrGenericServer
	}

	execution, err := wfGet(ctx, workflowengine.Execution{
		WorkflowID: workflowID,
		RequestID:  requestID,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(logCapture, "error when getting existing workflow for sale completion", slog.Error(err))
		return nil, logic.ErrGenericServer
	}

	var transactionState = execution.GetState()
	var captureAmount = req.TransactionInfo.TransactionAmount
	switch execution.GetState() {
	case stAuthorized:
		if captureAmount == 0 {
			transactionState, err = TriggerCancelWf(ctx, requestID, req)
		} else {
			transactionState, err = TriggerCaptureWf(ctx, requestID, req)
		}
		if err != nil {
			return nil, err
		}
		if transactionState == stCaptureProcessing || transactionState == stCancelProcessing {
			processingState, errRes := w.retryProcessingState(ctx, requestID)
			return w.convertSaleCompletionResponse(req, processingState), errRes
		}
		return w.convertSaleCompletionResponse(req, transactionState), nil
	case stCaptureProcessing, stCaptureCompleted, stFailed, stCapturePersisted, stCancelPrepared, stCancelProcessing, stCancelStreamPersisted:
		if transactionState == stCaptureProcessing || transactionState == stCancelProcessing {
			processingState, errRes := w.retryProcessingState(ctx, requestID)
			return w.convertSaleCompletionResponse(req, processingState), errRes
		}
		return w.convertSaleCompletionResponse(req, transactionState), nil
	case stCancelCompleted:
		return nil, logic.ErrDuplicateRRN
	default:
		return nil, logic.ErrGenericServer
	}
}

// retryProcessingState: Paynet Sale Completion request timeout: 4.5s
// for interval_time 80 = and max_attempt = 5 -> 80 + 160 + 320 + 640 + 1280 = 2480 ~ 2.5s
func (w *WorkflowImpl) retryProcessingState(ctx context.Context, requestID string) (workflowengine.State, error) {
	var state workflowengine.State
	duration := time.Duration(w.WorkflowRetryConfig.SaleCompletionStateRetry.IntervalInMilliSeconds)
	for i := 0; i < w.WorkflowRetryConfig.SaleCompletionStateRetry.MaxAttempt; i++ {
		time.Sleep(duration * time.Millisecond)
		wfLatestData, err := wfGet(ctx, workflowengine.Execution{
			WorkflowID: workflowID,
			RequestID:  requestID,
		})
		if err != nil {
			slog.FromContext(ctx).Warn(logCapture, "error when getting existing workflow", slog.Error(err))
			return 0, logic.ErrGenericServer
		}
		slog.FromContext(ctx).Debug(logCapture, fmt.Sprintf("retry %d time to fetch transaction of requestID: %s", i, requestID))

		getState := wfLatestData.GetState()
		duration *= 2
		switch getState {
		case stCaptureProcessing, stCancelProcessing:
			continue
		case stCaptureCompleted, stCancelCompleted, stFailed:
			return getState, nil
		}
	}
	return state, logic.ErrAuthTimeOut
}

// TriggerCaptureWf ...
func TriggerCaptureWf(ctx context.Context, requestID string, saleCompletionReq *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest) (workflowengine.State, error) {
	captureEvent, err := wfExecute(ctx, workflowengine.Execution{
		WorkflowID:     workflowID,
		RequestID:      requestID,
		ExecutionEvent: evCaptureRequest,
	}, saleCompletionReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logCapture, "error when running capture transaction workflow", slog.Error(err))
		return stFailed, logic.ErrGenericServer
	}
	return captureEvent.GetState(), nil
}

// TriggerCancelWf ...
func TriggerCancelWf(ctx context.Context, requestID string, saleCompletionReq *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest) (workflowengine.State, error) {
	cancel, err := wfExecute(ctx, workflowengine.Execution{
		WorkflowID:     workflowID,
		RequestID:      requestID,
		ExecutionEvent: evCancel,
	}, saleCompletionReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logCapture, "error when running cancel transaction workflow", slog.Error(err))
		return stFailed, logic.ErrGenericServer
	}
	return cancel.GetState(), nil
}

// getCardOriginTxn ...
//
//nolint:unparam //storage.CardTransaction is never used, result 0
func getCardOriginTxn(ctx context.Context, saleCompletionReq *api.PaynetSaleCompletionRequest_SaleCompletionReq) (*storage.CardTransaction, error) {
	query := []data.Condition{
		data.EqualTo("RetrievalReferenceNumber", saleCompletionReq.OrigTransactionID),
		data.EqualTo("ProxyNumber", saleCompletionReq.ProxyNumber),
		data.Limit(1),
	}
	cardTransaction, err := storage.CardTransactionD.FindOnSlave(ctx, query...)
	if err != nil {
		if err == data.ErrNoData {
			slog.FromContext(ctx).Warn(logCapture, "paynet card transaction original not found", slog.Error(err))
			return nil, err
		}
		slog.FromContext(ctx).Warn(logCapture, "CardTransactionD.FindOnSlave error")
		return nil, err
	}
	return cardTransaction[0], nil
}

func (w *WorkflowImpl) convertSaleCompletionResponse(req *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest, state workflowengine.State) *api.PaynetSaleCompletionResponse_TransactionSaleCompletionResponse {
	res := mapper.PaynetSaleCompletionConvertResponse(&api.PaynetSaleCompletionRequest{TransactionSaleCompletionRequest: req}, nil)
	switch state {
	case stCaptureProcessing, stCapturePrepared, stCapturePersisted, stCancelPrepared, stCancelProcessing, stCancelStreamPersisted:
		res.TransactionSaleCompletionResponse.Response.ResponseCode = logic.ErrAuthTimeout.Code
		res.TransactionSaleCompletionResponse.Response.Description = logic.ErrAuthTimeout.Message
	case stCaptureCompleted, stCancelCompleted:
		res.TransactionSaleCompletionResponse.Response.ResponseCode = logic.ApprovedCode
		res.TransactionSaleCompletionResponse.Response.Description = logic.Approved
	default:
		res.TransactionSaleCompletionResponse.Response.ResponseCode = logic.NotApprovedCode
		res.TransactionSaleCompletionResponse.Response.Description = logic.NotApproved
	}

	return res.TransactionSaleCompletionResponse
}
