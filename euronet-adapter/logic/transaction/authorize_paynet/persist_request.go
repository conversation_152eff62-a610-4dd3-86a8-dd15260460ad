package authorizepaynet

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage/dbtype"
)

func (w *WorkflowImpl) persistAuthzRequest(
	ctx context.Context,
	transitionID string,
	execData workflowengine.ExecutionData,
	params interface{},
) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAuth, "Invalid context passed in persistRequest state")
		return nil, logic.ErrInvalidContext
	}

	transactionParams, ok := params.(*logic.Transaction)
	if !ok {
		slog.FromContext(ctx).Warn(logAuth, "Invalid params passed in persistRequest state")
		return nil, logic.ErrWrongParamsPassed
	}

	nextCtx := currCtx.Clone()
	storageCardTransaction, err := persistNewAuthzRequest(ctx, nextCtx, transactionParams)
	if err != nil {
		return nil, err
	}

	nextCtx.CardTransaction = *storageCardTransaction
	nextCtx.State = stRequestPersisted
	return nextCtx, nil
}

//nolint:funlen
func persistNewAuthzRequest(ctx context.Context, nextCtx *ExecutionData, transactionParams *logic.Transaction) (*storage.CardTransaction, error) {
	authReq := nextCtx.TransactionAuthorizeRequest

	appHeader := authReq.AppHeader
	transactionInfo := authReq.TransactionInfo
	acceptorCountryCode := logic.ParseAcceptorCountryCodeAlpha3(transactionInfo.CardAcceptorTermName, transactionInfo.NetworkID)
	merchantInfo := &dbtype.JSONMerchantInfo{
		AcquirerCountryCode:      transactionInfo.AcquirerCountryCode,
		AcquirerID:               transactionInfo.AcquirerID,
		AcquirerFIID:             transactionInfo.AcquirerFIID,
		CardAcceptorIDCode:       transactionInfo.CardAcceptorIDCode,
		TerminalAddress:          transactionInfo.TerminalAddress,
		TerminalOwnerFIID:        transactionInfo.TerminalOwnerFIID,
		TerminalID:               transactionInfo.CardAcceptorTerminalID,
		TerminalName:             transactionInfo.CardAcceptorTermName,
		TransitTransactionTypeID: transactionInfo.TransitTransactionTypeID,
	}

	riskProperties := &dbtype.JSONRiskProperties{
		ThreeDsAuthTID:           transactionInfo.ThreeDsAuthTID,
		ThreeDsValidation:        transactionInfo.ThreeDsValidation,
		ThreeDsAuthResultCode:    transactionInfo.ThreeDsAuthResultCode,
		PANValidation:            transactionInfo.PANValidation,
		ExpiryDateValidation:     transactionInfo.ExpiryDateValidation,
		PinValidation:            transactionInfo.PinValidation,
		CVVValidation:            transactionInfo.CVVValidation,
		CVV2Validation:           transactionInfo.CVV2Validation,
		EMVValidation:            transactionInfo.EMVValidation,
		AcquirerID:               transactionInfo.AcquirerID,
		TransitTransactionTypeID: transactionInfo.TransitTransactionTypeID,
		SecondFactorAuthCode:     transactionInfo.SecondFactorAuthCode,
		CNPPaymentIndicator:      transactionInfo.CnpPaymentIndicator,
	}
	feeInfo := &dbtype.JSONFeeInfo{
		TransactionFeeAmount: transactionInfo.TransactionFeeAmount,
		ProcessingFeeAmount:  transactionInfo.ProcessingFeeAmount,
	}
	// transactionID is optional for paynet transaction
	// it's only required by Mastercard scheme
	cardSchemeTransactionID := transactionInfo.TransactionID
	if cardSchemeTransactionID == "" {
		cardSchemeTransactionID = transactionInfo.RetrievalReferenceNumber
	}
	isReversed := common.ContainStringValue([]string{logic.ReversalMTI, logic.ReversalRepeatMTI}, transactionInfo.Mti)
	entity := &storage.CardTransaction{
		Category:                 string(transactionParams.TransactionCategory),
		SubCategory:              string(transactionParams.TransactionSubCategory),
		TransactionType:          string(transactionParams.TransactionType),
		RequestID:                appHeader.ReqID,
		MTI:                      transactionInfo.Mti,
		ProxyNumber:              transactionInfo.ProxyNumber,
		ProcessingCode:           transactionInfo.ProcessingCode,
		RequestAmount:            transactionInfo.TransactionAmount,
		RequestCurrency:          transactionInfo.TransactionCurrencyCode,
		OriginalAmount:           transactionInfo.TransactionAmount,
		OriginalCurrency:         transactionInfo.TransactionCurrencyCode,
		CurrencyConvType:         string(transactionParams.CurrencyConvType),
		MCC:                      transactionInfo.MerchantCategoryCode,
		PanEntryMode:             transactionInfo.PosEntryMode, // PosEntryMode (paynet) is same as PanEntryMode (mastercard)
		PosConditionCode:         transactionInfo.PosConditionCode,
		RetrievalReferenceNumber: transactionInfo.RetrievalReferenceNumber,
		CardSchemeTransactionID:  cardSchemeTransactionID,
		EuronetTransactionID:     transactionInfo.SystemTraceAuditNumber,
		AuthorizationID:          transactionInfo.AuthorizationID,
		MerchantInfo:             merchantInfo,
		RiskProperties:           riskProperties,
		FeeInfo:                  feeInfo,
		Properties:               transaction.MarshalEmptyPropertiesJSON(),
		NetworkID:                transactionInfo.NetworkID,
		Status:                   transaction.StateToStatus(stInit),
		AcceptorCountryCode:      acceptorCountryCode,
		TransmissionDatetime:     transactionInfo.TransmissionDateTime,
		LocalTransactionDatetime: logic.ConvertLocalTransactionDateTime(transactionInfo.LocalTransactionDateTime),
		RequestBody:              marshalAuthRequest(authReq),
		// save empty resp json to avoid scan error when loading the entry from db
		ResponseBody: marshalEmptyRespJSON(),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		IsReversed:   isReversed,
		RetailerID:   transactionInfo.RetailerID,
	}
	if err := storage.CardTransactionD.Save(ctx, entity); err != nil {
		slog.FromContext(ctx).Warn(logAuth, "error saving card transaction record to DB", slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Info(logAuth, "successfully saved auth request: "+entity.RequestID)
	return entity, nil
}

//nolint:dupl
func (w *WorkflowImpl) persistRefundRequest(ctx context.Context, _ string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logReversalSettled, "Invalid context passed in persistRequest state")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	storageCardTransaction, err := persistNewCreateRefundRequest(ctx, nextCtx)
	if err != nil {
		return nil, err
	}

	nextCtx.Refund = *storageCardTransaction
	nextCtx.State = stRefundPrepared
	return nextCtx, nil
}

//nolint:dupl
func (w *WorkflowImpl) persistCancellationRequest(ctx context.Context, _ string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logReversalSettled, "Invalid context passed in persistRequest state")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	storageCardTransaction, err := persistNewCreateRefundRequest(ctx, nextCtx)
	if err != nil {
		return nil, err
	}

	nextCtx.Refund = *storageCardTransaction
	nextCtx.State = stCancellationPrepared
	return nextCtx, nil
}

func persistNewCreateRefundRequest(ctx context.Context, execData *ExecutionData) (*storage.Refund, error) {
	txnInfo := execData.PaynetReversalSettledRequest.TransactionReversalSettledRequest.TransactionInfo
	requestBody, _ := json.Marshal(execData.PaynetReversalSettledRequest.TransactionReversalSettledRequest)
	isReversed := common.ContainStringValue([]string{logic.ReversalMTI, logic.ReversalRepeatMTI}, txnInfo.Mti)
	// transactionID is optional for paynet transaction
	// it's only required by Mastercard scheme
	cardSchemeTransactionID := txnInfo.TransactionID
	if cardSchemeTransactionID == "" {
		cardSchemeTransactionID = txnInfo.RetrievalReferenceNumber
	}
	entity := storage.Refund{
		ProxyNumber:                   txnInfo.ProxyNumber,
		RequestAmount:                 txnInfo.TransactionAmount,
		RequestCurrency:               txnInfo.CardHolderCurrencyCode,
		OriginalAmount:                txnInfo.TransactionAmount,
		OriginalCurrency:              txnInfo.TransactionCurrencyCode,
		RefundAmount:                  txnInfo.TransactionAmount,
		RetrievalReferenceNumber:      txnInfo.RetrievalReferenceNumber,
		CardSchemeTransactionID:       cardSchemeTransactionID,
		OriginalInternalTransactionID: execData.CardTransaction.InternalTransactionID,
		InternalTransactionID:         uuid.NewString(),
		Status:                        transaction.StateToStatus(stInit),
		CreatedAt:                     time.Now(),
		UpdatedAt:                     time.Now(),
		ResponseBody:                  marshalEmptyRespJSON(),
		RequestBody:                   requestBody,
		NetworkID:                     txnInfo.NetworkID,
		ProcessingCode:                txnInfo.ProcessingCode,
		MerchantInfo:                  &dbtype.JSONMerchantInfo{TerminalID: txnInfo.CardAcceptorTerminalID},
		EuronetTransactionID:          txnInfo.SystemTraceAuditNumber,
		AuthorizationID:               txnInfo.AuthorizationID,
		AcceptorCountryCode:           logic.Alpha3CountryCodeMY,
		IsReversed:                    isReversed,
		LocalTransactionDatetime:      logic.ConvertLocalTransactionDateTime(txnInfo.LocalTransactionDateTime),
		RetailerID:                    txnInfo.RetailerID,
	}

	if err := storage.RefundD.Save(ctx, &entity); err != nil {
		slog.FromContext(ctx).Error(logReversalSettled, "RefundD.save error", slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Debug(logReversalSettled, fmt.Sprintf("successfully save Refund %s", entity.InternalTransactionID))

	return &entity, nil
}

func marshalAuthRequest(req *api.PaynetAuthorizeRequest_TransactionAuthorizeRequest) json.RawMessage {
	reqStr, _ := json.Marshal(req)
	return reqStr
}

func convertLocalTransactionDateTime(dateTime string) time.Time {
	// EN Format : MMDDhhmmss
	// Prepend Year YYYY to parse and store in db
	year := time.Now().Year()
	timeStamp, _ := time.Parse(logic.TimeFormat, fmt.Sprintf("%d%s", year, dateTime))
	return timeStamp.UTC()
}

func marshalEmptyPropertiesJSON() json.RawMessage {
	resp := &api.TransactionProperties{}
	respStr, _ := json.Marshal(resp)
	return respStr
}

func marshalEmptyRespJSON() json.RawMessage {
	resp := &api.TransactionResponse{}
	respStr, _ := json.Marshal(resp)
	return respStr
}
