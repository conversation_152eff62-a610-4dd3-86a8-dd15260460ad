package activate

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"

	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/mocks"

	"gitlab.myteksi.net/dakota/servus/v2/data"

	"github.com/stretchr/testify/mock"

	"github.com/google/uuid"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/card"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
)

var (
	cardID         = "1234"
	idempotencyKey = uuid.NewString()
)

func TestPhysicalCardActivate(t *testing.T) {
	mockUserCardMapping := storage.MockIUserCardMappingDAO{}
	storage.UserCardMappingD = &mockUserCardMapping
	mockCardActivity := storage.MockICardActivityDAO{}
	storage.CardActivityD = &mockCardActivity
	mockEuronet := &mocks.EuronetClient{}
	clientImpl := physicalCardActivateClientImpl{
		EuronetClient: mockEuronet,
		CountryConfig: &config.CountryConfig{AppHeaderConfig: config.AppHeaderConfig{}},
	}
	req := &api.PhysicalCardActivateRequest{
		CardID:         cardID,
		IdempotencyKey: idempotencyKey,
	}
	expectedUserMapping := &storage.UserCardMapping{
		InternalCardID:     cardID,
		CardProxyNumber:    "1234",
		CardSequenceNumber: 1,
		CreatedAt:          time.Now(),
		InternalCustomerID: "122",
		EuronetCustomerID:  "1233",
	}
	resp := &dto.ActivatePhysicalCardResponse{
		CardActivationResponse: dto.CardActivationResponse{
			Response: &dto.Response{
				ResponseCode: "00",
				Description:  "Success",
			},
		},
	}
	t.Run("Valid flow", func(t *testing.T) {
		mockUserCardMapping.On("Find", mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{expectedUserMapping}, nil).Once()
		mockCardActivity.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		mockCardActivity.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
		mockEuronet.On("ActivatePhysicalCard", mock.Anything, mock.Anything).Return(resp, nil).Once()
		mockCardActivity.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		resp, err := clientImpl.PhysicalCardActivate(context.Background(), req)
		assert.NotNil(t, resp)
		assert.Equal(t, req.IdempotencyKey, resp.ReferenceID)
		assert.Equal(t, "1234", resp.ProxyNumber)
		assert.Equal(t, string(logic.Success), resp.Status)
		assert.Empty(t, resp.StatusReason)
		assert.Nil(t, err)
	})
	t.Run("When fetchUserCardMapping Fails", func(t *testing.T) {
		mockUserCardMapping.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		resp, err := clientImpl.PhysicalCardActivate(context.Background(), req)
		expectedErr := logic.CustomError(http.StatusBadRequest, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   invalidCardID,
				Path:      noSuchCard,
			},
		})
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("When fetchCardActivity Fails", func(t *testing.T) {
		mockUserCardMapping.On("Find", mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{expectedUserMapping}, nil).Once()
		mockCardActivity.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		resp, err := clientImpl.PhysicalCardActivate(context.Background(), req)
		assert.Nil(t, resp)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.NotNil(t, err)
	})
	t.Run("When invokeEuronetActivateCard Fails", func(t *testing.T) {
		mockUserCardMapping.On("Find", mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{expectedUserMapping}, nil).Once()
		mockCardActivity.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		mockCardActivity.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
		mockEuronet.On("ActivatePhysicalCard", mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		resp, err := clientImpl.PhysicalCardActivate(context.Background(), req)
		assert.Nil(t, resp)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.NotNil(t, err)
	})
	t.Run("When updateActivateCardResp Fails", func(t *testing.T) {
		mockUserCardMapping.On("Find", mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{expectedUserMapping}, nil).Once()
		mockCardActivity.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		mockCardActivity.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
		mockEuronet.On("ActivatePhysicalCard", mock.Anything, mock.Anything).Return(resp, nil).Once()
		mockCardActivity.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(data.ErrNotSupported).Once()
		resp, err := clientImpl.PhysicalCardActivate(context.Background(), req)
		assert.Nil(t, resp)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.NotNil(t, err)
	})
}

func TestFetchUserCardMapping(t *testing.T) {
	clientImpl := physicalCardActivateClientImpl{}
	mockUserCardMapping := storage.MockIUserCardMappingDAO{}
	storage.UserCardMappingD = &mockUserCardMapping
	req := &api.PhysicalCardActivateRequest{
		CardID:         cardID,
		IdempotencyKey: idempotencyKey,
	}
	expectedUserMapping := &storage.UserCardMapping{
		InternalCardID:     cardID,
		CardProxyNumber:    "1234",
		CardSequenceNumber: 1,
		CreatedAt:          time.Now(),
		InternalCustomerID: "122",
		EuronetCustomerID:  "1233",
	}
	t.Run("Valid flow", func(t *testing.T) {
		mockUserCardMapping.On("Find", mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{expectedUserMapping}, nil).Once()
		userCardMapping, err := clientImpl.fetchUserCardMapping(context.Background(), req)
		assert.NotNil(t, userCardMapping)
		assert.Equal(t, req.CardID, userCardMapping.InternalCardID)
		assert.Equal(t, "1234", userCardMapping.CardProxyNumber)
		assert.Nil(t, err)
	})
	t.Run("storage.UserCardMappingD.Find Fails", func(t *testing.T) {
		mockUserCardMapping.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		userCardMapping, err := clientImpl.fetchUserCardMapping(context.Background(), req)
		expectedErr := logic.CustomError(http.StatusBadRequest, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   invalidCardID,
				Path:      noSuchCard,
			},
		})
		assert.Nil(t, userCardMapping)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestFetchCardActivity(t *testing.T) {
	clientImpl := physicalCardActivateClientImpl{
		CountryConfig: &config.CountryConfig{AppHeaderConfig: config.AppHeaderConfig{}},
	}
	mockCardActivity := storage.MockICardActivityDAO{}
	storage.CardActivityD = &mockCardActivity
	req := &api.PhysicalCardActivateRequest{
		CardID:         cardID,
		IdempotencyKey: idempotencyKey,
	}
	userMapping := &storage.UserCardMapping{
		InternalCardID:     cardID,
		CardProxyNumber:    "1234",
		CardSequenceNumber: 1,
		CreatedAt:          time.Now(),
		InternalCustomerID: "122",
		EuronetCustomerID:  "1233",
	}

	expectedCardActivity := &storage.CardActivity{
		InternalCardID: cardID,
		RequestID:      idempotencyKey,
	}

	t.Run("Valid flow - When storage.CardActivity.Find - Success", func(t *testing.T) {
		mockCardActivity.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardActivity{expectedCardActivity}, nil).Once()
		cardActivity, err := clientImpl.fetchCardActivity(context.Background(), req, userMapping)
		assert.NotNil(t, cardActivity)
		assert.Equal(t, req.CardID, cardActivity.InternalCardID)
		assert.Equal(t, req.IdempotencyKey, cardActivity.RequestID)
		assert.Nil(t, err)
	})
	t.Run("Valid flow - When storage.CardActivity.Find - No Data found", func(t *testing.T) {
		mockCardActivity.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		mockCardActivity.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
		cardActivity, err := clientImpl.fetchCardActivity(context.Background(), req, userMapping)
		assert.NotNil(t, cardActivity)
		assert.Equal(t, req.CardID, cardActivity.InternalCardID)
		assert.Equal(t, req.IdempotencyKey, cardActivity.RequestID)
		assert.Nil(t, err)
	})
	t.Run("Valid flow - When storage.CardActivity.Find - Fails", func(t *testing.T) {
		mockCardActivity.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		cardActivity, err := clientImpl.fetchCardActivity(context.Background(), req, userMapping)
		assert.Nil(t, cardActivity)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.NotNil(t, err)
	})
	t.Run("When storage.CardActivityD.Save Fails", func(t *testing.T) {
		mockCardActivity.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		mockCardActivity.On("Save", mock.Anything, mock.Anything).Return(data.ErrNotSupported).Once()
		cardActivity, err := clientImpl.fetchCardActivity(context.Background(), req, userMapping)
		assert.Nil(t, cardActivity)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.NotNil(t, err)
	})
}

func TestCreateCardActivationEntry(t *testing.T) {
	clientImpl := physicalCardActivateClientImpl{
		CountryConfig: &config.CountryConfig{AppHeaderConfig: config.AppHeaderConfig{}},
	}
	mockCardActivity := storage.MockICardActivityDAO{}
	storage.CardActivityD = &mockCardActivity
	req := &api.PhysicalCardActivateRequest{
		CardID:         cardID,
		IdempotencyKey: idempotencyKey,
	}
	userMapping := &storage.UserCardMapping{
		InternalCardID:     cardID,
		CardProxyNumber:    "1234",
		CardSequenceNumber: 1,
		CreatedAt:          time.Now(),
		InternalCustomerID: "122",
		EuronetCustomerID:  "1233",
	}
	t.Run("Valid flow", func(t *testing.T) {
		mockCardActivity.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
		cardActivity, err := clientImpl.createCardActivationEntry(context.Background(), req, userMapping)
		assert.NotNil(t, cardActivity)
		assert.Equal(t, req.CardID, cardActivity.InternalCardID)
		assert.Equal(t, req.IdempotencyKey, cardActivity.RequestID)
		assert.Nil(t, err)
	})
	t.Run("When storage.CardActivityD.Save Fails", func(t *testing.T) {
		mockCardActivity.On("Save", mock.Anything, mock.Anything).Return(data.ErrNotSupported).Once()
		cardActivity, err := clientImpl.createCardActivationEntry(context.Background(), req, userMapping)
		assert.Nil(t, cardActivity)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.NotNil(t, err)
	})
}

func TestConstructCardActivationRequest(t *testing.T) {
	clientImpl := physicalCardActivateClientImpl{
		CountryConfig: &config.CountryConfig{AppHeaderConfig: config.AppHeaderConfig{}},
	}
	req := &api.PhysicalCardActivateRequest{
		CardID:         cardID,
		IdempotencyKey: idempotencyKey,
	}
	userMapping := &storage.UserCardMapping{
		InternalCardID:     cardID,
		CardProxyNumber:    "1234",
		CardSequenceNumber: 1,
		CreatedAt:          time.Now(),
		InternalCustomerID: "122",
		EuronetCustomerID:  "1233",
	}
	t.Run("Valid flow", func(t *testing.T) {
		jsonRequest := clientImpl.constructCardActivationRequest(req, userMapping)
		assert.NotNil(t, jsonRequest)
	})
}

func TestInvokeEuronetActivateCard(t *testing.T) {
	mockEuronet := &mocks.EuronetClient{}
	clientImpl := physicalCardActivateClientImpl{
		EuronetClient: mockEuronet,
		CountryConfig: &config.CountryConfig{AppHeaderConfig: config.AppHeaderConfig{}},
	}
	req := &api.PhysicalCardActivateRequest{
		CardID:         cardID,
		IdempotencyKey: idempotencyKey,
	}
	userMapping := &storage.UserCardMapping{
		InternalCardID:     cardID,
		CardProxyNumber:    "1234",
		CardSequenceNumber: 1,
		CreatedAt:          time.Now(),
		InternalCustomerID: "122",
		EuronetCustomerID:  "1233",
	}
	resp := &dto.ActivatePhysicalCardResponse{
		CardActivationResponse: dto.CardActivationResponse{
			Response: &dto.Response{
				ResponseCode: "00",
				Description:  "Success",
			},
		},
	}
	jsonRequest := clientImpl.constructCardActivationRequest(req, userMapping)
	cardActivity := &storage.CardActivity{
		InternalCardID: cardID,
		RequestID:      idempotencyKey,
		RequestBody:    jsonRequest,
	}
	t.Run("Valid flow", func(t *testing.T) {
		mockEuronet.On("ActivatePhysicalCard", mock.Anything, mock.Anything).Return(resp, nil).Once()
		response, err := clientImpl.invokeEuronetActivateCard(context.Background(), cardActivity)
		assert.Equal(t, "00", response.CardActivationResponse.Response.ResponseCode)
		assert.Nil(t, err)
	})
	t.Run("When cardActivity.RequestBody:json.Unmarshal Fails", func(t *testing.T) {
		cardActivity.RequestBody = json.RawMessage{}
		mockEuronet.On("ActivatePhysicalCard", mock.Anything, mock.Anything).Return(resp, nil).Once()
		response, err := clientImpl.invokeEuronetActivateCard(context.Background(), cardActivity)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
	t.Run("When EuronetClient.ActivatePhysicalCard Fails", func(t *testing.T) {
		mockEuronet.On("ActivatePhysicalCard", mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		response, err := clientImpl.invokeEuronetActivateCard(context.Background(), cardActivity)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
}

func TestUpdateActivateCardResp(t *testing.T) {
	clientImpl := physicalCardActivateClientImpl{}
	mockCardActivity := storage.MockICardActivityDAO{}
	storage.CardActivityD = &mockCardActivity

	resp := &dto.ActivatePhysicalCardResponse{
		CardActivationResponse: dto.CardActivationResponse{
			Response: &dto.Response{
				ResponseCode: "00",
				Description:  "Success",
			},
		},
	}

	cardActivity := &storage.CardActivity{
		InternalCardID: cardID,
		RequestID:      idempotencyKey,
	}

	t.Run("Valid flow", func(t *testing.T) {
		mockCardActivity.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		response, err := clientImpl.updateActivateCardResp(context.Background(), cardActivity, resp)
		assert.Equal(t, string(logic.Success), response.Status)
		assert.Nil(t, err)
	})
	t.Run("When storage.CardActivity.UpdateEntity Fails", func(t *testing.T) {
		mockCardActivity.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(data.ErrNotSupported).Once()
		response, err := clientImpl.updateActivateCardResp(context.Background(), cardActivity, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
}

func TestGetResponseStatus(t *testing.T) {
	clientImpl := physicalCardActivateClientImpl{}
	t.Run("Valid flow", func(t *testing.T) {
		resp := &dto.ActivatePhysicalCardResponse{
			CardActivationResponse: dto.CardActivationResponse{
				Response: &dto.Response{
					ResponseCode: "00",
					Description:  "Success",
				},
			},
		}
		status, reason := clientImpl.getResponseStatus(resp)
		assert.Equal(t, string(logic.Success), status)
		assert.Empty(t, reason)
	})
	t.Run("Response code is 11 and Reason is 'Card already Activated'", func(t *testing.T) {
		resp := &dto.ActivatePhysicalCardResponse{
			CardActivationResponse: dto.CardActivationResponse{
				Response: &dto.Response{
					ResponseCode: "11",
					Description:  "Card already Activated",
				},
			},
		}
		status, reason := clientImpl.getResponseStatus(resp)
		assert.Equal(t, string(logic.Success), status)
		assert.Empty(t, reason)
	})
	t.Run("Response code is 11 and Reason is not 'Card already Activated'", func(t *testing.T) {
		resp := &dto.ActivatePhysicalCardResponse{
			CardActivationResponse: dto.CardActivationResponse{
				Response: &dto.Response{
					ResponseCode: "11",
					Description:  "Entity update failed",
				},
			},
		}
		status, reason := clientImpl.getResponseStatus(resp)
		assert.Equal(t, string(logic.Failed), status)
		assert.Equal(t, card.ErrEntityUpdateFailed.Error(), reason)
	})
	t.Run("Invalid Response code", func(t *testing.T) {
		resp := &dto.ActivatePhysicalCardResponse{
			CardActivationResponse: dto.CardActivationResponse{
				Response: &dto.Response{
					ResponseCode: "200",
					Description:  "Entity update failed",
				},
			},
		}
		status, reason := clientImpl.getResponseStatus(resp)
		assert.Equal(t, string(logic.Failed), status)
		assert.Equal(t, logic.ErrInvalidEuronetErrorCode.Error(), reason)
	})
}
