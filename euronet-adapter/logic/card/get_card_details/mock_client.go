// Code generated by mockery v2.20.0. DO NOT EDIT.

package getcarddetails

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"

	mock "github.com/stretchr/testify/mock"
)

// MockClient is an autogenerated mock type for the Client type
type MockClient struct {
	mock.Mock
}

// GetEncryptedCVV2CardDetails provides a mock function with given fields: ctx, request
func (_m *MockClient) GetEncryptedCVV2CardDetails(ctx context.Context, request *api.GetEncryptedCVV2CardDetailsRequest) (*api.GetEncryptedCVV2CardDetailsResponse, error) {
	ret := _m.Called(ctx, request)

	var r0 *api.GetEncryptedCVV2CardDetailsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetEncryptedCVV2CardDetailsRequest) (*api.GetEncryptedCVV2CardDetailsResponse, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetEncryptedCVV2CardDetailsRequest) *api.GetEncryptedCVV2CardDetailsResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetEncryptedCVV2CardDetailsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetEncryptedCVV2CardDetailsRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockClient creates a new instance of MockClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockClient(t mockConstructorTestingTNewMockClient) *MockClient {
	mock := &MockClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
