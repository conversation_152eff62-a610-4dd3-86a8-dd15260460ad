package issuance

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"math/big"
	"reflect"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/card"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func (w *WorkflowImpl) persistRequest(ctx context.Context, transitionID string, exec workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*ExecutionData)
	if !ok {
		return nil, logic.ErrInvalidContext
	}

	nextCtx := currCtx.Clone()

	var enCustomerID string
	if nextCtx.CardIssuanceRequest.CardIssuanceType == string(constant.VirtualCard) {
		enCustomerID = generateEuronetCustomerID(ctx)
	} else {
		enCustomerID = nextCtx.UserCardMapping.EuronetCustomerID
	}
	// generate euronet card issuance request payload and persist into card activty table
	storageCardActivity, err := persistCardActivity(ctx, nextCtx, enCustomerID, w.CountryConfig)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "CardActivity.save error", slog.Error(err))
		return nil, err
	}

	nextCtx.CardActivity = *storageCardActivity
	nextCtx.EuronetCustomerID = enCustomerID
	nextCtx.State = stRequestPersisted

	return nextCtx, nil
}

func persistCardActivity(ctx context.Context, execData *ExecutionData, enCustomerID string, countryConfig *config.CountryConfig) (*storage.CardActivity, error) {
	var servicename string
	if execData.CardIssuanceRequest.CardIssuanceType == string(constant.VirtualCard) {
		servicename = logic.ServiceIssueNewCard
	} else {
		servicename = logic.ServiceRequestPlastic
	}
	enRequestBody := constructENIssuanceRequest(execData, servicename, enCustomerID, countryConfig)
	enRequestBodyJSON, err := json.Marshal(enRequestBody)
	if err != nil {
		return nil, err
	}
	newEntry := &storage.CardActivity{
		RequestID:      execData.CardIssuanceRequest.IdempotencyKey,
		InternalCardID: execData.CardIssuanceRequest.CardID,
		ServiceName:    servicename,
		RequestBody:    enRequestBodyJSON,
		// save empty resp json to avoid scan error when loading the entry from db
		ResponseBody: marshalEmptyRespJSON(),
		Status:       string(constant.CardStatusProcessing),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err = storage.CardActivityD.Save(ctx, newEntry); err != nil {
		return nil, err
	}
	return newEntry, nil
}

//nolint:funlen
func constructENIssuanceRequest(execData *ExecutionData, serviceName string, enCustomerID string, countryConfig *config.CountryConfig) *dto.IssuanceRequest {
	// MF indicates Mandatory Field
	debitCardIssuanceReq := &dto.DebitCardIssuanceRequest{
		AppHeader: card.GetNewAppHeaderWithConfig(execData.CardIssuanceRequest.IdempotencyKey, serviceName, countryConfig.AppHeaderConfig), //MF
		CardInfo:  &dto.CardInfo{},
		CustomerInfo: &dto.CustomerInfo{
			CustomerType: card.CustomerTypeIndividual,              //MF
			CustomerID:   enCustomerID,                             //MF
			FirstName:    execData.CardIssuanceRequest.DisplayName, //MF
		},
		AddressInfo: []*dto.AddressInfo{},
		PhoneInfo: []*dto.PhoneInfo{
			{
				PhoneType:   card.Mobile,          //MF
				PrimaryFlag: logic.PrimaryFlagYes, //MF
			},
		},
		AccountInfo: []*dto.AccountInfo{
			{
				AccountType:   logic.AccountTypeSaving,   //MF
				AccountNumber: enCustomerID,              //MF
				CurrencyCode:  countryConfig.CurrencyNum, //MF
				AccountBranch: card.AccountBranch,        //MF
				AccountStatus: string(logic.Active),      //MF
				PrimaryFlag:   logic.PrimaryFlagYes,      //MF
			},
		},
	}
	switch execData.CardIssuanceRequest.CardIssuanceType {
	case string(constant.PhysicalCard):
		debitCardIssuanceReq.CardInfo = &dto.CardInfo{
			CardType:         card.CardTypeMDP,          //MF
			CardIssuanceType: card.IssuanceTypePhysical, //MF
			ProxyNumber:      execData.UserCardMapping.CardProxyNumber,
			CardSeqNumber:    int64(execData.UserCardMapping.CardSequenceNumber),
			EmbossLine1:      execData.CardIssuanceRequest.DisplayName,
			PinMailerFlag:    logic.NoValue,
			//TODO EncodeFirstName EncodeLastName
			EmbossingType: logic.EmbossingTypeBatch, //TODO: TBC
			AdditionalInfo: &dto.AdditionalInfo{
				ActivationCode: execData.CardIssuanceRequest.ActivationCode,
			},
		}
		cleanseAddressFields(execData.CardIssuanceRequest.MailingAddress)
		debitCardIssuanceReq.AddressInfo = append(debitCardIssuanceReq.AddressInfo, &dto.AddressInfo{
			AddressType:  logic.AddressTypeDelivery,                                             //MF
			AddressLine1: ensureNonEmpty(execData.CardIssuanceRequest.MailingAddress.AddrLine1), //MF
			AddressLine2: execData.CardIssuanceRequest.MailingAddress.AddrLine2,
			City:         ensureNonEmpty(execData.CardIssuanceRequest.MailingAddress.City),       //MF
			State:        ensureNonEmpty(execData.CardIssuanceRequest.MailingAddress.State),      //MF
			PrimaryFlag:  logic.PrimaryFlagYes,                                                   //MF
			ZipCode:      ensureNonEmpty(execData.CardIssuanceRequest.MailingAddress.PostalCode), //MF
			CountryCode:  countryConfig.CountryCode,
		})
		debitCardIssuanceReq.PhoneInfo[0].PhoneNumber = execData.CardIssuanceRequest.PhoneNumber //MF
	case string(constant.VirtualCard):
		debitCardIssuanceReq.CardInfo = &dto.CardInfo{
			CardType:         card.CardTypeMDP,
			CardIssuanceType: card.IssuanceTypeVirtual,
			EmbossLine1:      execData.CardIssuanceRequest.DisplayName,
			PinMailerFlag:    logic.NoValue,
			EmbossingType:    logic.EmbossingTypeBatch,
		}
		debitCardIssuanceReq.AddressInfo = append(debitCardIssuanceReq.AddressInfo, &dto.AddressInfo{
			AddressType:  logic.AddressTypeHome,
			AddressLine1: logic.NotApplicable,
			City:         logic.NotApplicable,
			State:        logic.NotApplicable,
			PrimaryFlag:  logic.PrimaryFlagYes,
			ZipCode:      logic.NotApplicable,
			CountryCode:  logic.NotApplicable,
		})
		debitCardIssuanceReq.PhoneInfo[0].PhoneNumber = logic.NotApplicable
	}

	return &dto.IssuanceRequest{
		DebitCardIssuanceRequest: debitCardIssuanceReq,
	}
}

func generateEuronetCustomerID(ctx context.Context) string {
	randomString := generateNumericString(20)
	_, err := storage.UserCardMappingD.Find(ctx,
		data.EqualTo("EuronetCustomerID", randomString),
	)
	if err == data.ErrNoData {
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Generated Euronet CustomerID: %s", randomString))
		return randomString
	}
	// try again
	return generateNumericString(20)
}

func generateNumericString(length int) string {
	stringArr := make([]string, length)
	for idx := range stringArr {
		randomInt, _ := rand.Int(rand.Reader, big.NewInt(10))
		stringArr[idx] = randomInt.String()
	}
	return strings.Join(stringArr, "")
}

func marshalEmptyRespJSON() json.RawMessage {
	resp := &dto.IssuanceResponse{}
	respStr, _ := json.Marshal(resp)
	return respStr
}

func getAddressLine(mailingAdr *api.MailingAddress) string {
	// TODO: Check if this address line approach is valid
	addressLine := mailingAdr.Street
	if mailingAdr.Block != "" {
		addressLine = fmt.Sprintf("%s Blk %s", addressLine, mailingAdr.Block)
	}
	if mailingAdr.Unit != "" {
		addressLine = fmt.Sprintf("%s %s", addressLine, mailingAdr.Unit)
	}
	return addressLine
}

func ensureNonEmpty(s string) string {
	if s == "" {
		return logic.NotApplicable
	}
	return s
}

func cleanseAddressFields(mailingAddress *api.MailingAddress) {

	val := reflect.ValueOf(mailingAddress).Elem()

	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		if field.Kind() == reflect.String {
			field.SetString(common.RemoveNonASCIIChar(field.String()))
			field.SetString(common.ReplaceLineBreak(field.String()))
		}
	}
}
