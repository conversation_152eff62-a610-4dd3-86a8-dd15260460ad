// Package pinmanager ...
package pinmanager

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/card"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"

	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external"
)

const (
	logTag        = "CardSetPin"
	invalidCardID = "Invalid Card ID"
	noSuchCard    = "No such card found"
)

// CardPinManagerClient defines the client to activate physical card.
type CardPinManagerClient interface {
	// SetPin - set Pin in EN...
	SetPin(ctx context.Context, request *api.CardSetPinRequest) (*api.CardSetPinResponse, error)
}

//go:generate mockery --name CardPinManagerClient --inpackage --case=underscore

// NewClient creates a CardPinManagerClient client.
func NewClient() CardPinManagerClient {
	return &cardSetPinClientImpl{}
}

type cardSetPinClientImpl struct {
	EuronetClient external.EuronetClient `inject:"client.euronet"`
	CountryConfig *config.CountryConfig  `inject:"config.countryConfig"`
}

// SetPin ...
func (c *cardSetPinClientImpl) SetPin(ctx context.Context, request *api.CardSetPinRequest) (*api.CardSetPinResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, request.IdempotencyKey), slog.CustomTag(logic.CardIDTag, request.CardID))

	userCardMapping, err := c.fetchUserCardMapping(ctx, request)
	if err != nil {
		return nil, err
	}

	// fetch card activity
	cardActivity, err := c.fetchCardActivity(ctx, request, userCardMapping)
	if err != nil {
		return nil, err
	}

	resp, err := c.invokeEuronetCardSetPin(ctx, cardActivity)
	if err != nil {
		return nil, err
	}

	result, err := c.updateCardActivity(ctx, cardActivity, resp)
	if err != nil {
		return nil, err
	}

	result.CardSequenceNumber = int64(userCardMapping.CardSequenceNumber)
	result.ProxyNumber = userCardMapping.CardProxyNumber

	return result, nil
}

func (c *cardSetPinClientImpl) fetchCardActivity(ctx context.Context, request *api.CardSetPinRequest, userCardMapping *storage.UserCardMapping) (*storage.CardActivity, error) {
	cardActivity, err := storage.CardActivityD.Find(ctx, data.EqualTo("RequestID", request.IdempotencyKey))

	if err != nil {
		if err == data.ErrNoData {
			return c.createCardSetPinEntry(ctx, request, userCardMapping)
		}
		slog.FromContext(ctx).Error(logTag, "error loading card activity record from DB", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}
	return cardActivity[0], nil
}

func (c *cardSetPinClientImpl) createCardSetPinEntry(ctx context.Context, request *api.CardSetPinRequest, userCardMapping *storage.UserCardMapping) (*storage.CardActivity, error) {
	newEntry := &storage.CardActivity{
		RequestID:      request.IdempotencyKey,
		InternalCardID: request.CardID,
		ServiceName:    logic.ServiceCardSetPIN,
		RequestBody:    c.constructCardSetPinRequest(request, userCardMapping),
		ResponseBody:   marshalEmptyRespJSON(),
		Status:         string(logic.Processing),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	if err := storage.CardActivityD.Save(ctx, newEntry); err != nil {
		slog.FromContext(ctx).Error(logTag, "error saving card activity record to DB", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}
	return newEntry, nil
}

func (c *cardSetPinClientImpl) constructCardSetPinRequest(request *api.CardSetPinRequest, userCardMapping *storage.UserCardMapping) json.RawMessage {
	reqObj := &dto.CardPinMgmtAPIRequest{
		CardPinMgmtRequest: &dto.CardPinMgmtRequest{
			AppHeader: card.GetNewAppHeaderWithConfig(request.IdempotencyKey, logic.ServiceCardSetPIN, c.CountryConfig.AppHeaderConfig),
			CardParams: &dto.CardPinParams{
				ProxyNumber:   userCardMapping.CardProxyNumber,
				CardSeqNumber: int64(userCardMapping.CardSequenceNumber),
			},
			PinParams: &dto.PinParams{EncryptedPin: request.EncryptedPin},
		},
		Signature: "", //TODO signature
	}

	reqStr, _ := json.Marshal(reqObj)
	return reqStr
}

func marshalEmptyRespJSON() json.RawMessage {
	resp := &dto.CardPinMgmtAPIResponse{}
	respStr, _ := json.Marshal(resp)
	return respStr
}

func (c *cardSetPinClientImpl) fetchUserCardMapping(ctx context.Context, request *api.CardSetPinRequest) (*storage.UserCardMapping, error) {
	query := []data.Condition{
		data.EqualTo("InternalCardID", request.CardID),
		data.EqualTo("InternalCustomerID", logic.GetUserID(ctx)),
	}
	querySearch, err := storage.UserCardMappingD.Find(ctx, query...)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error in finding card ID", slog.Error(err))
		return nil, logic.CustomError(http.StatusBadRequest, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   invalidCardID,
				Path:      noSuchCard,
			},
		})
	}
	return querySearch[0], nil
}

func (c *cardSetPinClientImpl) invokeEuronetCardSetPin(ctx context.Context, cardActivity *storage.CardActivity) (*dto.CardPinMgmtAPIResponse, error) {
	euronetReq := &dto.CardPinMgmtAPIRequest{}
	err := json.Unmarshal(cardActivity.RequestBody, euronetReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error occurred while unmarshalling card set pin request", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	resp, err := c.EuronetClient.CardSetPin(ctx, euronetReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error calling euronet card set pin api", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	return resp, nil
}

func (c *cardSetPinClientImpl) updateCardActivity(ctx context.Context, cardActivity *storage.CardActivity, resp *dto.CardPinMgmtAPIResponse) (*api.CardSetPinResponse, error) {
	respJSON, _ := json.Marshal(resp)

	updatedCardActivity := *cardActivity
	updatedCardActivity.UpdatedAt = time.Now()
	updatedCardActivity.ResponseBody = respJSON
	updatedCardActivity.ResponseCode = resp.CardPinMgmtResponse.Response.ResponseCode

	respStatus, respStatusReason := c.getResponseStatus(resp)
	updatedCardActivity.Status = respStatus

	if err := storage.CardActivityD.UpdateEntity(ctx, cardActivity, &updatedCardActivity); err != nil {
		slog.FromContext(ctx).Warn(logTag, "error updating card set pin response to DB", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	response := &api.CardSetPinResponse{
		Status:       respStatus,
		StatusReason: respStatusReason,
		ReferenceID:  cardActivity.RequestID,
	}
	return response, nil
}

func (c *cardSetPinClientImpl) getResponseStatus(resp *dto.CardPinMgmtAPIResponse) (string, string) {
	responseCode := resp.CardPinMgmtResponse.Response.ResponseCode
	responseDesc := resp.CardPinMgmtResponse.Response.Description
	errObj, ok := card.EuronetErrorCodes[responseCode]

	if !ok {
		return string(logic.Failed), logic.ErrInvalidEuronetErrorCode.Error()
	} else if errObj != nil {
		// TODO compare desc msg
		if responseDesc != "TODO" {
			return string(logic.Failed), errObj.Error()
		}
	}
	return string(logic.Success), ""
}
