package clearing

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/uuid"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

func (c *clearingLogicImpl) rowToClearingRecord(ctx context.Context, fileProperties *FileProperties, lineNumber int, recordArr []string) (*storage.ClearingRecord, error) {
	cr, err := c.newClearingRecordStorage(ctx, fileProperties, lineNumber, recordArr)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "newClearingRecordStorage err", slog.CustomTag(string(constant.Clearing), recordArr), slog.Error(err))
		return c.newInvalidClearingRecordStorage(ctx, fileProperties, lineNumber, recordArr, err)
	}
	return cr, nil
}

func (c *clearingLogicImpl) newInvalidClearingRecordStorage(ctx context.Context, fileProperties *FileProperties, lineNumber int, recordArr []string, reason error) (*storage.ClearingRecord, error) {
	for i, rec := range recordArr {
		recordArr[i] = strings.ToValidUTF8(rec, "") // convert to valid utf8
	}
	rowData, err := json.Marshal(&RowData{Arr: recordArr})
	if err != nil {
		return nil, err
	}
	// save raw data to invalid record
	rowData, marshalErr := json.Marshal(&RowData{Arr: recordArr})
	if marshalErr != nil {
		return nil, marshalErr
	}
	return &storage.ClearingRecord{
		Status:         "INVALID",
		StatusReason:   reason.Error(),
		SettlementDate: fileProperties.SettlementDate,
		Source:         fileProperties.FileName,
		LineNumber:     lineNumber,
		ReferenceID:    uuid.NewString(), // no fix uuid since row is invalid
		Data:           rowData,
	}, nil
}

// Function to split slice into smaller batches and save each batch
func (c *clearingLogicImpl) saveBatch(ctx context.Context, arr []*storage.ClearingRecord, batchSize int) error {
	// Iterate over the data slice and create smaller batches
	for i := 0; i < len(arr); i += batchSize {
		// Define the end index of the batch
		end := i + batchSize
		if end > len(arr) {
			end = len(arr)
		}
		// Create a batch slice
		batch := arr[i:end]

		// Save the batch
		persistErr := c.ClearingRecordDBHelper.SaveBatch(ctx, batch)
		if persistErr != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("ClearingRecordDBHelper.SaveBatch batch %v err...", i), slog.Error(persistErr))
			continue
		}
	}
	return nil
}

func (c *clearingLogicImpl) newClearingRecordStorage(ctx context.Context, fileProperties *FileProperties, lineNumber int, record []string) (*storage.ClearingRecord, error) {
	if len(record) != ClearingFileRecordLength {
		return nil, errors.New("wrong number of columns")
	}
	if len(record[TransactionDetailsIndexProcessingCode]) < 2 {
		return nil, errors.New("invalid processing code")
	}
	originalAmount, err := strconv.ParseInt(record[TransactionDetailsIndexOriginalAmount], 10, 64)
	if err != nil {
		return nil, errors.New("failed to parse original amount")
	}
	settlementAmount, err := strconv.ParseInt(record[TransactionDetailsIndexSettlementAmount], 10, 64)
	if err != nil {
		return nil, errors.New("failed to parse settlement amount")
	}
	if len(record[TransactionDetailsIndexPosEntryMode]) < 7 {
		return nil, errors.New("pos entry mode has invalid length")
	}
	panEntryMode, ok := getPanEntryMode(record[TransactionDetailsIndexPosEntryMode], record[TransactionDetailsIndexProcessingCode]) // 7th element of PosEntryMode, and processing code
	if !ok {
		return nil, errors.New("failed to parse pos entry mode: " + record[TransactionDetailsIndexPosEntryMode])
	}
	originalCurrency := record[TransactionDetailsIndexOriginalCurrency]
	cardHolderCurrency := strings.TrimSpace(record[TransactionDetailsIndexCardHolderCurrency])
	conversionRate, err := convertConversionToEuronetFormat(record[TransactionDetailsIndexConversionRate])
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("failed to convert Conversion %s", record[TransactionDetailsIndexConversionRate]), slog.Error(err))
	}

	for i, rec := range record {
		record[i] = strings.ToValidUTF8(rec, "") // convert to valid utf8
	}

	rowData, err := json.Marshal(&RowData{Arr: record, OriginalAmount: originalAmount})
	if err != nil {
		return nil, err
	}

	crDto := &dto.ClearingRecord{
		ProxyNumber:              strings.TrimSpace(record[TransactionDetailsIndexProxyNumber]),
		ProcessingCode:           record[TransactionDetailsIndexProcessingCode],
		OriginalAmount:           originalAmount,
		CardHolderAmount:         settlementAmount,
		LocalTxnDateTime:         record[TransactionDetailsIndexLocalTxnDateTime],
		MCC:                      record[TransactionDetailsIndexMCC],
		PANEntryMode:             panEntryMode,
		AcquirerReferenceData:    record[TransactionDetailsIndexAcquirerReferenceData],
		AcquirerID:               record[TransactionDetailsIndexAcquirerID],
		RetrievalReferenceNumber: strings.TrimSpace(record[TransactionDetailsIndexRetrievalReferenceNumber]),
		ApprovalCode:             strings.TrimSpace(record[TransactionDetailsIndexApprovalCode]),
		AcceptorTerminalID:       record[TransactionDetailsIndexAcceptorTerminalID],
		AcceptorIDCode:           record[TransactionDetailsIndexAcceptorIDCode],
		AcceptorNameLocation:     record[TransactionDetailsIndexAcceptorNameLocation],
		OriginalCurrency:         originalCurrency,
		CardHolderCurrencyCode:   cardHolderCurrency,
		TransactionLifeCycleID:   strings.TrimSpace(record[TransactionDetailsIndexTransactionLifeCycleID]),
		ReversalIndicator:        record[TransactionDetailsIndexReversalIndicator],
		ConversionRate:           conversionRate,
		MRC:                      record[TransactionDetailsIndexMRC],
	}
	referenceID := uuid.StringToUUID(strings.Join([]string{strconv.Itoa(lineNumber), fileProperties.FileName, crDto.RetrievalReferenceNumber}, ""))
	return &storage.ClearingRecord{
		QueryAC:                  "", // to be updated on processing
		OriginalRequestID:        "", // to be updated on processing
		InternalTransactionID:    "", // to be updated on processing
		Status:                   "", // to be updated on processing
		StatusReason:             "", // to be updated on processing
		Type:                     "", // to be updated on processing
		SettlementDate:           fileProperties.SettlementDate,
		Source:                   fileProperties.FileName,
		LineNumber:               lineNumber,
		ReferenceID:              referenceID,
		ProxyNumber:              crDto.ProxyNumber,
		ProcessingCode:           crDto.ProcessingCode,
		SettlementAmount:         crDto.CardHolderAmount,
		SettlementCurrency:       crDto.CardHolderCurrencyCode,
		LocalTransactionDatetime: crDto.LocalTxnDateTime,
		PanEntryMode:             crDto.PANEntryMode,
		MCC:                      crDto.MCC,
		AcquirerReferenceData:    crDto.AcquirerReferenceData,
		AcquirerID:               crDto.AcquirerID,
		RetrievalReferenceNumber: crDto.RetrievalReferenceNumber,
		ApprovalCode:             crDto.ApprovalCode,
		CardAcceptorTerminalID:   crDto.AcceptorTerminalID,
		CardAcceptorIDCode:       crDto.AcceptorIDCode,
		CardAcceptorName:         strings.ToValidUTF8(crDto.AcceptorNameLocation, ""), // to avoid invalid utf8 name
		CardSchemeTransactionID:  crDto.TransactionLifeCycleID,                        // to be updated on processing
		MessageReversalIndicator: crDto.ReversalIndicator,
		MessageReasonCode:        crDto.MRC,
		Data:                     rowData,
	}, nil
}
