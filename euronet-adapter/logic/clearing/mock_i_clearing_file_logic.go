// Code generated by mockery v2.45.0. DO NOT EDIT.

package clearing

import (
	context "context"
	io "io"

	mock "github.com/stretchr/testify/mock"
)

// MockIClearingFileLogic is an autogenerated mock type for the IClearingFileLogic type
type MockIClearingFileLogic struct {
	mock.Mock
}

// PersistClearingRecordsFromCSV provides a mock function with given fields: ctx, fileProperties, reader
func (_m *MockIClearingFileLogic) PersistClearingRecordsFromCSV(ctx context.Context, fileProperties *FileProperties, reader io.ReadSeeker) error {
	ret := _m.Called(ctx, fileProperties, reader)

	if len(ret) == 0 {
		panic("no return value specified for PersistClearingRecordsFromCSV")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *FileProperties, io.ReadSeeker) error); ok {
		r0 = rf(ctx, fileProperties, reader)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ProcessClearingRecord provides a mock function with given fields: ctx, request
func (_m *MockIClearingFileLogic) ProcessClearingRecord(ctx context.Context, request *ProcessClearingRecordRequest) error {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for ProcessClearingRecord")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProcessClearingRecordRequest) error); ok {
		r0 = rf(ctx, request)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMockIClearingFileLogic creates a new instance of MockIClearingFileLogic. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockIClearingFileLogic(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockIClearingFileLogic {
	mock := &MockIClearingFileLogic{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
