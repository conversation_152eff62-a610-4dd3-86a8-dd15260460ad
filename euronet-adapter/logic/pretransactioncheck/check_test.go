package pretransactioncheck

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	gdProxyMock "gitlab.myteksi.net/dbmy/gd-proxy/api/mock"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	gdProxyAPI "gitlab.myteksi.net/dbmy/gd-proxy/api"
)

func TestPreTransactionCheck(t *testing.T) {
	type TestTransactionRequest struct {
		ProxyNumber string
	}

	scenarios := []struct {
		desc                      string
		resp                      PreTransactionResult
		mockRiskCheckResponse     *gdProxyAPI.InternalRiskCheckResponse
		enablePreTransactionCheck bool
		preTransactionCheckConfig *config.PreTransactionCheckConfig
		mockRiskCheckError        error
	}{
		{
			desc:                      "expect pass - should pass without gd call",
			resp:                      PreTransactionResult{Allowed: true},
			enablePreTransactionCheck: false,
			preTransactionCheckConfig: &config.PreTransactionCheckConfig{
				AllowedProxyNumbers:         []string{"123"},
				AllowedProxyNumberThreshold: 100,
			},
			mockRiskCheckResponse: &gdProxyAPI.InternalRiskCheckResponse{
				Recommendations: []string{},
			},
		},
		{
			desc:                      "expect pass - with allowed recommendation",
			resp:                      PreTransactionResult{Allowed: true},
			enablePreTransactionCheck: true,
			preTransactionCheckConfig: &config.PreTransactionCheckConfig{
				AllowedProxyNumbers:         []string{"123"},
				AllowedProxyNumberThreshold: 100,
			},
			mockRiskCheckResponse: &gdProxyAPI.InternalRiskCheckResponse{
				Recommendations: []string{gdProxyAPI.RecommendationAllow},
			},
		},
		{
			desc:                      "expect pass - skipped risk checking because not in whitelist",
			resp:                      PreTransactionResult{Allowed: true},
			enablePreTransactionCheck: true,
			preTransactionCheckConfig: &config.PreTransactionCheckConfig{
				AllowedProxyNumbers:         []string{"123"},
				AllowedProxyNumberThreshold: 0,
			},
			mockRiskCheckResponse: &gdProxyAPI.InternalRiskCheckResponse{
				Recommendations: []string{gdProxyAPI.RecommendationAllow},
			},
		},
		{
			desc:                      "expect fail - with empty recommendation",
			resp:                      PreTransactionResult{Allowed: false, RejectReason: &logic.ErrSystemMalfunction},
			enablePreTransactionCheck: true,
			preTransactionCheckConfig: &config.PreTransactionCheckConfig{
				AllowedProxyNumbers:         []string{"123"},
				AllowedProxyNumberThreshold: 100,
			},
			mockRiskCheckResponse: &gdProxyAPI.InternalRiskCheckResponse{
				Recommendations: []string{},
			},
		},
		{
			desc:                      "expect fail - with gd response NotAllow",
			resp:                      PreTransactionResult{Allowed: false, RejectReason: &logic.ErrRejectedTransaction},
			enablePreTransactionCheck: true,
			preTransactionCheckConfig: &config.PreTransactionCheckConfig{
				AllowedProxyNumbers:         []string{"123"},
				AllowedProxyNumberThreshold: 100,
			},
			mockRiskCheckResponse: &gdProxyAPI.InternalRiskCheckResponse{
				Recommendations: []string{gdProxyAPI.RecommendationNotAllow},
			},
		},
		{
			desc:                      "expect fail - with gd response error",
			resp:                      PreTransactionResult{Allowed: false, Error: errors.New("simulate error")},
			enablePreTransactionCheck: true,
			preTransactionCheckConfig: &config.PreTransactionCheckConfig{
				AllowedProxyNumbers:         []string{"123"},
				AllowedProxyNumberThreshold: 100,
			},
			mockRiskCheckError: errors.New("simulate error"),
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			ctx := context.Background()

			mockCardMapping :=
				[]*storage.UserCardMapping{
					{
						ID: 1,
					},
				}

			// Mock GD Proxy Client
			mockGdProxyClient := &gdProxyMock.GdProxy{}
			randomReq := TestTransactionRequest{
				ProxyNumber: "123456789",
			}
			rawJSON, _ := json.Marshal(randomReq)
			var eventFeatures map[string]interface{}
			_ = json.Unmarshal(rawJSON, &eventFeatures)
			gdProxyRequest := &gdProxyAPI.InternalRiskCheckRequest{
				RiskCheckEvent: "CardTransaction",
				EventFeatures:  eventFeatures,
			}
			mockGdProxyClient.On("InternalRiskCheck", mock.Anything, gdProxyRequest).Return(scenario.mockRiskCheckResponse, scenario.mockRiskCheckError)

			// Mock pre transaction configs
			threshold := 0
			allowedProxyNumbers := []string{"123456789"}
			if scenario.preTransactionCheckConfig != nil {
				threshold = scenario.preTransactionCheckConfig.AllowedProxyNumberThreshold
				allowedProxyNumbers = scenario.preTransactionCheckConfig.AllowedProxyNumbers
			}

			// Mock Storage
			mockStorageDAO := &storage.MockIUserCardMappingDAO{}
			mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockCardMapping, nil).Once()
			storage.UserCardMappingD = mockStorageDAO

			mockUpdateDAO := &storage.MockICardTransactionDAO{}
			mockUpdateDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
			storage.CardTransactionD = mockUpdateDAO

			testPreTransactionCheckImpl := &preTransactionCheckImpl{
				StatsD: statsd.NewNoop(),
				PreTransactionCheckConfig: &config.PreTransactionCheckConfig{
					AllowedProxyNumberThreshold: threshold,
					AllowedProxyNumbers:         allowedProxyNumbers,
				},
				GDProxyClient:             mockGdProxyClient,
				EnablePreTransactionCheck: scenario.enablePreTransactionCheck,
				LogTag:                    "test-tag",
			}

			resp := testPreTransactionCheckImpl.PreTransactionCheck(ctx, "123456789", rawJSON)

			if !scenario.enablePreTransactionCheck {
				mockGdProxyClient.AssertNotCalled(t, "InternalRiskCheck", mock.Anything, gdProxyRequest)
			}

			if scenario.resp.Error != nil {
				assert.Equal(t, scenario.resp.Error, scenario.mockRiskCheckError, "nope ")
			} else {
				assert.NoError(t, scenario.resp.Error, scenario)
				assert.Equal(t, scenario.resp.Allowed, resp.Allowed)
				assert.Equal(t, scenario.resp.RejectReason, resp.RejectReason)

			}
		})
	}

}
