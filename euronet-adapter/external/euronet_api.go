package external

import (
	"context"
	"encoding/json"
	"net/http"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/dto"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

var (
	issuanceDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "Card Issuance",
		Method:  http.MethodPost,
		Path:    "/RenCms/V2.1.0.0/card/issue",
	}
	getCVV2Descriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "Get CVV2",
		Method:  http.MethodGet,
		Path:    "/RenCms/V2.1.0.0/card/getcvv",
	}
	cardStatusChangeDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "Card Status Change",
		Method:  http.MethodPost,
		Path:    "/RenCms/V2.1.0.0/card/cardStatusChange",
	}
	activatePhysicalCardDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "Physical Card Activation",
		Method:  http.MethodPost,
		Path:    "/RenCms/V2.1.0.0/card/cardActivation",
	}
	cardSetPinDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "Card Set Pin",
		Method:  http.MethodPost,
		Path:    "/RenCms/V2.1.0.0/pin/assign",
	}
)

// Issuance defines the logic for calling euronet card issuance API.
func (ec *EuronetCMSClient) Issuance(ctx context.Context, req *dto.IssuanceRequest) (*dto.IssuanceResponse, error) {
	referenceID := req.DebitCardIssuanceRequest.AppHeader.RequestID
	requestSignature, signErr := ec.signENRequest(req.DebitCardIssuanceRequest)
	if signErr != nil {
		slog.FromContext(ctx).Error(logTag, "failed to sign card issuance API request", slog.RequestID(referenceID), slog.Error(err))
		return nil, err
	}
	req.Signature = requestSignature
	slog.FromContext(ctx).Info(logTag, "calling card issuance API", slog.RequestID(referenceID))
	kResp := &dto.IssuanceResponse{}
	ctx = klient.MakeContext(ctx, issuanceDescriptor)

	span, ctx := ec.tracer.StartSpanFromContext(ctx, constant.SpanName, getSpanOptions(issuanceDescriptor)...)
	defer span.Finish()

	err2 := ec.machinery.RoundTrip(ctx, &klientRequest{
		ctx:         ctx,
		descriptor:  issuanceDescriptor,
		requestBody: req,
	}, &klientResponse{responseBody: kResp})
	if err2 != nil {
		slog.FromContext(ctx).Warn(logTag, "failed calling card issuance API", slog.RequestID(referenceID), slog.Error(err2))
		return nil, err2
	}
	slog.FromContext(ctx).Info(logTag, "received response for card issuance API", slog.RequestID(referenceID))
	return kResp, nil
}

// GetCVV2 defines the logic for calling euronet getCVV2 API.
func (ec *EuronetCMSClient) GetCVV2(ctx context.Context, req *dto.GetCVV2Request) (*dto.GetCVV2Response, error) {
	kResp := &dto.GetCVV2Response{}
	referenceID := req.GetCVVRequest.AppHeader.RequestID
	requestSignature, signErr := ec.signENRequest(req.GetCVVRequest)
	if signErr != nil {
		slog.FromContext(ctx).Error(logTag, "failed to sign get CVV2 API request", slog.RequestID(referenceID), slog.Error(err))
		return nil, err
	}
	req.Signature = requestSignature
	slog.FromContext(ctx).Info(logTag, "calling getCVV2 API", slog.RequestID(referenceID))
	ctx = klient.MakeContext(ctx, getCVV2Descriptor)
	span, ctx := ec.tracer.StartSpanFromContext(ctx, constant.SpanName, getSpanOptions(getCVV2Descriptor)...)
	defer span.Finish()
	err2 := ec.machinery.RoundTrip(ctx, &klientRequest{
		ctx:         ctx,
		descriptor:  getCVV2Descriptor,
		requestBody: req,
	}, &klientResponse{responseBody: kResp})
	if err2 != nil {
		slog.FromContext(ctx).Warn(logTag, "failed calling getCVV2 API", slog.RequestID(referenceID), slog.Error(err2))
		return nil, err2
	}

	slog.FromContext(ctx).Info(logTag, "received response for getCVV2 API", slog.RequestID(referenceID))
	return kResp, nil
}

// CardStatusChange defines the logic for calling euronet card status change API.
func (ec *EuronetCMSClient) CardStatusChange(ctx context.Context, req *dto.CardStatusChangeRequest) (*dto.CardStatusChangeResponse, error) {
	referenceID := req.CardStatusChangeRequest.AppHeader.RequestID
	requestSignature, signErr := ec.signENRequest(req.CardStatusChangeRequest)
	if signErr != nil {
		slog.FromContext(ctx).Error(logTag, "failed to sign change card status API request", slog.RequestID(referenceID), slog.Error(err))
		return nil, err
	}
	req.Signature = requestSignature
	slog.FromContext(ctx).Info(logTag, "calling card status change API", slog.RequestID(referenceID))

	ctx = klient.MakeContext(ctx, cardStatusChangeDescriptor)
	span, ctx := ec.tracer.StartSpanFromContext(ctx, constant.SpanName, getSpanOptions(cardStatusChangeDescriptor)...)
	defer span.Finish()
	kResp := &dto.CardStatusChangeResponse{}
	err := ec.machinery.RoundTrip(ctx, &klientRequest{
		ctx:         ctx,
		descriptor:  cardStatusChangeDescriptor,
		requestBody: req,
	}, &klientResponse{responseBody: kResp})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed calling card status change API", slog.RequestID(referenceID), slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, "received response for card status change API", slog.RequestID(referenceID))
	return kResp, nil
}

// ActivatePhysicalCard defines the logic for calling euronet card activation API.
func (ec *EuronetCMSClient) ActivatePhysicalCard(ctx context.Context, req *dto.ActivatePhysicalCardRequest) (*dto.ActivatePhysicalCardResponse, error) {
	referenceID := req.CardActivationRequest.AppHeader.RequestID
	requestSignature, signErr := ec.signENRequest(req.CardActivationRequest)
	if signErr != nil {
		slog.FromContext(ctx).Error(logTag, "failed to sign change card activate API request", slog.RequestID(referenceID), slog.Error(err))
		return nil, err
	}
	req.Signature = requestSignature
	slog.FromContext(ctx).Info(logTag, "calling activate physical card API", slog.RequestID(referenceID))
	kResp := &dto.ActivatePhysicalCardResponse{}
	kreq := &klientRequest{
		ctx:         ctx,
		descriptor:  activatePhysicalCardDescriptor,
		requestBody: req,
	}

	ctx = klient.MakeContext(ctx, activatePhysicalCardDescriptor)
	span, ctx := ec.tracer.StartSpanFromContext(ctx, constant.SpanName, getSpanOptions(activatePhysicalCardDescriptor)...)
	defer span.Finish()
	err := ec.machinery.RoundTrip(ctx, kreq, &klientResponse{responseBody: kResp})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed calling activate physical card API", slog.RequestID(referenceID), slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, "received response for activate physical card API", slog.RequestID(referenceID))
	return kResp, nil
}

// CardSetPin defines the logic for calling euronet card set pin API.
func (ec *EuronetCMSClient) CardSetPin(ctx context.Context, req *dto.CardPinMgmtAPIRequest) (*dto.CardPinMgmtAPIResponse, error) {
	referenceID := req.CardPinMgmtRequest.AppHeader.RequestID
	requestSignature, signErr := ec.signENRequest(req.CardPinMgmtRequest)
	if signErr != nil {
		slog.FromContext(ctx).Error(logTag, "failed to sign card set pin API request", slog.RequestID(referenceID), slog.Error(err))
		return nil, err
	}
	req.Signature = requestSignature
	slog.FromContext(ctx).Info(logTag, "calling card set pin API", slog.RequestID(referenceID))
	kResp := &dto.CardPinMgmtAPIResponse{}
	kReq := &klientRequest{
		ctx:         ctx,
		descriptor:  cardSetPinDescriptor,
		requestBody: req,
	}
	ctx = klient.MakeContext(ctx, cardSetPinDescriptor)
	span, ctx := ec.tracer.StartSpanFromContext(ctx, constant.SpanName, getSpanOptions(cardSetPinDescriptor)...)
	defer span.Finish()
	err := ec.machinery.RoundTrip(ctx, kReq, &klientResponse{responseBody: kResp})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed calling card set pin API", slog.RequestID(referenceID), slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, "received response for calling card set pin API", slog.RequestID(referenceID))
	return kResp, nil
}

// SignENRequest Sign request body
func (ec *EuronetCMSClient) signENRequest(body interface{}) (string, error) {
	bodyStr, err := json.Marshal(body)
	if err != nil {
		return "", err
	}

	signedBody, err := ec.signingMethod.Sign(string(bodyStr), ec.signatureRSAPrivateKey)
	if err != nil {
		return "", err
	}

	return signedBody, nil
}

func getSpanOptions(descriptor *klient.EndpointDescriptor) []tracer.StartSpanOption {
	return []tracer.StartSpanOption{
		tracer.ServiceName(descriptor.Service),
		tracer.SpanType(constant.TagSpanTypeHTTP),
		tracer.Tag(constant.TagHTTPURL, descriptor.Path),
		tracer.Tag(constant.TagHTTPMethod, descriptor.Method),
	}
}
