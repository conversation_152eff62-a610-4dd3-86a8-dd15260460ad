package dto

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
)

// CreateRefundRequest ..
type CreateRefundRequest struct {
	IdempotencyKey           string
	AuthTxnID                string
	ProxyNumber              string
	AuthAmount               int64
	AuthCurrency             string
	OriginalAmount           int64
	OriginalCurrency         string
	RefundAmount             int64
	RRN                      string
	CardSchemeTxnID          string
	CardAcceptorNameLocation string
	MCC                      string
	AcquirerReferenceData    string
	AcquirerID               string
	ApprovalCode             string
	OpParams                 OfflineProcessingParams
	MetaData                 api.MetaData
	RequestBody              json.RawMessage
	ResponseBody             json.RawMessage
	ProcessingCode           string
	EuronetTransactionID     string
	IsReversed               bool
	LocalTransactionDatetime time.Time
	RefundSource             string
}

// CreateRefundResponse ..
type CreateRefundResponse struct {
	RefundTransactionID string
	Status              string
	StatusReason        string
}
