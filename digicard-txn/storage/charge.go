package storage

import (
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
)

// Charge ...
type Charge struct {
	ID                            uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
	Type                          string          `sql-col:"type"`
	CardID                        string          `sql-col:"card_id"`
	CustomerID                    string          `sql-col:"customer_id"`
	ChargeID                      string          `sql-col:"charge_id"`
	ReferenceID                   string          `sql-col:"reference_id"`
	Status                        string          `sql-col:"status"`
	SourceAccountID               string          `sql-col:"source_account_id"`
	DestinationAccountID          string          `sql-col:"destination_account_id"`
	CaptureMethod                 string          `sql-col:"capture_method"`
	RequestAmount                 int64           `sql-col:"request_amount"`
	MarkUpAmount                  int64           `sql-col:"markup_amount"`
	RequestCurrency               string          `sql-col:"request_currency"`
	CaptureAmount                 int64           `sql-col:"capture_amount"`
	OriginalAmount                int64           `sql-col:"original_amount"`
	OriginalCurrency              string          `sql-col:"original_currency"`
	MerchantDescription           string          `sql-col:"merchant_description"`
	MerchantID                    string          `sql-col:"merchant_id"`
	MCC                           string          `sql-col:"mcc"`
	CaptureMode                   string          `sql-col:"capture_mode"`
	Properties                    json.RawMessage `sql-col:"properties" sql-where:"false" data-type:"json"`
	Metadata                      json.RawMessage `sql-col:"metadata" sql-where:"false" data-type:"json"`
	StatusReason                  string          `sql-col:"status_reason"`
	StatusReasonDescription       string          `sql-col:"status_reason_description"`
	VendorChargeID                string          `sql-col:"vendor_charge_id"`
	VendorChargeTimestamp         time.Time       `sql-col:"vendor_charge_timestamp"`
	PaymentTransactionID          string          `sql-col:"payment_transaction_id"`
	TransactionDomain             string          `sql-col:"transaction_domain"`
	TransactionType               string          `sql-col:"transaction_type"`
	TransactionSubtype            string          `sql-col:"transaction_subtype"`
	DisputeList                   json.RawMessage `sql-col:"dispute_list" sql-where:"false" data-type:"json"`
	ReversalAmount                int64           `sql-col:"reversal_amount"`
	ReversalCurrency              string          `sql-col:"reversal_currency"`
	ReversalTimestamp             time.Time       `sql-col:"reversal_timestamp"`
	CaptureAmountTillDate         int64           `sql-col:"captured_amount_till_date"`
	CaptureOriginalAmount         int64           `sql-col:"capture_original_amount"`
	CaptureOriginalAmountTillDate int64           `sql-col:"capture_original_amount_till_date"`
	CreationTimestamp             time.Time       `sql-col:"created_at"`
	ValueTimestamp                time.Time       `sql-col:"valued_timestamp"`
	UpdatedTimestamp              time.Time       `sql-col:"updated_at"`
}

// GetMetadata ...
func (d *Charge) GetMetadata() (api.MetaData, error) {
	if d.Metadata == nil {
		return api.MetaData{}, nil
	}
	metaData := api.MetaData{}
	err := json.Unmarshal(d.Metadata, &metaData)
	if err != nil {
		return metaData, err
	}
	return metaData, nil
}

//const (
//	logTagCharge = "storage.charge"
//	ttlCharge    = 24 * 3600 // 1 day
//)
