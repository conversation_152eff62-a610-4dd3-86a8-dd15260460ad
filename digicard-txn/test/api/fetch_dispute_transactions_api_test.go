package api

import (
	"encoding/json"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("Fetch Dispute Transactions", func() {
	var (
		client   *hcl.Client
		setupErr error
	)

	const (
		customerID       = "CUST1234"
		txnID            = "TXN1234"
		disputeID        = "DISPUTE1234"
		refundID         = "REFUND1234"
		maskedCardNumber = "525578XXXXXX2300"
		noDisputeFound   = "no dispute found"
	)

	BeforeEach(func() {
		client, setupErr = hcl.NewClient(server.URL())
		Expect(setupErr).ShouldNot(HaveOccurred())
	})

	Describe("Calling fetch dispute transactions API with appropriate parameters", func() {
		apiURL := "/v1/dispute/transactions"

		Context("Fetch dispute transactions API with all valid parameters", func() {
			When("Fetch dispute transactions API successfully", func() {
				It("Return 200", func() {
					requestBody := &api.FetchDisputeTransactionsRequest{
						CustomerID: customerID,
					}
					idempotencyModifier := []hcl.RequestModifier{
						hcl.JSON(requestBody),
					}
					disputeTransactions := api.DisputeTransactionDetail{
						TransactionID:    txnID,
						MaskedCardNumber: maskedCardNumber,
						Disputes: []api.DisputeDetail{
							{
								DisputeID: disputeID,
							},
						},
						Refunds: []api.RefundDetails{
							{
								RefundID: refundID,
							},
						},
					}
					mockFetchDisputeTransactionsClient.On("FetchDisputeTransactions", mock.Anything, mock.Anything).Return(&api.FetchDisputeTransactionsResponse{
						DisputeTransactions: []api.DisputeTransactionDetail{
							disputeTransactions,
						},
					}, nil).Once()
					resp, _ := client.Post(apiURL, idempotencyModifier...)

					respObj := &api.FetchDisputeTransactionsResponse{}
					err := json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.DisputeTransactions[0].TransactionID).Should(Equal(txnID))
					Expect(respObj.DisputeTransactions[0].MaskedCardNumber).Should(Equal(maskedCardNumber))
					Expect(respObj.DisputeTransactions[0].Disputes[0].DisputeID).Should(Equal(disputeID))
					Expect(len(respObj.DisputeTransactions)).Should(Equal(1))

				})
			})
			When("When no dispute found", func() {
				It("Return 400", func() {
					requestBody := &api.FetchDisputeTransactionsRequest{
						CustomerID: customerID,
					}
					idempotencyModifier := []hcl.RequestModifier{
						hcl.JSON(requestBody),
					}
					expectedErrResponse := logic.BuildErrorResponse(api.DisputeNotFound, noDisputeFound)
					mockFetchDisputeTransactionsClient.On("FetchDisputeTransactions", mock.Anything, mock.Anything).Return(nil, expectedErrResponse).Once()
					resp, _ := client.Post(apiURL, idempotencyModifier...)

					respObj := &servus.ServiceError{}
					err := json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					Expect(respObj.Message).Should(Equal("no dispute found"))
					Expect(respObj.Code).Should(Equal("DISPUTE_NOT_FOUND"))
				})
			})
			When("When Generic Failure Happens", func() {
				It("Return 500", func() {
					requestBody := &api.FetchDisputeTransactionsRequest{
						CustomerID: customerID,
					}
					idempotencyModifier := []hcl.RequestModifier{
						hcl.JSON(requestBody),
					}
					expectedErrResponse := api.DefaultInternalServerError
					mockFetchDisputeTransactionsClient.On("FetchDisputeTransactions", mock.Anything, mock.Anything).Return(nil, expectedErrResponse).Once()
					resp, _ := client.Post(apiURL, idempotencyModifier...)

					respObj := &servus.ServiceError{}
					err := json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))
				})
			})
		})

		Context("Fetch transaction details with  invalid parameters", func() {
			When("CustomerID is mandatory", func() {
				It("Return 400", func() {
					requestBody := &api.FetchTransactionDetailsRequest{}
					idempotencyModifier := []hcl.RequestModifier{
						hcl.JSON(requestBody),
					}
					resp, err := client.Post(apiURL, idempotencyModifier...)

					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))

					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Message).Should(Equal("request has invalid parameter(s) or header(s)."))
					Expect(respObj.Code).Should(Equal("BAD_REQUEST"))
					Expect(respObj.Errors[0].ErrorCode).Should(Equal("FIELD_INVALID"))
					Expect(respObj.Errors[0].Message).Should(Equal("string cannot be empty"))
					Expect(respObj.Errors[0].Path).Should(Equal("CustomerID"))
				})
			})
		})
	})

})
