package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/mock"
	updatedispute "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/dispute/update"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
)

func TestValidateUpdateRequest(t *testing.T) {
	t.Run("Valid flow", func(t *testing.T) {
		err := validateUpdateRequest(&api.UpdateDisputeRequest{
			DisputeID:                   "1",
			ChargeBackStatusDescription: "Bank Review",
			ChargeBackStatusCode:        "02",
			DisputeReasonCode:           "Sample Reason Code",
		})
		assert.Nil(t, err)
	})

	t.Run("When empty request", func(t *testing.T) {
		err := validateUpdateRequest(nil)
		errorCode := api.BadRequest
		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "request has invalid parameter(s).",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("When Request does not have any data", func(t *testing.T) {
		err := validateUpdateRequest(&api.UpdateDisputeRequest{})
		errorCode := api.BadRequest

		var errs []servus.ErrorDetail

		emptyFieldValidator("", "disputeID", &errs)
		emptyFieldValidator("", "chargeBackStatusCode", &errs)
		emptyFieldValidator("", "chargeBackStatusDescription", &errs)
		emptyFieldValidator("", "disputeReasonCode", &errs)

		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "request has invalid parameter(s) or header(s).",
			HTTPCode: errorCode.HTTPStatusCode(),
			Errors:   errs,
		}
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestUpdateDispute(t *testing.T) {
	t.Run("Valid flow", func(t *testing.T) {
		req := &api.UpdateDisputeRequest{
			DisputeID:                   "1",
			ChargeBackStatusDescription: "Bank Review",
			ChargeBackStatusCode:        "02",
			DisputeReasonCode:           "Sample Reason Code",
		}
		updateDisputeClient := &updatedispute.MockClient{}
		service := DigicardTransactionService{UpdateDisputeClient: updateDisputeClient}
		updateDisputeClient.On("UpdateDispute", mock.Anything, mock.Anything).Return(&api.UpdateDisputeResponse{}, nil)
		resp, err := service.UpdateDispute(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, resp)
	})

	t.Run("Request validation fail", func(t *testing.T) {
		req := &api.UpdateDisputeRequest{}
		updateDisputeClient := &updatedispute.MockClient{}
		service := DigicardTransactionService{UpdateDisputeClient: updateDisputeClient}
		updateDisputeClient.On("UpdateDispute", mock.Anything, mock.Anything).Return(&api.UpdateDisputeResponse{}, nil)
		resp, err := service.UpdateDispute(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})

	t.Run("update dispute fail to process", func(t *testing.T) {
		req := &api.UpdateDisputeRequest{}
		updateDisputeClient := &updatedispute.MockClient{}
		service := DigicardTransactionService{UpdateDisputeClient: updateDisputeClient}
		updateDisputeClient.On("UpdateDispute", mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		resp, err := service.UpdateDispute(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
}
