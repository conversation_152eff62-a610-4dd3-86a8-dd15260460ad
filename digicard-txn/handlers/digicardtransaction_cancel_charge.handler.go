package handlers

import (
	context "context"
	"fmt"
	"net/http"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction/charge"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	cancelWorkflowResponseToChargeResponse = charge.WorkflowResponseToChargeResponse
)

// CancelCharge ...
func (d *DigicardTransactionService) CancelCharge(ctx context.Context, req *api.CancelChargeRequest) (*api.ChargeResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, req.IdempotencyKey),
		slog.CustomTag(logic.VendorChargeIDTag, req.Id))

	if errors := cancelChargeRequestValidator(req); len(errors) != 0 {
		slog.FromContext(ctx).Warn(logCharge, "Error in validating cancel charge request body")
		return nil, logic.CustomError(http.StatusBadRequest, errors)
	}

	chargeData, err := transaction.LoadTransactionByChargeID(ctx, req.Id)
	if err != nil {
		if err == data.ErrNoData {
			message := "The requested charge transaction resource is not found."
			return nil, logic.BuildErrorResponse(api.ResourceNotFound, message)
		}
		return nil, logic.ErrGenericServer
	}

	if chargeData.CaptureMethod != string(dto.Manual) {
		return nil, api.ErrorWrongCaptureMode
	}

	// check idempotency
	properties := dto.GetProperties(chargeData)
	if properties.CancelIdempotencyKey == req.IdempotencyKey {
		slog.FromContext(ctx).Info(logCharge, fmt.Sprintf("existing charge data CancelIdempotencyKey matches request, return existing data. CancelIdempotencyKey='%s'", req.IdempotencyKey))
		response := dto.ChargeStorageToAPIResponse(chargeData)
		return response, nil
	}

	workflowImpl := d.ChargeWorkflow
	manualData := &dto.ChargeManualData{
		VendorChargeID:           req.Id,
		IdempotencyKey:           req.IdempotencyKey,
		ChargeStorage:            chargeData,
		IsCapture:                false,
		Amount:                   req.AmountParams,
		IsSaleCompletionReversal: req.IsSaleCompletionReversal,
		PostingSource:            req.PostingSource,
	}
	canceledWorkflow, err := workflowImpl.CaptureOrCancelWorkflow(ctx, manualData)
	if err != nil {
		return nil, err
	}

	resp, err := cancelWorkflowResponseToChargeResponse(ctx, canceledWorkflow)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func cancelChargeRequestValidator(req *api.CancelChargeRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	cancelChargeRequestBodyValidator(req, &errs)
	return errs
}

func cancelChargeRequestBodyValidator(req *api.CancelChargeRequest, errs *[]servus.ErrorDetail) {
	if req.IdempotencyKey == "" {
		*errs = append(*errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'idempotencyKey' is a mandatory field.",
			Path:      "idempotencyKey",
		})
	}
}
