package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	logDisputeTag = "transaction_dispute_check"
)

// TransactionDisputeCheck checks if the transaction is disputable, given the transactionID and customerID
func (d *DigicardTransactionService) TransactionDisputeCheck(ctx context.Context, req *api.TransactionDisputeCheckRequest) (*api.TransactionDisputeCheckResponse, error) {
	if err := validateTransactionDisputeCheckRequest(req); err != nil {
		slog.FromContext(ctx).Warn(logDisputeTag, "request validation failed", slog.Error(err))
		return nil, err
	}
	result, err := d.ValidateDisputeClient.IsTxnDisputable(ctx, req.CustomerID, req.TransactionID)
	if err != nil {
		slog.FromContext(ctx).Warn(logDisputeTag, "ValidateDisputeClient.IsTxnDisputable failed", slog.Error(err))
		return nil, err
	}
	return &api.TransactionDisputeCheckResponse{IsTransactionDisputable: result}, nil
}

func validateTransactionDisputeCheckRequest(req *api.TransactionDisputeCheckRequest) error {
	if req == nil {
		return logic.BuildErrorResponse(api.BadRequest, "Invalid Request")
	}

	if req.CustomerID == "" || req.TransactionID == "" {
		return logic.BuildErrorResponse(api.BadRequest, "CustomerID and Transaction ID is mandatory")
	}
	return nil
}
