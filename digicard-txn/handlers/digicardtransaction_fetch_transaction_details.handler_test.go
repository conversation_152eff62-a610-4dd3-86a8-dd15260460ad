package handlers

import (
	"context"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction/transactioninfo"
)

func TestFetchTransactionDetails(t *testing.T) {
	mockTransactionDetailsClient := &transactioninfo.MockTransactionDetailClient{}
	d := DigicardTransactionService{
		TransactionClient: mockTransactionDetailsClient,
	}
	t.Run("Valid flow", func(t *testing.T) {
		mockTransactionDetailsClient.On("FetchTransactionDetails", mock.Anything, mock.Anything).Return(&api.FetchTransactionDetailsResponse{
			TransactionDetails: []api.TransactionDetail{
				{
					TransactionID: uuid.NewString(),
				},
			},
		}, nil).Once()
		response, err := d.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{CustomerID: "1234"})
		assert.NotNil(t, response)
		assert.Equal(t, 1, len(response.TransactionDetails))
		assert.Nil(t, err)
	})
	t.Run("When no charge found", func(t *testing.T) {
		expectedErrResp := logic.BuildErrorResponse(api.InvalidTransactionRequest, "here is no transaction found for given request")
		mockTransactionDetailsClient.On("FetchTransactionDetails", mock.Anything, mock.Anything).Return(nil, expectedErrResp).Once()
		response, err := d.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{CustomerID: "1234"})
		assert.NotNil(t, err)
		assert.Equal(t, expectedErrResp, err)
		assert.Nil(t, response)
	})
	t.Run("When api.DefaultInternalServerError occurs", func(t *testing.T) {
		expectedErrResp := api.DefaultInternalServerError
		mockTransactionDetailsClient.On("FetchTransactionDetails", mock.Anything, mock.Anything).Return(nil, expectedErrResp).Once()
		response, err := d.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{CustomerID: "1234"})
		assert.NotNil(t, err)
		assert.Equal(t, expectedErrResp, err)
		assert.Nil(t, response)
	})
}
