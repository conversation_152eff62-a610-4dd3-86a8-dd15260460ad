package cancellation

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	accountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"
)

func TestPublishCancellation(t *testing.T) {
	errDummy := errors.New("simulate error")
	testcardID := uuid.NewString()
	testChargeID := uuid.NewString()
	defaultChargeStorage := &storage.Charge{
		CardID: testcardID,
	}
	defaultRefundStorage := storage.Refund{
		CardID: testcardID,
	}
	metadata := &dto.RefundMetaData{
		TransactionCategory: string(constant.TransactionCategoryECOM),
	}
	refundMetaDataJSON, _ := dto.ConvertRefundMetaDataToJSON(metadata)
	defaultRefundStorage.Metadata = &refundMetaDataJSON
	testcases := []struct {
		currState     workflowengine.State
		ChargeID      string
		chargeStorage *storage.Charge
		RefundStorage storage.Refund
		expectedState workflowengine.State
		publishErr    error
		expectedErr   error
	}{
		{
			chargeStorage: defaultChargeStorage,
			RefundStorage: defaultRefundStorage,
			publishErr:    errDummy,
			expectedErr:   errDummy,
			ChargeID:      testChargeID,
		},
		{
			currState:     stCancellationPersisted,
			expectedState: stCancellationProcessingPublished,
			chargeStorage: defaultChargeStorage,
			RefundStorage: defaultRefundStorage,
			ChargeID:      testChargeID,
		},
		{
			currState:     stCancelCompleted,
			expectedState: stCompletedPublished,
			chargeStorage: defaultChargeStorage,
			RefundStorage: defaultRefundStorage,
			ChargeID:      testChargeID,
		},
		{
			currState:     stFailed,
			expectedState: stFailurePublished,
			chargeStorage: defaultChargeStorage,
			RefundStorage: defaultRefundStorage,
			ChargeID:      testChargeID,
		},
	}

	for index, scenario := range testcases {
		testcase := scenario
		t.Run(fmt.Sprintf("test case #%d", index), func(t *testing.T) {
			mockClient := kafkawriter.MockClient{}
			mockAccountServiceClient := &accountServiceMock.AccountService{}
			publisher := &transaction.PublisherImpl{
				KafkaWriter:        &mockClient,
				AccountServiceDBMY: mockAccountServiceClient,
			}
			mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
				Id:     "123",
				Status: accountServiceAPI.AccountStatus_ACTIVE,
			}}, nil).Once()
			mockClient.On("Save", mock.Anything).Return(testcase.publishErr).Once()
			w := WorkflowImpl{
				Publisher: publisher,
				StatsD:    statsd.NewNoop(),
			}
			resp, err := w.publishCancellation(context.Background(), "", &ExecutionData{
				State:  testcase.currState,
				Refund: testcase.RefundStorage,
				Charge: testcase.chargeStorage,
				CreateRefund: &dto.CreateRefund{
					IdempotencyKey: uuid.NewString(),
					ChargeID:       testcase.ChargeID,
				},
			}, "")
			if testcase.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, testcase.expectedErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, testcase.expectedState, resp.GetState())
			}
		})
	}
}
