package cancellation

import (
	"context"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
)

// Workflow ...
type Workflow interface {
	CreateCancellationWorkflow(context.Context, *dto.CreateRefund, *storage.Charge) (*dto.WorkflowResponse, error)
	GetWorkflow(ctx context.Context, runID string) (*dto.WorkflowResponse, error)
}

//go:generate mockery --name Workflow --inpackage --case=underscore
