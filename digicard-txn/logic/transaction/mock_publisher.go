// Code generated by mockery v2.42.1. DO NOT EDIT.

package transaction

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	dto "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
)

// MockPublisher is an autogenerated mock type for the Publisher type
type MockPublisher struct {
	mock.Mock
}

// Publish provides a mock function with given fields: _a0, _a1, _a2, _a3, _a4
func (_m *MockPublisher) Publish(_a0 context.Context, _a1 *dto.Transaction, _a2 string, _a3 string, _a4 string) error {
	ret := _m.Called(_a0, _a1, _a2, _a3, _a4)

	if len(ret) == 0 {
		panic("no return value specified for Publish")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *dto.Transaction, string, string, string) error); ok {
		r0 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PublishAutoReversal provides a mock function with given fields: _a0, _a1, _a2, _a3, _a4
func (_m *MockPublisher) PublishAutoReversal(_a0 context.Context, _a1 *dto.Transaction, _a2 string, _a3 string, _a4 string) error {
	ret := _m.Called(_a0, _a1, _a2, _a3, _a4)

	if len(ret) == 0 {
		panic("no return value specified for PublishAutoReversal")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *dto.Transaction, string, string, string) error); ok {
		r0 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PublishCompletedAutoReversal provides a mock function with given fields: _a0, _a1, _a2, _a3, _a4
func (_m *MockPublisher) PublishCompletedAutoReversal(_a0 context.Context, _a1 *dto.Transaction, _a2 string, _a3 string, _a4 string) error {
	ret := _m.Called(_a0, _a1, _a2, _a3, _a4)

	if len(ret) == 0 {
		panic("no return value specified for PublishCompletedAutoReversal")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *dto.Transaction, string, string, string) error); ok {
		r0 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMockPublisher creates a new instance of MockPublisher. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPublisher(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPublisher {
	mock := &MockPublisher{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
