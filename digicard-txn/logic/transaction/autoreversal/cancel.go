package autoreversal

import (
	"context"
	"fmt"
	"time"

	paymentCoreAPI "gitlab.myteksi.net/dakota/payment/payment-core/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
)

func (w *WorkflowImpl) cancel(ctx context.Context, _ string, execData workflowengine.ExecutionData, _ interface{}) (workflowengine.ExecutionData, error) {
	slog.FromContext(ctx).Info(logAutoReversal, "auto cancel handler start")
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAutoReversal, "Invalid context passed")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	cancelRequest := &paymentCoreAPI.InternalCancelRequest{
		ReferenceID: createPCRefID(nextCtx.OriginalTransaction.ReferenceID),
		AuthTxID:    nextCtx.OriginalTransaction.PaymentTransactionID,
	}

	var respStatus logic.TransactionStatus
	response, err := w.PaymentCoreClient.InternalCancel(ctx, cancelRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logAutoReversal, "PaymentCoreClient.InternalCancel", slog.Error(err))
		respStatus = logic.Failed
	} else {
		respStatus = logic.TransactionStatus(response.Status)
	}

	switch respStatus {
	case logic.Success:
		slog.FromContext(ctx).Info(logAutoReversal, "Internal Cancel transaction status is successful")
		nextCtx.OriginalTransaction.ReversalAmount = currCtx.OriginalTransaction.RequestAmount
		nextCtx.OriginalTransaction.ReversalCurrency = currCtx.OriginalTransaction.RequestCurrency
		nextCtx.OriginalTransaction.ReversalTimestamp = time.Now()
		nextCtx.OriginalTransaction.Status = string(logic.Canceled)
		nextCtx.OriginalTransaction.Properties = dto.UpdateProperties(nextCtx.OriginalTransaction.Properties, "", "", int(nextCtx.State), 0)
		if updateErr := updateChargeTxn(ctx, currCtx.OriginalTransaction.ChargeID, string(logic.Canceled)); updateErr != nil {
			slog.FromContext(ctx).Error(logAutoReversal, fmt.Sprintf("Failed to update charge transaction: chargeID=%s", currCtx.OriginalTransaction.ChargeID), slog.Error(updateErr))
			return nil, updateErr
		}

		nextCtx.State = stCancelled
	case logic.Processing:
		slog.FromContext(ctx).Info(logAutoReversal, "Internal Cancel transaction status is processing")
		nextCtx.State = stCancelProcessing
	case logic.Failed:
		slog.FromContext(ctx).Info(logAutoReversal, "Internal Cancel transaction status is failed")
	}

	if respStatus == logic.Failed {
		slog.FromContext(ctx).Info(logAutoReversal, fmt.Sprintf("failed to cancel transaction chargeID=%s", nextCtx.OriginalTransaction.ChargeID))
		nextCtx.State = stCancelFailed
		return nextCtx, nil
	}

	slog.FromContext(ctx).Info(logAutoReversal, "auto cancel handler end")
	return nextCtx, nil
}

func (w *WorkflowImpl) persistCancelStream(ctx context.Context, _ string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	slog.FromContext(ctx).Info(logAutoReversal, "persistCancelStream handler start")
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAutoReversal, "Invalid context passed in persistCancelStream state")
		return nil, logic.ErrInvalidContext
	}

	nextCtx := currCtx.Clone()
	if err := persistStream(ctx, nextCtx, logAutoReversal, params, stCancelStreamPersisted); err != nil {
		slog.FromContext(ctx).Error(logAutoReversal, "Failed to persist auto cancel stream", slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Info(logAutoReversal, "persistCancelStream handler end")
	return nextCtx, nil
}

func (w *WorkflowImpl) completeCancel(ctx context.Context, _ string, execData workflowengine.ExecutionData, _ interface{}) (workflowengine.ExecutionData, error) {
	slog.FromContext(ctx).Info(logAutoReversal, "completeCancel handler start")
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAutoReversal, "Invalid context passed in completeCancel state")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	streamMessage := currCtx.StreamMessage

	respStatus := logic.TransactionStatus(streamMessage.Status)
	switch respStatus {
	case logic.Success:
		slog.FromContext(ctx).Info(logAutoReversal, "Internal Cancel transaction status is successful")
		nextCtx.State = stCancelled
		now := time.Now()
		nextCtx.OriginalTransaction.ReversalAmount = currCtx.OriginalTransaction.RequestAmount
		nextCtx.OriginalTransaction.ReversalCurrency = currCtx.OriginalTransaction.RequestCurrency
		nextCtx.OriginalTransaction.ReversalTimestamp = now
		nextCtx.OriginalTransaction.UpdatedTimestamp = now
		nextCtx.OriginalTransaction.Status = string(logic.Canceled)
		nextCtx.OriginalTransaction.Properties = dto.UpdateProperties(nextCtx.OriginalTransaction.Properties, "", "", int(nextCtx.State), 0)
		if updateErr := updateChargeTxn(ctx, currCtx.OriginalTransaction.ChargeID, string(logic.Canceled)); updateErr != nil {
			slog.FromContext(ctx).Error(logAutoReversal, fmt.Sprintf("Failed to update charge transaction: chargeID=%s", currCtx.OriginalTransaction.ChargeID), slog.Error(updateErr))
			return nil, updateErr
		}
	case logic.Failed:
		slog.FromContext(ctx).Info(logAutoReversal, "Internal Cancel transaction status is failed")
		nextCtx.State = stCancelFailed
	}

	slog.FromContext(ctx).Info(logAutoReversal, "auto cancel handler end")
	return nextCtx, nil
}
