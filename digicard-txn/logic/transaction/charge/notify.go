package charge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/bgcontext"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	customerMasterAPI "gitlab.myteksi.net/dakota/customer-master/api/v2"

	"github.com/shopspring/decimal"

	"gitlab.myteksi.net/dakota/common/currency"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/notification"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/server/config"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const (
	// SGD config
	sgdExponent = 2
	timeZone    = "Asia/Singapore"
)

// nolint: unparam
func (w *WorkflowImpl) notify(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	logTag := logHandler + ".notify"
	slog.FromContext(ctx).Info(logTag, "notify handler start")
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Error(logCharge, "Invalid context passed in notify state")
		return nil, logic.ErrInvalidContext
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, currCtx.CreateCharge.IdempotencyKey))
	nextCtx := currCtx.Clone()

	transactionDTO := dto.ChargeStorageToDTO(&nextCtx.Charge)
	var notificationType notification.Type

	switch currCtx.State {
	case stAuthPublished:
		notificationType = notification.Success
		nextCtx.State = stAuthSuccessPushNotified
	case stCompletedPublished:
		notificationType = notification.Success
		nextCtx.State = stCompletedNotified
	case stFailurePublished, stFailureToBeNotified, stDeclinedThenNotify:
		notificationType = notification.Failed
		nextCtx.State = stFailureNotified
	case stLimitCheckFailedPublished:
		notificationType = notification.Failed
		nextCtx.State = stLimitCheckFailedPushNotified
	case stDeclinedBecauseCardLocked:
		notificationType = notification.FailedCardIsFrozen
		nextCtx.State = stFailureNotified
	}
	bgCtx := bgcontext.BackgroundWithValue(ctx)
	additionalParams := additionalParamsForChargeNotification(nextCtx)
	gconcurrent.Go(bgCtx, logTag, transaction.PushNotify(w.Notifier, notificationType, transactionDTO, additionalParams, logTag))
	return nextCtx, nil
}

func (w *WorkflowImpl) sendPush(ctx context.Context, _ string, execData workflowengine.ExecutionData, _ interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logCharge, "Invalid context passed in push notify state")
		return nil, logic.ErrInvalidContext
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, currCtx.CreateCharge.IdempotencyKey))
	nextCtx := currCtx.Clone()

	transactionDTO := dto.ChargeStorageToDTO(&nextCtx.Charge)
	var notificationType notification.Type
	var notificationRequest *notification.Request

	switch currCtx.State {
	case stAuthPublished:
		notificationType = notification.AuthSuccess
		nextCtx.State = stAuthSuccessPushNotified
	case stFailureToBeNotified:
		notificationType = notification.AuthFailed
		nextCtx.State = stFailurePushNotified
	case stLimitCheckFailedPublished:
		notificationType = notification.LimitCheckFailed
		nextCtx.State = stLimitCheckFailedPushNotified
	case stLimitCheckFailedPushNotified:
		notificationType = notification.Failed
		nextCtx.State = stFailureEmailNotified
	default:
		slog.FromContext(ctx).Warn(logCharge, "Invalid state for notification")
		return nil, errors.New("invalid state for notification")
	}

	additionalParams := additionalParamsForChargeNotification(nextCtx)
	notificationRequest = w.buildPushNotificationRequest(ctx, notificationType, transactionDTO, additionalParams)

	if err := w.PushNotifier.NotifyV2(ctx, notificationRequest); err != nil {
		slog.FromContext(ctx).Warn(logCharge, "Error in publishing push notification", slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Info(logCharge, "Successfully published push notification type: "+string(notificationType))
	return nextCtx, nil
}

func (w *WorkflowImpl) sendEmail(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logCharge, "Invalid context passed in notify state")
		return nil, logic.ErrInvalidContext
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, currCtx.CreateCharge.IdempotencyKey))
	nextCtx := currCtx.Clone()

	transactionDTO := dto.ChargeStorageToDTO(&nextCtx.Charge)
	var notificationType notification.Type

	switch currCtx.State {
	case stAuthSuccessPushNotified:
		notificationType = notification.AuthSuccess
		nextCtx.State = stAuthSuccessEmailNotified
	case stFailurePushNotified, stFailureNotified:
		notificationType = notification.Failed
		nextCtx.State = stFailureEmailNotified
	case stLimitCheckFailedPushNotified:
		notificationType = notification.Failed
		nextCtx.State = stFailureEmailNotified
	case stCompletedNotified:
		notificationType = notification.Success
		nextCtx.State = stCompletedEmail
	default:
		slog.FromContext(ctx).Warn(logCharge, "Invalid state for notification")
		return nil, errors.New("invalid state for notification")
	}

	username := w.resolveUserName(ctx, transactionDTO.CustomerID)
	additionalParams := additionalParamsForChargeNotification(nextCtx)
	notificationRequest := w.buildEmailNotificationRequest(ctx, notificationType, transactionDTO, username, additionalParams)

	if notificationRequest == nil {
		return nextCtx, nil
	}

	if feeWaived, okMap := additionalParams[constant.FeeWaive]; okMap {
		if i, okInt := feeWaived.(int64); okInt {
			notificationRequest.Params["processing_fee_waived"] = notification.FormatAmount(i)
		}
	}
	bgCtx := bgcontext.BackgroundWithValue(ctx)
	gconcurrent.Go(bgCtx, logCharge, transaction.EmailNotify(w.EmailNotifier, notificationType, notificationRequest, logCharge))

	return nextCtx, nil
}

func (w *WorkflowImpl) buildPushNotificationRequest(ctx context.Context, notificationType notification.Type, transactionDTO *dto.Transaction,
	additionalParams map[string]interface{}) *notification.Request {
	params := make(map[string]string)
	params["amount"] = decimal.New(transactionDTO.RequestAmount, -sgdExponent).String()
	params["tail_number"] = transaction.GetTailCardNumberFromMetaData(ctx, transactionDTO.Metadata, logCharge)
	params["merchant_name"] = transactionDTO.MerchantDescription
	dateTime := transactionDTO.CreationTimestamp.Truncate(time.Second)
	loc, _ := time.LoadLocation(timeZone)
	params["date_time"] = dateTime.In(loc).Format(time.ANSIC)
	return &notification.Request{
		RecipientID: transactionDTO.CustomerID,
		TemplateID:  config.PushTemplateNameMap[string(notificationType)],
		Params:      params,
	}
}

func (w *WorkflowImpl) buildEmailNotificationRequest(ctx context.Context, notificationType notification.Type,
	transactionDTO *dto.Transaction, username string, additionalParams map[string]interface{}) *notification.Request {
	params := make(map[string]string)
	params[notification.TemplatePreferredName] = username
	params["tail_number"] = transaction.GetTailCardNumberFromMetaData(ctx, transactionDTO.Metadata, logCharge)
	params[notification.TemplateAmount] = notification.FormatAmount(transactionDTO.RequestAmount)
	params[notification.TemplateMerchant] = notification.GetMerchantName(ctx, transactionDTO)
	params[notification.TemplateForeignAmount] = notification.FormatAmount(transactionDTO.OriginalAmount)
	params[notification.TemplateForeignCurrency] = currency.GetByNumericCode(transactionDTO.OriginalCurrency).Code
	dateTime := transactionDTO.CreationTimestamp.Truncate(time.Second)
	loc, _ := time.LoadLocation(timeZone)
	params["date_time"] = dateTime.In(loc).Format(time.ANSIC)
	templateDto := notification.DetectNotificationTemplate(notificationType, transactionDTO, params, additionalParams)
	if templateDto == nil || templateDto.EmailTemplateName == "" {
		slog.FromContext(ctx).Warn("PigeonClient.Push", fmt.Sprintf("Skip sending email for %s - %s - %s - %s transaction", transactionDTO.SubType, transactionDTO.CaptureMode, transactionDTO.Status, transactionDTO.CaptureMethod))
		return nil
	}
	return &notification.Request{
		RecipientID: transactionDTO.CustomerID,
		TemplateID:  templateDto.EmailTemplateName,
		Params:      params,
	}
}

func (w *WorkflowImpl) resolveUserName(ctx context.Context, safeID string) string {
	slog.FromContext(ctx).Debug(logCharge, "calling customer master - GetCustomer")
	customerObj, err := w.CustomerMasterClient.GetCustomer(ctx, &customerMasterAPI.GetCustomerRequest{
		ID: safeID,
		Target: &customerMasterAPI.TargetGroup{
			ServiceID: constant.ServiceID,
		},
	})
	if err != nil {
		slog.FromContext(ctx).Warn(logCharge, "CustomerMasterClient.GetCustomer error", slog.Error(err))
		return ""
	}
	customerData := &customerMasterAPI.Customer{}
	if err = json.Unmarshal(customerObj.Customer.Data, customerData); err != nil {
		return ""
	}

	// customer preferredName can be nil if not set
	preferredName := *customerData.Name
	if customerData.PreferredName != nil {
		preferredName = *customerData.PreferredName
	}

	return preferredName
}
