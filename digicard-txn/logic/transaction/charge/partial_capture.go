package charge

import (
	"context"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	paymentCoreAPI "gitlab.myteksi.net/dakota/payment/payment-core/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

// nolint
func (w *WorkflowImpl) partialCapture(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logCharge, "Invalid context passed in capture state")
		return nil, logic.ErrInvalidContext
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.TransactionIDTag, currCtx.Charge.PaymentTransactionID))
	nextCtx := currCtx.Clone()

	captureData, ok2 := params.(*dto.ChargeManualData)
	if !ok2 {
		slog.FromContext(ctx).Warn(logCharge, "Wrong params passed in prepareCapture state")
		return nil, logic.ErrInvalidParams
	}
	nextCtx.ChargeManualData = captureData

	metaDataJSON, err := updateChargeMetaData(ctx, captureData, nextCtx.Charge.Metadata)
	if err != nil {
		return nil, api.DefaultInternalServerError
	}
	nextCtx.Charge.Metadata = metaDataJSON

	slog.FromContext(ctx).Info(logCharge, "capture scenario: PARTIAL CAPTURE")
	captureRequest := &paymentCoreAPI.InternalCaptureRequest{
		ReferenceID:   transitionID,
		Amount:        nextCtx.ChargeManualData.Amount.RequestAmount,
		Currency:      nextCtx.ChargeManualData.Amount.RequestCurrency,
		AuthTxID:      nextCtx.Charge.PaymentTransactionID,
		Final:         false,
		PostingSource: nextCtx.ChargeManualData.PostingSource,
	}

	response, err := w.PaymentCoreClient.InternalCapture(ctx, captureRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logCharge, "PARTIAL CAPTURE: PaymentCoreClient.InternalCapture", slog.Error(err))
		return nil, err
	}

	respStatus := logic.TransactionStatus(response.Status)
	switch respStatus {
	case logic.Success:
		nextCtx.State = stPartialCaptureCompleted
		// Update the capture amount if given settlement is successful to maintain data stability
		nextCtx.Charge.CaptureAmount = captureData.Amount.RequestAmount
		nextCtx.Charge.CaptureAmountTillDate = nextCtx.Charge.CaptureAmountTillDate + captureData.Amount.RequestAmount
		nextCtx.Charge.CaptureOriginalAmount = captureData.Amount.OriginalAmount
		nextCtx.Charge.CaptureOriginalAmountTillDate = nextCtx.Charge.CaptureOriginalAmountTillDate + captureData.Amount.OriginalAmount
		slog.FromContext(ctx).Info(logCharge, "PARTIAL CAPTURE: Internal Capture transaction status is successful")
	case logic.Processing:
		nextCtx.State = stCaptureProcessing
		slog.FromContext(ctx).Info(logCharge, "PARTIAL CAPTURE: Internal Capture transaction status is processing")
	case logic.Failed:
		nextCtx.State = stPartialCaptureInitiated
		nextCtx.Charge.StatusReason = response.Error.Code
		nextCtx.Charge.StatusReasonDescription = response.Error.Message
		slog.FromContext(ctx).Warn(logCharge, "PARTIAL CAPTURE: Internal Capture transaction status is failed")
		w.Stats(ctx, string(logic.Error), nextCtx.Charge.StatusReason)
	}
	nextCtx.Charge.Status = dto.StateToStatus(nextCtx.State)
	nextCtx.Charge.Properties = dto.UpdateProperties(nextCtx.Charge.Properties, "", "", int(nextCtx.State), 0)
	if err := UpdateDBCharge(ctx, logCharge, &currCtx.Charge, &nextCtx.Charge); err != nil {
		return nil, err
	}

	return nextCtx, nil
}

func (w *WorkflowImpl) persistPartialCapture(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logCharge, "PARTIAL CAPTURE: Invalid context passed in completeCapture state")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	streamMessage := currCtx.StreamMessage

	respStatus := logic.TransactionStatus(streamMessage.Status)
	switch respStatus {
	case logic.Success:
		// Update the capture amount if given settlement is successful to maintain data stability
		nextCtx.Charge.CaptureAmount = nextCtx.ChargeManualData.Amount.RequestAmount
		nextCtx.Charge.CaptureAmountTillDate = nextCtx.Charge.CaptureAmountTillDate + nextCtx.ChargeManualData.Amount.RequestAmount
		nextCtx.Charge.CaptureOriginalAmount = nextCtx.ChargeManualData.Amount.OriginalAmount
		nextCtx.Charge.CaptureOriginalAmountTillDate = nextCtx.Charge.CaptureOriginalAmountTillDate + nextCtx.ChargeManualData.Amount.OriginalAmount
		slog.FromContext(ctx).Info(logCharge, "PARTIAL CAPTURE: Internal Capture transaction status is successful")
		nextCtx.State = stPartialCaptureCompleted
	case logic.Failed:
		nextCtx.Charge.StatusReason = streamMessage.ErrorCode
		nextCtx.Charge.StatusReasonDescription = streamMessage.ErrorMessage
		slog.FromContext(ctx).Warn(logCharge, "PARTIAL CAPTURE: Internal Capture transaction status is failed")
		w.Stats(ctx, string(logic.Error), nextCtx.Charge.StatusReason)
		nextCtx.State = stPartialCaptureInitiated
	}
	nextCtx.Charge.Properties = dto.UpdateProperties(nextCtx.Charge.Properties, "", "", int(nextCtx.State), 0)

	if err := UpdateDBCharge(ctx, logCharge, &currCtx.Charge, &nextCtx.Charge); err != nil {
		return nil, err
	}
	return nextCtx, nil
}
