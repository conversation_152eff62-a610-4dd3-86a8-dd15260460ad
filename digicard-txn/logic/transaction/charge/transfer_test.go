package charge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"

	"gitlab.myteksi.net/dakota/klient/errorhandling"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	paymentcoreAPI "gitlab.myteksi.net/dakota/payment/payment-core/api"
	paymentcore "gitlab.myteksi.net/dakota/payment/payment-core/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
	accountAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestTransfer(t *testing.T) {
	errDummy := errors.New("simulate error")
	testcardID := uuid.NewString()
	defaultCreateCharge := &dto.CreateCharge{
		IdempotencyKey: uuid.NewString(),
		AmountParams: &api.Amount{
			RequestAmount:    100,
			RequestCurrency:  "SGD",
			MarkUpAmount:     10,
			OriginalAmount:   90,
			OriginalCurrency: "SGD",
		},
		TransactionCode: &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.PhysicalCardSpendTransactionType,
			SubType: dto.MastercardTransactionSubtype,
		},
		MetaData: &api.MetaData{NetworkID: "MDT"},
	}

	createChargeWithAdditionalCharges := &dto.CreateCharge{
		IdempotencyKey: uuid.NewString(),
		AmountParams: &api.Amount{
			RequestAmount:    100,
			RequestCurrency:  "SGD",
			MarkUpAmount:     10,
			OriginalAmount:   90,
			OriginalCurrency: "SGD",
		},
		TransactionCode: &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.PhysicalCardSpendTransactionType,
			SubType: dto.MastercardTransactionSubtype,
		},
		MetaData: &api.MetaData{NetworkID: "MDT"},
		AdditionalCharges: []dto.AdditionalCharge{
			{
				Amount:        100,
				Remarks:       "test",
				FeeChargeType: "test",
			},
		},
	}

	createChargeWithFeeWaive := &dto.CreateCharge{
		IdempotencyKey: uuid.NewString(),
		AmountParams: &api.Amount{
			RequestAmount:    100,
			RequestCurrency:  "SGD",
			MarkUpAmount:     10,
			OriginalAmount:   90,
			OriginalCurrency: "SGD",
		},
		TransactionCode: &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.PhysicalCardSpendTransactionType,
			SubType: dto.MastercardTransactionSubtype,
		},
		MetaData: &api.MetaData{NetworkID: "MDT"},
		AdditionalCharges: []dto.AdditionalCharge{
			{
				Amount:        100,
				Remarks:       "test",
				FeeChargeType: constant.FeeWaive,
			},
		},
	}

	scenarios := []struct {
		name              string
		creatCharge       *dto.CreateCharge
		captureMethod     string
		workflowState     workflowengine.State
		errClient         error
		errStorage        error
		expectedErr       error
		expectedStatus    string
		bypassPaymentCore bool
	}{
		{
			name:              "Automatic charge failed",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Automatic),
			errClient:         errDummy,
			expectedErr:       errDummy,
			bypassPaymentCore: false,
		},
		{
			name:              "Automatic charge stTransactionCompleted",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Automatic),
			expectedStatus:    string(logic.Success),
			workflowState:     stTransactionCompleted,
			bypassPaymentCore: false,
		},
		{
			name:              "Automatic charge stTransactionProcessing",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Automatic),
			expectedStatus:    string(logic.Processing),
			workflowState:     stTransactionProcessing,
			bypassPaymentCore: false,
		},
		{
			name:              "Automatic charge stTransferFailed",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Automatic),
			expectedStatus:    string(logic.Failed),
			workflowState:     stTransferFailed,
			bypassPaymentCore: false,
		},
		{
			name:              "Manual charge failed",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Manual),
			errClient:         errDummy,
			expectedErr:       errDummy,
			bypassPaymentCore: false,
		},
		{
			name:              "Manual charge stAuthSuccess",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Manual),
			expectedStatus:    string(logic.Success),
			workflowState:     stAuthSuccess,
			bypassPaymentCore: false,
		},
		{
			name:              "Manual charge stAuthProcessing",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Manual),
			expectedStatus:    string(logic.Processing),
			workflowState:     stAuthProcessing,
			bypassPaymentCore: false,
		},
		{
			name:              "Manual charge stAuthFailed",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Manual),
			expectedStatus:    string(logic.Failed),
			workflowState:     stAuthFailed,
			bypassPaymentCore: false,
		},
		{
			name:              "Manual charge failed 2",
			creatCharge:       defaultCreateCharge,
			expectedStatus:    string(logic.Failed),
			errStorage:        errDummy,
			expectedErr:       errDummy,
			bypassPaymentCore: false,
		},
		{
			name:              "Bypass payment core",
			expectedStatus:    string(logic.Completed),
			workflowState:     stTransactionCompletedWithBypass,
			bypassPaymentCore: true,
		},
		{
			name:              "Update db fail",
			creatCharge:       defaultCreateCharge,
			captureMethod:     string(dto.Manual),
			expectedStatus:    string(logic.Success),
			workflowState:     stAuthSuccess,
			bypassPaymentCore: false,
		},
		{
			name:              "Automatic charge stTransactionProcessing- with Additional charge",
			creatCharge:       createChargeWithAdditionalCharges,
			captureMethod:     string(dto.Automatic),
			expectedStatus:    string(logic.Processing),
			workflowState:     stTransactionProcessing,
			bypassPaymentCore: false,
		}, {
			name:              "Automatic charge stTransactionProcessing- with Additional charge, Fee waived",
			creatCharge:       createChargeWithFeeWaive,
			captureMethod:     string(dto.Automatic),
			expectedStatus:    string(logic.Processing),
			workflowState:     stTransactionProcessing,
			bypassPaymentCore: false,
		},
	}

	for _, scenario := range scenarios {
		testcase := scenario
		t.Run(testcase.name, func(t *testing.T) {
			mockStorageDAO := &storage.MockIChargeDAO{}
			mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(testcase.errStorage).Once()
			storage.ChargeD = mockStorageDAO

			mockPaymentCoreClient := &paymentcore.PaymentCore{}
			response := paymentcoreAPI.InternalResponse{
				TxID:   "1",
				Status: testcase.expectedStatus,
				Error: &paymentcoreAPI.Error{
					Code:    "1018",
					Message: "Dummy Error Message.",
				},
			}
			mockPaymentCoreClient.On("InternalAuth", mock.Anything, mock.Anything).Return(&response, testcase.errClient).Once()
			mockPaymentCoreClient.On("InternalTransfer", mock.Anything, mock.Anything).Return(&response, testcase.errClient).Once()

			w := WorkflowImpl{
				PaymentCoreClient: mockPaymentCoreClient,
				StatsD:            statsd.NewNoop(),
				FeatureFlags: &config.FeatureFlags{
					EnableTransactionLimit: true,
					BypassPaymentCore:      testcase.bypassPaymentCore,
				},
				PaynetVendorConfig: &config.VendorConfig{
					HoldingAcct: "test_account",
				},
			}

			metaDataJSON, _ := json.Marshal(defaultCreateCharge.MetaData)
			defaultChargeStorage := storage.Charge{
				CardID:        testcardID,
				RequestAmount: 50,
				CaptureMethod: testcase.captureMethod,
				Metadata:      metaDataJSON,
			}
			resp, err := w.transfer(context.Background(), "", &ExecutionData{
				Charge:       defaultChargeStorage,
				CreateCharge: testcase.creatCharge,
			}, "")
			if testcase.expectedErr != nil {
				assert.Error(t, err, testcase.name)
				assert.Equal(t, testcase.expectedErr, err, testcase.name)
				assert.Nil(t, resp, testcase.name)
			} else {
				assert.NoError(t, err, testcase.name)
				assert.NotNil(t, resp, testcase.name)
				assert.Equal(t, testcase.workflowState, resp.GetState(), testcase.name)
			}
		})
	}
}

func TestPersistTransactionStream(t *testing.T) {
	errDummy := errors.New("simulate error")
	scenarios := []struct {
		WorkflowState workflowengine.State
		streamMessage interface{}
		errParams     error
		expectedErr   error
	}{
		{
			errParams:     errDummy,
			expectedErr:   logic.ErrInvalidParams,
			streamMessage: "Stream Message",
		},
		{
			WorkflowState: stTransactionStreamPersisted,
			streamMessage: &dto.StreamMessage{
				ReferenceID: uuid.NewString(),
				TxID:        uuid.NewString(),
			},
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		w := WorkflowImpl{FeatureFlags: &config.FeatureFlags{EnableAutoReversalTestMode: false}}
		resp, err := w.persistTransactionStream(context.Background(), "", &ExecutionData{}, testcase.streamMessage)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
			assert.Equal(t, testcase.WorkflowState, resp.GetState(), description)
		}
	}
}

func TestCompleteTransaction(t *testing.T) {
	errDummy := errors.New("simulate error")
	paynetATMMetadata := api.MetaData{
		TransactionCategory:    "ATM",
		TransactionSubCategory: "WITHDRAWAL",
		NetworkID:              constant.NETWORK_SAN,
	}
	metadata := api.MetaData{
		TransactionCategory:    "POS",
		TransactionSubCategory: "CONTACTLESS",
	}
	scenarios := []struct {
		desc          string
		workflowState workflowengine.State
		chargeStorage storage.Charge
		streamMessage *dto.StreamMessage
		availableBal  *accountAPI.Money
		expectedBal   *api.Balance
		accountErr    error
		errStorage    error
		expectedErr   error
	}{
		{
			desc:          "stTransactionCompleted",
			chargeStorage: createSampleChargeRequest(paynetATMMetadata),
			availableBal: &accountAPI.Money{
				CurrencyCode: "MYR",
				Val:          100,
			},
			workflowState: stTransactionCompleted,
			streamMessage: &dto.StreamMessage{
				Status: "SUCCESS",
			},
			expectedBal: &api.Balance{
				Currency: "MYR",
				Amount:   100,
			},
		},
		{
			desc:          "stTransferFailed",
			chargeStorage: createSampleChargeRequest(metadata),
			workflowState: stTransferFailed,
			streamMessage: &dto.StreamMessage{
				Status: "FAILED",
			},
		},
		{
			desc:          "error update db",
			chargeStorage: createSampleChargeRequest(metadata),
			streamMessage: &dto.StreamMessage{
				Status: "SUCCESS",
			},
			errStorage:  errDummy,
			expectedErr: errDummy,
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			mockStorageDAO := &storage.MockIChargeDAO{}
			mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(scenario.errStorage).Once()
			storage.ChargeD = mockStorageDAO
			mockAccountAPI := &accountMock.AccountService{}
			mockAccountAPI.On("GetAccountBalance", mock.Anything, mock.Anything).Return(&accountAPI.GetAccountBalanceResponse{
				AvailableBalance: scenario.availableBal,
			}, scenario.accountErr)
			w := WorkflowImpl{
				StatsD:             statsd.NewNoop(),
				AccountServiceDBMY: mockAccountAPI,
			}
			resp, err := w.completeTransaction(context.Background(), "", &ExecutionData{
				StreamMessage: scenario.streamMessage,
				Charge:        scenario.chargeStorage,
				CreateCharge:  &dto.CreateCharge{AmountParams: &api.Amount{}},
			}, "")
			expectedCtx, _ := resp.(*ExecutionData)
			if scenario.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, scenario.workflowState, resp.GetState())
				assert.Equal(t, scenario.expectedBal, expectedCtx.CreateCharge.AmountParams.AvailableBalance)
			}
		})
	}
}

func TestGetAccountBalance(t *testing.T) {
	errDummy := errors.New("simulate error")
	testAccountID := uuid.NewString()
	defaultAvailBal := &accountAPI.Money{
		CurrencyCode: "SGD",
		Val:          100,
	}

	scenarios := []struct {
		desc         string
		findAccResp  *accountAPI.GetAccountBalanceResponse
		expectedResp *accountAPI.GetAccountBalanceResponse
		availableBal *accountAPI.Money
		accountID    string
		expectedErr  error
		findErr      error
	}{
		{
			desc:      "happy-path",
			accountID: testAccountID,
			expectedResp: &accountAPI.GetAccountBalanceResponse{
				AvailableBalance: defaultAvailBal,
			},
			findAccResp: &accountAPI.GetAccountBalanceResponse{
				AvailableBalance: defaultAvailBal,
			},
		},
		{
			desc:        "find fail Balance",
			accountID:   testAccountID,
			findErr:     errDummy,
			expectedErr: logic.ErrGenericServer,
		},
		{
			desc:        "nil Balance",
			accountID:   testAccountID,
			findAccResp: &accountAPI.GetAccountBalanceResponse{},
			expectedErr: logic.BuildErrorResponse(api.RequestFailed, "could not fetch account balance"),
		},
		{
			desc:        "account not found",
			accountID:   testAccountID,
			findErr:     &errorhandling.Error{Code: "1218"},
			expectedErr: logic.BuildErrorResponse(api.AccountIDInvalid, "Invalid Account ID"),
		},
	}
	for _, _scenarios := range scenarios {
		scenario := _scenarios
		t.Run(scenario.desc, func(t *testing.T) {
			mockAccountAPI := &accountMock.AccountService{}
			mockAccountAPI.On("GetAccountBalance", mock.Anything, mock.Anything).Return(
				scenario.findAccResp, scenario.findErr)

			w := WorkflowImpl{
				AccountServiceDBMY: mockAccountAPI,
				StatsD:             statsd.NewNoop(),
			}

			resp, err := w.getAccountBalance(context.Background(), scenario.accountID)
			if scenario.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, scenario.expectedResp, resp)
			}
		})

	}
}

func TestChargeWithCapture(t *testing.T) {
	errDummy := errors.New("simulate error")
	testSourceAccountID := uuid.NewString()
	testDestinationAccountID := uuid.NewString()
	testChargeID := uuid.NewString()
	testTransactionID := uuid.NewString()
	defaultChargeStorage := &storage.Charge{
		RequestAmount:        100,
		RequestCurrency:      "MYR",
		SourceAccountID:      testSourceAccountID,
		DestinationAccountID: testDestinationAccountID,
		ChargeID:             testChargeID,
		TransactionDomain:    string(dto.DebitCardDomain),
		TransactionType:      string(dto.ATMCashWithdrawalRefundTransactionType),
		TransactionSubtype:   string(dto.PaynetSANTransactionSubtype),
	}
	scenarios := []struct {
		desc            string
		transactionID   string
		chargeStorage   *storage.Charge
		paymentCoreResp *paymentcoreAPI.InternalResponse
		paymentCoreErr  error
		expectedErr     error
	}{
		{
			desc:          "happy path - txn status success",
			transactionID: testTransactionID,
			chargeStorage: defaultChargeStorage,
			paymentCoreResp: &paymentcoreAPI.InternalResponse{
				Status: string(logic.Success),
			},
		},
		{
			desc:          "happy path - txn status failed",
			transactionID: testTransactionID,
			chargeStorage: defaultChargeStorage,
			paymentCoreResp: &paymentcoreAPI.InternalResponse{
				Status: string(logic.Failed),
				Error: &paymentcoreAPI.Error{
					Code:    "1018",
					Message: "Dummy Error Message.",
				},
			},
		},
		{
			desc:          "happy path txn status processing",
			transactionID: testTransactionID,
			chargeStorage: defaultChargeStorage,
			paymentCoreResp: &paymentcoreAPI.InternalResponse{
				Status: string(logic.Processing),
			},
		},
		{
			desc:           "sad path - payment core error",
			transactionID:  testTransactionID,
			chargeStorage:  defaultChargeStorage,
			paymentCoreErr: errDummy,
			expectedErr:    errDummy,
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			mockPaymentCoreClient := &paymentcore.PaymentCore{}
			mockPaymentCoreClient.On("InternalAuth", mock.Anything, mock.Anything).Return(scenario.paymentCoreResp, scenario.paymentCoreErr).Once()

			w := WorkflowImpl{
				StatsD:            statsd.NewNoop(),
				PaymentCoreClient: mockPaymentCoreClient,
			}

			err := w.chargeWithCapture(context.Background(), scenario.transactionID, scenario.chargeStorage, &ExecutionData{}, "test")
			if scenario.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedErr, err)
			}
		})
	}

}
