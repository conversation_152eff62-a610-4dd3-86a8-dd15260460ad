package charge

import (
	"context"
	"errors"
	"fmt"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/server/config"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/external/hedwig"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestPersistAuthStream(t *testing.T) {
	errDummy := errors.New("simulate error")
	scenarios := []struct {
		WorkflowState workflowengine.State
		streamMessage interface{}
		errParams     error
		expectedErr   error
	}{
		{
			errParams:     errDummy,
			expectedErr:   logic.ErrInvalidParams,
			streamMessage: "Stream Message",
		},
		{
			WorkflowState: stAuthStreamPersisted,
			streamMessage: &dto.StreamMessage{
				ReferenceID: uuid.NewString(),
				TxID:        uuid.NewString(),
			},
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		w := WorkflowImpl{FeatureFlags: &config.FeatureFlags{EnableAutoReversalTestMode: false}}
		resp, err := w.persistAuthStream(context.Background(), "", &ExecutionData{}, testcase.streamMessage)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
			assert.Equal(t, testcase.WorkflowState, resp.GetState(), description)
		}
	}
}

func TestCompleteAuth(t *testing.T) {
	errDummy := errors.New("simulate error")
	scenarios := []struct {
		workflowState workflowengine.State
		streamMessage *dto.StreamMessage
		errStorage    error
		expectedErr   error
	}{
		{
			workflowState: stAuthSuccess,
			streamMessage: &dto.StreamMessage{
				Status: "SUCCESS",
			},
		},
		{
			workflowState: stAuthFailed,
			streamMessage: &dto.StreamMessage{
				Status: "FAILED",
			},
		},
		{
			streamMessage: &dto.StreamMessage{
				Status: "SUCCESS",
			},
			errStorage:  errDummy,
			expectedErr: errDummy,
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		mockStorageDAO := &storage.MockIChargeDAO{}
		mockHedwigClient := &hedwig.MockNotifier{}
		mockHedwigClient.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(testcase.errStorage).Once()
		storage.ChargeD = mockStorageDAO

		w := WorkflowImpl{
			StatsD:       statsd.NewNoop(),
			HedwigClient: mockHedwigClient,
			WorkFlowConfig: &config.WorkFlowConfig{
				Charge: &config.WorkFlowConfigCharge{
					CompleteAuthPostAuthDelayInMs: 500,
				},
			},
		}
		resp, err := w.completeAuth(context.Background(), "", &ExecutionData{
			StreamMessage: testcase.streamMessage,
			Charge:        storage.Charge{},
		}, "")
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
			assert.Equal(t, testcase.workflowState, resp.GetState(), description)
		}
	}
}
