package charge

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestWorkflow(t *testing.T) {
	testCreateWorkflow(t)
	testCaptureOrCancelWorkflow(t)
	testGetWorkflow(t)
}

func testCreateWorkflow(t *testing.T) {
	errDummy := errors.New("simulate error")
	testcardID := uuid.NewString()
	defaultChargeStorage := storage.Charge{
		CardID:        testcardID,
		RequestAmount: 50,
	}
	defaultCreateCharge := &dto.CreateCharge{
		IdempotencyKey: uuid.NewString(),
		AmountParams: &api.Amount{
			RequestAmount:    100,
			RequestCurrency:  "SGD",
			MarkUpAmount:     10,
			OriginalAmount:   90,
			OriginalCurrency: "SGD",
		},
		TransactionCode: &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.PhysicalCardSpendTransactionType,
			SubType: dto.MastercardTransactionSubtype,
		},
	}
	scenarios := []struct {
		description    string
		chargeStorage  storage.Charge
		createCharge   *dto.CreateCharge
		WorkflowState  workflowengine.State
		errInit        error
		errGet         error
		errExecute     error
		expectedErr    error
		expectedStatus string
	}{
		{
			description:   "CreateWorkflow with init wf error",
			chargeStorage: defaultChargeStorage,
			createCharge:  defaultCreateCharge,
			errInit:       errDummy,
			expectedErr:   logic.ErrGenericServer,
		},
		{
			description:   "CreateWorkflow with get wf error",
			chargeStorage: defaultChargeStorage,
			createCharge:  defaultCreateCharge,
			errInit:       workflowengine.ErrExecutionAlreadyExists,
			errGet:        errDummy,
			expectedErr:   logic.ErrGenericServer,
		},
		{
			description:   "CreateWorkflow with execute wf error",
			chargeStorage: defaultChargeStorage,
			createCharge:  defaultCreateCharge,
			errExecute:    errDummy,
			expectedErr:   logic.ErrGenericServer,
		},
		{
			description:    "CreateWorkflow without error",
			chargeStorage:  defaultChargeStorage,
			createCharge:   defaultCreateCharge,
			WorkflowState:  stCompletedNotified,
			expectedStatus: "COMPLETED",
		},
	}

	for _, scenario := range scenarios {
		testcase := scenario
		description := scenario.description

		t.Run(description, func(t *testing.T) {
			wfInit = func(ctx context.Context, e workflowengine.Execution, executionData workflowengine.ExecutionData) error {
				return testcase.errInit
			}
			wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State:        testcase.WorkflowState,
					Charge:       testcase.chargeStorage,
					CreateCharge: testcase.createCharge,
				}, testcase.errGet
			}
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State:        testcase.WorkflowState,
					Charge:       testcase.chargeStorage,
					CreateCharge: testcase.createCharge,
				}, testcase.errExecute
			}

			w := WorkflowImpl{}
			resp, err := w.CreateWorkflow(context.Background(), testcase.createCharge)
			if scenario.expectedErr != nil {
				assert.Error(t, err, "err")
				assert.Equal(t, testcase.expectedErr, err, "err")
				assert.Nil(t, resp, "resp")
			} else {
				assert.NoError(t, err, "err")
				assert.NotNil(t, resp, "response")
				assert.Equal(t, testcase.expectedStatus, resp.Status, "status")
			}
		})
	}
}

func testCaptureOrCancelWorkflow(t *testing.T) {
	errDummy := errors.New("simulate error")
	errWorkflowStateTransition := errors.New("error occurred during state transition")
	testcardID := uuid.NewString()
	defaultChargeStorage := storage.Charge{
		CardID:        testcardID,
		RequestAmount: 50,
	}
	scenarios := []struct {
		description          string
		IsCapture            bool
		getWorkflowState     workflowengine.State
		executeWorkflowState workflowengine.State
		errGet               error
		errExecute           error
		expectedErr          error
		expectedStatus       string
	}{
		{
			description: "CaptureOrCancelWorkflow wfGet error",
			errGet:      errDummy,
			expectedErr: logic.ErrGenericServer,
		},
		{
			description:      "CaptureOrCancelWorkflow stAuthFailed",
			getWorkflowState: stAuthFailed,
			expectedErr:      api.ErrorTransactionHasFailed,
		},
		{
			description: "CaptureOrCancelWorkflow transaction has not been authorised yet",
			expectedErr: api.ErrorNotAuthorisedYet,
		},
		{
			description:          "CaptureOrCancelWorkflow stAuthSuccessEmailNotified -> stCapturePrepared returns PROCESSING",
			getWorkflowState:     stAuthSuccessEmailNotified,
			IsCapture:            true,
			executeWorkflowState: stCapturePrepared,
			expectedStatus:       "PROCESSING",
		},
		{
			description:          "CaptureOrCancelWorkflow stAuthSuccessEmailNotified -> stCancelPrepared returns PROCESSING",
			getWorkflowState:     stAuthSuccessEmailNotified,
			IsCapture:            false,
			executeWorkflowState: stCancelPrepared,
			expectedStatus:       "PROCESSING",
		},
		{
			description:      "CaptureOrCancelWorkflow cancel workflow state transition error returns 409",
			getWorkflowState: stAuthSuccessEmailNotified,
			IsCapture:        false,
			errExecute:       errWorkflowStateTransition,
			expectedErr:      logic.ErrStatusConflict,
		},
		{
			description:      "CaptureOrCancelWorkflow cancel workflow other error returns 500",
			getWorkflowState: stAuthSuccessEmailNotified,
			IsCapture:        false,
			errExecute:       errDummy,
			expectedErr:      logic.ErrGenericServer,
		},
		{
			description:      "CaptureOrCancelWorkflow capture workflow state transition error returns 500",
			getWorkflowState: stAuthSuccessEmailNotified,
			IsCapture:        true,
			errExecute:       errWorkflowStateTransition,
			expectedErr:      logic.ErrGenericServer,
		},
	}

	for _, scenario := range scenarios {
		description := scenario.description
		testcase := scenario

		t.Run(description, func(t *testing.T) {
			wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State:  testcase.getWorkflowState,
					Charge: defaultChargeStorage,
				}, testcase.errGet
			}
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State:  testcase.executeWorkflowState,
					Charge: defaultChargeStorage,
				}, testcase.errExecute
			}
			chargeManualData := &dto.ChargeManualData{
				IsCapture:     testcase.IsCapture,
				ChargeStorage: &defaultChargeStorage,
			}

			w := WorkflowImpl{}
			resp, err := w.CaptureOrCancelWorkflow(context.Background(), chargeManualData)
			if scenario.expectedErr != nil {
				assert.Error(t, err, description)
				assert.Equal(t, testcase.expectedErr, err, description)
				assert.Nil(t, resp, description)
			} else {
				assert.NoError(t, err, description)
				assert.NotNil(t, resp, description)
				assert.Equal(t, testcase.expectedStatus, resp.Status, description)
			}
		})
	}
}

func testGetWorkflow(t *testing.T) {
	errDummy := errors.New("simulate error")
	defaultChargeStorage := storage.Charge{
		CardID:        uuid.NewString(),
		RequestAmount: 50,
	}
	scenarios := []struct {
		description    string
		WorkflowState  workflowengine.State
		errGet         error
		expectedErr    error
		expectedStatus string
	}{
		{
			description: "GetWorkflow with error",
			errGet:      errDummy,
			expectedErr: logic.ErrGenericServer,
		},
		{
			description:    "GetWorkflow without error",
			WorkflowState:  stCompletedNotified,
			expectedStatus: "COMPLETED",
		},
	}

	for _, scenario := range scenarios {
		description := scenario.description
		testcase := scenario

		t.Run(description, func(t *testing.T) {
			wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State:  testcase.WorkflowState,
					Charge: defaultChargeStorage,
				}, testcase.errGet
			}

			w := WorkflowImpl{}
			resp, err := w.GetWorkflow(context.Background(), uuid.NewString())
			if scenario.expectedErr != nil {
				assert.Error(t, err, "err")
				assert.Equal(t, testcase.expectedErr, err, "err")
				assert.Nil(t, resp, "resp")
			} else {
				assert.NoError(t, err, "err")
				assert.NotNil(t, resp, "resp")
				assert.Equal(t, testcase.expectedStatus, resp.Status, "status")
			}
		})
	}
}
