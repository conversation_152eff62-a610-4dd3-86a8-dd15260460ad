package charge

import (
	"context"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/bgcontext"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

//nolint:dupl
func (w *WorkflowImpl) persistAuthStream(ctx context.Context, _ string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	logTag := logHandler + ".persistAuthStream"
	slog.FromContext(ctx).Info(logTag, "persistAuthStream handler start")
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "Invalid context passed in persistAuthStream state")
		return nil, logic.ErrInvalidContext
	}

	if w.FeatureFlags.EnableAutoReversalTestMode && w.FeatureFlags.AutoReversalCardID == currCtx.CardID { // will remove after finish testing on staging
		slog.FromContext(ctx).Info(logCharge, "Auto reversal test mode ON, begin sleep to simulate timeout")
		time.Sleep((time.Duration(w.TimeLimitConfig.RetryCount) * w.TimeLimitConfig.Sync * time.Millisecond) + 1000*time.Millisecond) //sleep a little longer to make sure we time out
		slog.FromContext(ctx).Info(logCharge, "Auto reversal test mode ON, end sleep to simulate timeout")
	}

	nextCtx := currCtx.Clone()
	if err := PersistStream(ctx, nextCtx, logTag, params, stAuthStreamPersisted); err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, "persistAuthStream handler end")
	return nextCtx, nil
}

func (w *WorkflowImpl) completeAuth(ctx context.Context, _ string, execData workflowengine.ExecutionData, _ interface{}) (workflowengine.ExecutionData, error) {
	logTag := logHandler + ".completeAuth"
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "Invalid context passed in completeAuth state")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	streamMessage := currCtx.StreamMessage

	respStatus := logic.TransactionStatus(streamMessage.Status)
	switch respStatus {
	case logic.Success:
		nextCtx.State = stAuthSuccess
		slog.FromContext(ctx).Info(logTag, "Internal Auth transaction status is successful")
		w.Stats(ctx, string(logic.Authorised), "")
		w.LatencyStats(ctx, nextCtx.CreationTimestamp, string(logic.Authorised))
	case logic.Failed:
		nextCtx.State = stAuthFailed
		nextCtx.Charge.StatusReason = streamMessage.ErrorCode
		nextCtx.Charge.StatusReasonDescription = streamMessage.ErrorMessage
		slog.FromContext(ctx).Info(logTag, "Internal Auth transaction status is failed")
		w.Stats(ctx, string(logic.Failed), "")
	}
	nextCtx.Charge.PaymentTransactionID = streamMessage.TxID
	nextCtx.Charge.Status = dto.StateToStatus(nextCtx.State)
	nextCtx.Charge.Properties = dto.UpdateProperties(nextCtx.Charge.Properties, "", "", int(nextCtx.State), 0)

	if err := UpdateDBCharge(ctx, logTag, &currCtx.Charge, &nextCtx.Charge); err != nil {
		return nil, err
	}

	// perform post-auth actions in another thread so that it won't block sending the response back to EN
	bgCtx := bgcontext.BackgroundWithValue(ctx)

	duration := w.WorkFlowConfig.Charge.CompleteAuthPostAuthDelayInMs * time.Millisecond
	gconcurrent.Go(bgCtx, logTag,
		transaction.ExecuteEventAsyncWithDelay(nextCtx.Charge.ReferenceID, evnPostAuth, logTag, workflowID, duration))
	return nextCtx, nil
}
