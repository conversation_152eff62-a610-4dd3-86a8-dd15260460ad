// Package chargestatus ...
package chargestatus

import (
	"context"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	noTransactionDataFound = "no transaction found for given request"
)

// FetchChargeStatusClient defines the client to fetch the status of a charge.
type FetchChargeStatusClient interface {
	FetchChargeStatus(ctx context.Context, req *api.FetchChargeStatusRequest) (*api.ChargeResponse, error)
}

//go:generate mockery --name FetchChargeStatusClient --inpackage --case=underscore

// NewChargeStatusClient creates a new transaction client.
func NewChargeStatusClient() FetchChargeStatusClient {
	return &ClientImpl{}
}

// ClientImpl ...
type ClientImpl struct {
	StatsD statsd.Client `inject:"statsD"`
}

// FetchChargeStatus implements FetchChargeStatusClient
func (c *ClientImpl) FetchChargeStatus(ctx context.Context, req *api.FetchChargeStatusRequest) (*api.ChargeResponse, error) {
	query := []data.Condition{
		data.EqualTo("VendorChargeID", req.VendorChargeID),
		data.EqualTo("ReferenceID", req.IdempotencyKey),
		data.EqualTo("CardID", req.CardID),
		data.Limit(1),
	}

	chargeStorage, err := storage.ChargeD.Find(ctx, query...)

	if err != nil {
		if err == data.ErrNoData {
			return nil, logic.BuildErrorResponse(api.ChargeNotFound, noTransactionDataFound)
		}
		return nil, api.DefaultInternalServerError
	}

	chargeResource := &api.ChargeResource{
		Id:                      chargeStorage[0].ChargeID,
		Status:                  chargeStorage[0].Status,
		VendorChargeID:          chargeStorage[0].VendorChargeID,
		StatusReason:            chargeStorage[0].StatusReason,
		StatusReasonDescription: chargeStorage[0].StatusReasonDescription,
		CreationTimestamp:       chargeStorage[0].CreationTimestamp.Truncate(time.Second),
		ValueTimestamp:          chargeStorage[0].ValueTimestamp.Truncate(time.Second),
	}
	return &api.ChargeResponse{
		Data: chargeResource,
	}, nil
}
