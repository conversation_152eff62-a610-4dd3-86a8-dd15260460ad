// Package transaction ...
package transaction

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/notification"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/dispute"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

// LogDigicardTransaction ...
const (
	LogDigicardTransaction = "digicard_transaction"
)

// WfExecute represents the workflow engine function Execute
var WfExecute = workflowengine.Execute

// LoadTransactionByChargeID gets the resource from charge table based chargeID
func LoadTransactionByChargeID(ctx context.Context, chargeID string) (*storage.Charge, error) {
	filter := []data.Condition{
		data.EqualTo("ChargeID", chargeID),
	}
	records, err := storage.ChargeD.Find(ctx, filter...)
	if err != nil {
		slog.FromContext(ctx).Error("dbError", "Error getting charge row", slog.Error(err))
		return nil, err
	}
	return records[0], nil
}

// LoadTransactionByVendorChargeID gets the resource from charge table based vendorChargeID
func LoadTransactionByVendorChargeID(ctx context.Context, vendorChargeID string) (*storage.Charge, error) {
	filter := []data.Condition{
		data.EqualTo("VendorChargeID", vendorChargeID),
	}
	records, err := storage.ChargeD.Find(ctx, filter...)
	if err != nil {
		slog.FromContext(ctx).Error("dbError", "Error getting charge row", slog.Error(err))
		return nil, err
	}
	return records[0], nil
}

// ExecuteEventAsync triggers a workflow engine event
func ExecuteEventAsync(requestID string, event workflowengine.Event, logTag string, workflowID string) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		ctx = slog.AddTagsToContext(ctx, slog.CustomTag("workflowID", workflowID), slog.CustomTag("requestID", requestID), slog.CustomTag("event", event))
		slog.FromContext(ctx).Info(logTag, "start executing ExecuteEventAsync")
		_, err := WfExecute(ctx, workflowengine.Execution{
			WorkflowID:     workflowID,
			RequestID:      requestID,
			ExecutionEvent: event,
		}, nil)
		if err != nil {
			slog.FromContext(ctx).Warn(logTag, "Error occured while executing ExecuteEventAsync", slog.Error(err))
			return err
		}
		return nil
	}
}

// ExecuteEventAsyncWithDelay triggers a workflow engine event with an delay
func ExecuteEventAsyncWithDelay(requestID string, event workflowengine.Event, logTag string, workflowID string, delay time.Duration) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		ctx = slog.AddTagsToContext(ctx, slog.CustomTag("workflowID", workflowID), slog.CustomTag("requestID", requestID), slog.CustomTag("event", event))
		slog.FromContext(ctx).Info(logTag, "start executing ExecuteEventAsyncWithDelay")
		time.Sleep(delay)
		_, err := WfExecute(ctx, workflowengine.Execution{
			WorkflowID:     workflowID,
			RequestID:      requestID,
			ExecutionEvent: event,
		}, nil)
		if err != nil {
			slog.FromContext(ctx).Error(logTag, "Error occured while executing ExecuteEventAsyncWithDelay", slog.Error(err))
			return err
		}
		return nil
	}
}

// FetchRefundTransactions ...
func FetchRefundTransactions(ctx context.Context, chargeIDs []string, logTag string) ([]*storage.Refund, error) {
	query := []data.Condition{
		data.ContainedIn("OriginalChargeID", dispute.ConvertToInterface(chargeIDs)...),
	}
	refunds, err := storage.RefundD.FindOnSlave(ctx, query...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Error(logTag, "storage.RefundD.FindOnSlave Failed", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	return refunds, nil
}

// FetchRefundTransactionByRefundID ...
func FetchRefundTransactionByRefundID(ctx context.Context, refundID string, logTag string) (*storage.Refund, error) {
	query := []data.Condition{
		data.EqualTo("RefundID", refundID),
	}
	refunds, err := storage.RefundD.FindOnSlave(ctx, query...)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "storage.RefundD.FindOnSlave Failed", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	return refunds[0], nil
}

// GetTailCardNumberFromMetaData returns the last four digits of card number from charge metadata
func GetTailCardNumberFromMetaData(ctx context.Context, transactionMetadata []byte, logTag string) string {
	metaData := make(map[string]interface{})
	if transactionMetadata != nil {
		unmarshallErr := json.Unmarshal(transactionMetadata, &metaData)
		if unmarshallErr != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Error while unmarshalling metadata, %s", unmarshallErr.Error()))
		}
	}
	maskedCardNumber := fmt.Sprintf("%v", metaData["maskedCardNumber"])
	if maskedCardNumber == "" {
		return maskedCardNumber
	}

	return maskedCardNumber[len(maskedCardNumber)-4:]
}

// ResolveLimitName Refer to documents below:
// https://docs.google.com/document/d/15wAQYoE_Lwfzuvodj6cI506Z0dNef5EYnY5-C02Jgu4/edit#heading=h.xene2jhdrovy
// https://docs.google.com/spreadsheets/d/1ZH7eRonyCmlQ1OPkwdQEEitwGjVU2wV3HGYPFpATefs/edit#gid=0
func ResolveLimitName(category, subCategory string) (string, error) {
	txnCategory := constant.TransactionCategory(category)
	txnSubCategory := constant.TransactionSubCategory(subCategory)
	switch txnCategory {
	case constant.TransactionCategoryPOS:
		switch txnSubCategory {
		case constant.TransactionSubCategoryAFD, constant.TransactionSubCategorySwipe,
			constant.TransactionSubCategoryTransit, constant.TransactionSubCategoryContact,
			constant.TransactionSubCategoryManual:
			return constant.TxnLimitPinAndPay, nil
		case constant.TransactionSubCategoryContactless, constant.TransactionSubCategoryContactlessPin:
			return constant.TxnLimitContactless, nil
		default:
			return "", logic.ErrUnsupportedTransactionLimit
		}
	case constant.TransactionCategoryECOM, constant.TransactionCategoryAdjustment, constant.TransactionCategoryMOTO, constant.TransactionCategoryCOF:
		return constant.TxnLimitOnline, nil
	case constant.TransactionCategoryATM:
		if constant.TransactionSubCategoryWithdrawal == txnSubCategory {
			return constant.TxnLimitAtmWithdrawal, nil
		}
		return "", logic.ErrUnsupportedTransactionLimit
	default:
		return "", logic.ErrUnsupportedTransactionLimit
	}
}

// IsPaynetATM ...
func IsPaynetATM(category, subCategory, networkID string) bool {
	txnCategory := constant.TransactionCategory(category)
	txnSubCategory := constant.TransactionSubCategory(subCategory)
	return txnCategory == constant.TransactionCategoryATM &&
		txnSubCategory == constant.TransactionSubCategoryWithdrawal &&
		networkID == constant.NETWORK_SAN
}

// PushNotify used to send push notification in background
func PushNotify(notifier notification.TransactionNotifier, notificationType notification.Type, transactionDto *dto.Transaction,
	additionalParams map[string]interface{}, logTag string) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		err := notifier.Notify(ctx, notificationType, transactionDto, additionalParams)
		if err != nil {
			slog.FromContext(ctx).Error(logTag, "Error in publishing push notification", slog.Error(err))
			return err
		}
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Successfully published push notification type: "+string(notificationType)))
		return nil
	}
}

// EmailNotify used to send email notification in background
func EmailNotify(notifier notification.TransactionNotifier, notificationType notification.Type, req *notification.Request, logTag string) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		err := notifier.NotifyV2(ctx, req)
		if err != nil {
			slog.FromContext(ctx).Error(logTag, "Error in publishing email notification", slog.Error(err))
			return err
		}
		slog.FromContext(ctx).Info(logTag, "Successfully published email notification type: "+string(notificationType))
		return nil
	}
}
