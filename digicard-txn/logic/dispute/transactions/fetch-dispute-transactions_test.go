package transactions

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"

	"gitlab.myteksi.net/dakota/servus/v2/data"

	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"

	disputeHelper "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/dispute"
)

const (
	customerID       = "CUST1234"
	chargeID         = "CHARGE1234"
	disputeID        = "DISPUTE1234"
	refundID         = "REFUND1234"
	maskedCardNumber = "525578XXXXXX2300"
)

func TestFetchDisputeTransactions(t *testing.T) {
	mockDisputeDAO := &storage.MockIDisputeDAO{}
	storage.DisputeD = mockDisputeDAO
	mockChargeDAO := &storage.MockIChargeDAO{}
	storage.ChargeD = mockChargeDAO
	mockRefundDAO := &storage.MockIRefundDAO{}
	storage.RefundD = mockRefundDAO

	dispute := &storage.Dispute{
		ChargeID:  chargeID,
		DisputeID: disputeID,
	}
	charge := &storage.Charge{
		ChargeID: chargeID,
		Metadata: json.RawMessage(`{"currencyConvType": "LOCAL", "maskedCardNumber": "` + maskedCardNumber + `"}`),
	}
	refund := &storage.Refund{
		OriginalChargeID: chargeID,
		RefundID:         refundID,
		ValueTimestamp:   time.Now(),
		Amount:           10,
	}

	t.Run("Valid flow", func(t *testing.T) {
		client := clientImpl{}
		mockDisputeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Dispute{dispute}, nil).Once()
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		mockRefundDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Refund{refund}, nil).Once()
		req := &api.FetchDisputeTransactionsRequest{CustomerID: customerID}
		resp, err := client.FetchDisputeTransactions(context.Background(), req)
		assert.NotNil(t, resp)
		assert.Equal(t, chargeID, resp.DisputeTransactions[0].TransactionID)
		assert.Equal(t, disputeID, resp.DisputeTransactions[0].Disputes[0].DisputeID)
		assert.Equal(t, refundID, resp.DisputeTransactions[0].Refunds[0].RefundID)
		assert.Nil(t, err)
	})
	t.Run("When fetchDisputes Failed", func(t *testing.T) {
		client := clientImpl{}
		mockDisputeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		req := &api.FetchDisputeTransactionsRequest{CustomerID: customerID}
		resp, err := client.FetchDisputeTransactions(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, logic.BuildErrorResponse(api.DisputeNotFound, noDisputeFound), err)
	})
	t.Run("When fetchTransactions Failed", func(t *testing.T) {
		client := clientImpl{}
		mockDisputeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Dispute{dispute}, nil).Once()
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		req := &api.FetchDisputeTransactionsRequest{CustomerID: customerID}
		resp, err := client.FetchDisputeTransactions(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
	t.Run("When fetchRefundByTransactions Failed", func(t *testing.T) {
		client := clientImpl{}
		mockDisputeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Dispute{dispute}, nil).Once()
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		mockRefundDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		req := &api.FetchDisputeTransactionsRequest{CustomerID: customerID}
		resp, err := client.FetchDisputeTransactions(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
	t.Run("When convertToResponse Failed", func(t *testing.T) {
		charge.Metadata = json.RawMessage(`{`)
		client := clientImpl{}
		mockDisputeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Dispute{dispute}, nil).Once()
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		mockRefundDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Refund{refund}, nil).Once()
		req := &api.FetchDisputeTransactionsRequest{CustomerID: customerID}
		resp, err := client.FetchDisputeTransactions(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestFetchDisputes(t *testing.T) {
	mockDisputeDAO := &storage.MockIDisputeDAO{}
	storage.DisputeD = mockDisputeDAO
	dispute := &storage.Dispute{
		ChargeID:  chargeID,
		DisputeID: disputeID,
	}
	sgLocation, _ := disputeHelper.GetTimeLocation()
	t.Run("Valid flow", func(t *testing.T) {
		client := clientImpl{}
		mockDisputeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Dispute{dispute}, nil).Once()
		req := &api.FetchDisputeTransactionsRequest{CustomerID: customerID}
		resp, err := client.fetchDisputes(context.Background(), req, sgLocation)
		assert.NotNil(t, resp)
		assert.Equal(t, chargeID, resp[0].ChargeID)
		assert.Nil(t, err)
	})
	t.Run("When no dispute found", func(t *testing.T) {
		client := clientImpl{}
		mockDisputeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		req := &api.FetchDisputeTransactionsRequest{CustomerID: customerID}
		resp, err := client.fetchDisputes(context.Background(), req, sgLocation)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, logic.BuildErrorResponse(api.DisputeNotFound, noDisputeFound), err)
	})
	t.Run("storage.DisputeD.FindOnSlave Failed", func(t *testing.T) {
		client := clientImpl{}
		mockDisputeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		req := &api.FetchDisputeTransactionsRequest{CustomerID: customerID}
		resp, err := client.fetchDisputes(context.Background(), req, sgLocation)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestFetchTransactions(t *testing.T) {
	mockChargeDAO := &storage.MockIChargeDAO{}
	storage.ChargeD = mockChargeDAO
	charge := &storage.Charge{
		ChargeID: chargeID,
		Metadata: json.RawMessage(`{"currencyConvType": "LOCAL", "maskedCardNumber": "` + maskedCardNumber + `"}`),
	}
	t.Run("Valid flow", func(t *testing.T) {
		client := clientImpl{}
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		resp, err := client.fetchTransactions(context.Background(), []string{chargeID})
		assert.NotNil(t, resp)
		assert.Equal(t, chargeID, resp[0].ChargeID)
		assert.Nil(t, err)
	})
	t.Run("storage.ChargeD.FindOnSlave Failed", func(t *testing.T) {
		client := clientImpl{}
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		resp, err := client.fetchTransactions(context.Background(), []string{chargeID})
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestConvertToResponse(t *testing.T) {
	t.Run("Valid flow", func(t *testing.T) {
		dispute := &storage.Dispute{
			ChargeID:  chargeID,
			DisputeID: disputeID,
		}
		charge := &storage.Charge{
			ChargeID: chargeID,
			Metadata: json.RawMessage(`{"currencyConvType": "LOCAL", "maskedCardNumber": "` + maskedCardNumber + `"}`),
		}
		refund := &storage.Refund{
			OriginalChargeID: chargeID,
			RefundID:         refundID,
			ValueTimestamp:   time.Now(),
			Amount:           10,
		}
		client := clientImpl{}
		resp, err := client.convertToResponse(context.Background(), []*storage.Dispute{dispute}, []*storage.Charge{charge}, []*storage.Refund{refund})
		assert.NotNil(t, resp)
		assert.Equal(t, maskedCardNumber, resp.DisputeTransactions[0].MaskedCardNumber)
		assert.Nil(t, err)
	})
	t.Run("when json.Unmarshal Fails", func(t *testing.T) {
		dispute := &storage.Dispute{
			ChargeID:  chargeID,
			DisputeID: disputeID,
		}

		charge := &storage.Charge{
			ChargeID: chargeID,
			Metadata: json.RawMessage(`{`),
		}
		refund := &storage.Refund{
			OriginalChargeID: chargeID,
			RefundID:         refundID,
			ValueTimestamp:   time.Now(),
			Amount:           10,
		}
		client := clientImpl{}
		resp, err := client.convertToResponse(context.Background(), []*storage.Dispute{dispute}, []*storage.Charge{charge}, []*storage.Refund{refund})
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}
