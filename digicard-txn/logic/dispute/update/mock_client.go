// Code generated by mockery v2.14.0. DO NOT EDIT.

package update

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"

	mock "github.com/stretchr/testify/mock"
)

// MockClient is an autogenerated mock type for the Client type
type MockClient struct {
	mock.Mock
}

// UpdateDispute provides a mock function with given fields: ctx, request
func (_m *MockClient) UpdateDispute(ctx context.Context, request *api.UpdateDisputeRequest) (*api.UpdateDisputeResponse, error) {
	ret := _m.Called(ctx, request)

	var r0 *api.UpdateDisputeResponse
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateDisputeRequest) *api.UpdateDisputeResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateDisputeResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateDisputeRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockClient creates a new instance of MockClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockClient(t mockConstructorTestingTNewMockClient) *MockClient {
	mock := &MockClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
