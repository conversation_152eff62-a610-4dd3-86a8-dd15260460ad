// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// NewDigicardCoreT is an autogenerated mock type for the NewDigicardCoreT type
type NewDigicardCoreT struct {
	mock.Mock
}

// Cleanup provides a mock function with given fields: _a0
func (_m *NewDigicardCoreT) Cleanup(_a0 func()) {
	_m.Called(_a0)
}

// Errorf provides a mock function with given fields: format, args
func (_m *NewDigicardCoreT) Errorf(format string, args ...interface{}) {
	var _ca []interface{}
	_ca = append(_ca, format)
	_ca = append(_ca, args...)
	_m.Called(_ca...)
}

// FailNow provides a mock function with given fields:
func (_m *NewDigicardCoreT) FailNow() {
	_m.Called()
}

// Logf provides a mock function with given fields: format, args
func (_m *NewDigicardCoreT) Logf(format string, args ...interface{}) {
	var _ca []interface{}
	_ca = append(_ca, format)
	_ca = append(_ca, args...)
	_m.Called(_ca...)
}

type mockConstructorTestingTNewNewDigicardCoreT interface {
	mock.TestingT
	Cleanup(func())
}

// NewNewDigicardCoreT creates a new instance of NewDigicardCoreT. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewNewDigicardCoreT(t mockConstructorTestingTNewNewDigicardCoreT) *NewDigicardCoreT {
	mock := &NewDigicardCoreT{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
