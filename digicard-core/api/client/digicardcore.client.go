// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: digicard_core.proto
package client

import (
	bytes "bytes"
	context "context"
	fmt "fmt"
	_go "github.com/json-iterator/go"
	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
	http "net/http"
)

// DigicardCoreClient makes calls to DigicardCore service.
type DigicardCoreClient struct {
	machinery klient.RoundTripper
}

// MakeDigicardCoreClient instantiates a new DigicardCoreClient.
// Deprecated: Use NewDigicardCoreClient instead
func MakeDigicardCoreClient(initializer klient.Initializer) (*DigicardCoreClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &DigicardCoreClient{
		machinery: roundTripper,
	}, nil
}

// NewDigicardCoreClient instantiates a new DigicardCoreClient.
func NewDigicardCoreClient(baseURL string, options ...klient.Option) (*DigicardCoreClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &DigicardCoreClient{
		machinery: roundTripper,
	}, nil
}

func (d *DigicardCoreClient) GetAllCards(ctx context.Context) (*api.GetAllCardsResponse, error) {
	reqShell := (*GetAllCardsRequestShell)(&struct{}{})
	resShell := &GetAllCardsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getAllCardsDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetAllCardsResponse)(resShell), err
}

func (d *DigicardCoreClient) GetSpecificCard(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error) {
	reqShell := (*GetSpecificCardRequestShell)(req)
	resShell := &GetSpecificCardResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getSpecificCardDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CardDetail)(resShell), err
}

func (d *DigicardCoreClient) CreateCard(ctx context.Context, req *api.CreateCardRequest) (*api.CardResponse, error) {
	reqShell := (*CreateCardRequestShell)(req)
	resShell := &CreateCardResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createCardDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CardResponse)(resShell), err
}

func (d *DigicardCoreClient) UpdateCardStatus(ctx context.Context, req *api.UpdateCardRequest) (*api.CardResponse, error) {
	reqShell := (*UpdateCardStatusRequestShell)(req)
	resShell := &UpdateCardStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateCardStatusDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CardResponse)(resShell), err
}

func (d *DigicardCoreClient) CancelCard(ctx context.Context, req *api.CancelCardRequest) (*api.CardResponse, error) {
	reqShell := (*CancelCardRequestShell)(req)
	resShell := &CancelCardResponseShell{}
	clientCtx := klient.MakeContext(ctx, &cancelCardDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CardResponse)(resShell), err
}

func (d *DigicardCoreClient) GetCardDetailsWithEncryption(ctx context.Context, req *api.GetCardDetailsWithEncryptionRequest) (*api.GetCardDetailsWithEncryptionResponse, error) {
	reqShell := (*GetCardDetailsWithEncryptionRequestShell)(req)
	resShell := &GetCardDetailsWithEncryptionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCardDetailsWithEncryptionDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCardDetailsWithEncryptionResponse)(resShell), err
}

func (d *DigicardCoreClient) PatchUpdateUserMailingAddress(ctx context.Context, req *api.PatchUpdateUserMailingAddressRequest) error {
	reqShell := (*PatchUpdateUserMailingAddressRequestShell)(req)
	resShell := &PatchUpdateUserMailingAddressResponseShell{}
	clientCtx := klient.MakeContext(ctx, &patchUpdateUserMailingAddressDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) AcceptCardsTerms(ctx context.Context) error {
	reqShell := (*AcceptCardsTermsRequestShell)(&struct{}{})
	resShell := &AcceptCardsTermsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &acceptCardsTermsDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) CardSetPin(ctx context.Context, req *api.CardSetPinRequest) error {
	reqShell := (*CardSetPinRequestShell)(req)
	resShell := &CardSetPinResponseShell{}
	clientCtx := klient.MakeContext(ctx, &cardSetPinDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) GetCardPinEncryptionKey(ctx context.Context, req *api.GetCardPinEncryptionKeyRequest) (*api.GetCardPinEncryptionKeyResponse, error) {
	reqShell := (*GetCardPinEncryptionKeyRequestShell)(req)
	resShell := &GetCardPinEncryptionKeyResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCardPinEncryptionKeyDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCardPinEncryptionKeyResponse)(resShell), err
}

func (d *DigicardCoreClient) CardNameCheck(ctx context.Context, req *api.CardNameCheckRequest) (*api.CardNameCheckResponse, error) {
	reqShell := (*CardNameCheckRequestShell)(req)
	resShell := &CardNameCheckResponseShell{}
	clientCtx := klient.MakeContext(ctx, &cardNameCheckDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CardNameCheckResponse)(resShell), err
}

func (d *DigicardCoreClient) CreateCheckoutPreviewCardByCardVariantID(ctx context.Context, req *api.CreateCheckoutPreviewCardByCardVariantIDRequest) (*api.CreateCheckoutPreviewCardByCardVariantIDResponse, error) {
	reqShell := (*CreateCheckoutPreviewCardByCardVariantIDRequestShell)(req)
	resShell := &CreateCheckoutPreviewCardByCardVariantIDResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createCheckoutPreviewCardByCardVariantIDDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateCheckoutPreviewCardByCardVariantIDResponse)(resShell), err
}

// GetCardByVendorCardID is to get the card details by the vendor card id
func (d *DigicardCoreClient) GetCardByVendorCardID(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error) {
	reqShell := (*GetCardByVendorCardIDRequestShell)(req)
	resShell := &GetCardByVendorCardIDResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCardByVendorCardIDDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CardDetail)(resShell), err
}

// GetCardByInternalCardID is to get the card details by the card id
func (d *DigicardCoreClient) GetCardByInternalCardID(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error) {
	reqShell := (*GetCardByInternalCardIDRequestShell)(req)
	resShell := &GetCardByInternalCardIDResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCardByInternalCardIDDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CardDetail)(resShell), err
}

// AccountStatusEnquiry check the status of the associated account of the card
func (d *DigicardCoreClient) AccountStatusEnquiry(ctx context.Context, req *api.AccountStatusEnquiryRequest) (*api.AccountStatusEnquiryResponse, error) {
	reqShell := (*AccountStatusEnquiryRequestShell)(req)
	resShell := &AccountStatusEnquiryResponseShell{}
	clientCtx := klient.MakeContext(ctx, &accountStatusEnquiryDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.AccountStatusEnquiryResponse)(resShell), err
}

// GetAccountStatus check the status of the associated account of the card
func (d *DigicardCoreClient) GetAccountStatus(ctx context.Context, req *api.AccountStatusEnquiryRequest) (*api.AccountStatusEnquiryResponse, error) {
	reqShell := (*GetAccountStatusRequestShell)(req)
	resShell := &GetAccountStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getAccountStatusDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.AccountStatusEnquiryResponse)(resShell), err
}

// TriggerOtp triggers the otp for acs validation
func (d *DigicardCoreClient) TriggerOtp(ctx context.Context, req *api.TriggerOtpRequest) (*api.TriggerOtpResponse, error) {
	reqShell := (*TriggerOtpRequestShell)(req)
	resShell := &TriggerOtpResponseShell{}
	clientCtx := klient.MakeContext(ctx, &triggerOtpDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.TriggerOtpResponse)(resShell), err
}

func (d *DigicardCoreClient) GetAllCardDesigns(ctx context.Context) (*api.GetAllCardDesignsResponse, error) {
	reqShell := (*GetAllCardDesignsRequestShell)(&struct{}{})
	resShell := &GetAllCardDesignsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getAllCardDesignsDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetAllCardDesignsResponse)(resShell), err
}

func (d *DigicardCoreClient) GetPhysicalCardInventory(ctx context.Context) (*api.GetPhysicalCardInventoryResponse, error) {
	reqShell := (*GetPhysicalCardInventoryRequestShell)(&struct{}{})
	resShell := &GetPhysicalCardInventoryResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getPhysicalCardInventoryDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetPhysicalCardInventoryResponse)(resShell), err
}

func (d *DigicardCoreClient) OverwritePhysicalCardInventory(ctx context.Context, req *api.OverwritePhysicalCardInventoryRequest) (*api.OverwritePhysicalCardInventoryResponse, error) {
	reqShell := (*OverwritePhysicalCardInventoryRequestShell)(req)
	resShell := &OverwritePhysicalCardInventoryResponseShell{}
	clientCtx := klient.MakeContext(ctx, &overwritePhysicalCardInventoryDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.OverwritePhysicalCardInventoryResponse)(resShell), err
}

func (d *DigicardCoreClient) GetCardLimits(ctx context.Context, req *api.GetCardLimitRequest) (*api.GetCardLimitsResponse, error) {
	reqShell := (*GetCardLimitsRequestShell)(req)
	resShell := &GetCardLimitsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCardLimitsDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCardLimitsResponse)(resShell), err
}

func (d *DigicardCoreClient) GetCardDetailsByCustomer(ctx context.Context, req *api.GetCardDetailsRequest) (*api.GetCardDetailsResponse, error) {
	reqShell := (*GetCardDetailsByCustomerRequestShell)(req)
	resShell := &GetCardDetailsByCustomerResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCardDetailsByCustomerDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCardDetailsResponse)(resShell), err
}

func (d *DigicardCoreClient) UpdateOverseasToggle(ctx context.Context, req *api.ToggleUpdateRequest) (*api.ToggleUpdateResponse, error) {
	reqShell := (*UpdateOverseasToggleRequestShell)(req)
	resShell := &UpdateOverseasToggleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateOverseasToggleDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ToggleUpdateResponse)(resShell), err
}

func (d *DigicardCoreClient) UpdateCnpToggle(ctx context.Context, req *api.ToggleUpdateRequest) (*api.ToggleUpdateResponse, error) {
	reqShell := (*UpdateCnpToggleRequestShell)(req)
	resShell := &UpdateCnpToggleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateCnpToggleDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ToggleUpdateResponse)(resShell), err
}

func (d *DigicardCoreClient) TransactionDisputeCheck(ctx context.Context, req *api.TransactionDisputeCheckRequest) (*api.TransactionDisputeCheckResponse, error) {
	reqShell := (*TransactionDisputeCheckRequestShell)(req)
	resShell := &TransactionDisputeCheckResponseShell{}
	clientCtx := klient.MakeContext(ctx, &transactionDisputeCheckDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.TransactionDisputeCheckResponse)(resShell), err
}

func (d *DigicardCoreClient) PhysicalCardActivation(ctx context.Context, req *api.PhysicalCardActivationRequest) error {
	reqShell := (*PhysicalCardActivationRequestShell)(req)
	resShell := &PhysicalCardActivationResponseShell{}
	clientCtx := klient.MakeContext(ctx, &physicalCardActivationDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) FetchCardLimits(ctx context.Context, req *api.FetchCardLimitsRequest) (*api.FetchCardLimitsResponse, error) {
	reqShell := (*FetchCardLimitsRequestShell)(req)
	resShell := &FetchCardLimitsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &fetchCardLimitsDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.FetchCardLimitsResponse)(resShell), err
}

func (d *DigicardCoreClient) BankBlock(ctx context.Context, req *api.BankBlockRequest) error {
	reqShell := (*BankBlockRequestShell)(req)
	resShell := &BankBlockResponseShell{}
	clientCtx := klient.MakeContext(ctx, &bankBlockDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) BankUnblock(ctx context.Context, req *api.BankUnblockRequest) error {
	reqShell := (*BankUnblockRequestShell)(req)
	resShell := &BankUnblockResponseShell{}
	clientCtx := klient.MakeContext(ctx, &bankUnblockDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) FreezeCardInternal(ctx context.Context, req *api.FreezeCardInternalRequest) error {
	reqShell := (*FreezeCardInternalRequestShell)(req)
	resShell := &FreezeCardInternalResponseShell{}
	clientCtx := klient.MakeContext(ctx, &freezeCardInternalDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) UnfreezeCardInternal(ctx context.Context, req *api.UnfreezeCardInternalRequest) error {
	reqShell := (*UnfreezeCardInternalRequestShell)(req)
	resShell := &UnfreezeCardInternalResponseShell{}
	clientCtx := klient.MakeContext(ctx, &unfreezeCardInternalDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) CancelCardInternal(ctx context.Context, req *api.CancelCardInternalRequest) error {
	reqShell := (*CancelCardInternalRequestShell)(req)
	resShell := &CancelCardInternalResponseShell{}
	clientCtx := klient.MakeContext(ctx, &cancelCardInternalDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) CardActivateAndSetPin(ctx context.Context, req *api.CardActivateAndSetPinRequest) error {
	reqShell := (*CardActivateAndSetPinRequestShell)(req)
	resShell := &CardActivateAndSetPinResponseShell{}
	clientCtx := klient.MakeContext(ctx, &cardActivateAndSetPinDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (d *DigicardCoreClient) GetFeatureFlags(ctx context.Context, req *api.GetFeatureFlagsRequest) (*api.GetFeatureFlagsResponse, error) {
	reqShell := (*GetFeatureFlagsRequestShell)(req)
	resShell := &GetFeatureFlagsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getFeatureFlagsDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetFeatureFlagsResponse)(resShell), err
}

func (d *DigicardCoreClient) UpdateOverseasToggleInternal(ctx context.Context, req *api.InternalToggleUpdateRequest) (*api.InternalToggleUpdateResponse, error) {
	reqShell := (*UpdateOverseasToggleInternalRequestShell)(req)
	resShell := &UpdateOverseasToggleInternalResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateOverseasToggleInternalDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.InternalToggleUpdateResponse)(resShell), err
}

func (d *DigicardCoreClient) UpdateCnpToggleInternal(ctx context.Context, req *api.InternalToggleUpdateRequest) (*api.InternalToggleUpdateResponse, error) {
	reqShell := (*UpdateCnpToggleInternalRequestShell)(req)
	resShell := &UpdateCnpToggleInternalResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateCnpToggleInternalDescriptor)
	err := d.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.InternalToggleUpdateResponse)(resShell), err
}

// GetAllCardsRequestShell is a wrapper to make the object a klient.Request
type GetAllCardsRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetAllCardsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/cards"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetAllCardsResponseShell is a wrapper to make the object a klient.Request
type GetAllCardsResponseShell api.GetAllCardsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetAllCardsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetSpecificCardRequestShell is a wrapper to make the object a klient.Request
type GetSpecificCardRequestShell api.GetSpecificCardRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetSpecificCardRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/" + fmt.Sprint(g.CardID)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetSpecificCardResponseShell is a wrapper to make the object a klient.Request
type GetSpecificCardResponseShell api.CardDetail

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetSpecificCardResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateCardRequestShell is a wrapper to make the object a klient.Request
type CreateCardRequestShell api.CreateCardRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateCardRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateCardResponseShell is a wrapper to make the object a klient.Request
type CreateCardResponseShell api.CardResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateCardResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdateCardStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateCardStatusRequestShell api.UpdateCardRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateCardStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/" + fmt.Sprint(u.CardID)
	fullURL := baseURL + filledPath

	pathVar0 := u.CardID
	u.CardID = ""

	defer func() {
		u.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateCardStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateCardStatusResponseShell api.CardResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateCardStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CancelCardRequestShell is a wrapper to make the object a klient.Request
type CancelCardRequestShell api.CancelCardRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CancelCardRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/" + fmt.Sprint(c.CardID)
	fullURL := baseURL + filledPath

	pathVar0 := c.CardID
	c.CardID = ""

	defer func() {
		c.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("DELETE", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CancelCardResponseShell is a wrapper to make the object a klient.Request
type CancelCardResponseShell api.CardResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CancelCardResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetCardDetailsWithEncryptionRequestShell is a wrapper to make the object a klient.Request
type GetCardDetailsWithEncryptionRequestShell api.GetCardDetailsWithEncryptionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCardDetailsWithEncryptionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/get-details"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCardDetailsWithEncryptionResponseShell is a wrapper to make the object a klient.Request
type GetCardDetailsWithEncryptionResponseShell api.GetCardDetailsWithEncryptionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCardDetailsWithEncryptionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// PatchUpdateUserMailingAddressRequestShell is a wrapper to make the object a klient.Request
type PatchUpdateUserMailingAddressRequestShell api.PatchUpdateUserMailingAddressRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (p *PatchUpdateUserMailingAddressRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/mailing-address"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(p)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PATCH", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// PatchUpdateUserMailingAddressResponseShell is a wrapper to make the object a klient.Request
type PatchUpdateUserMailingAddressResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (p *PatchUpdateUserMailingAddressResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// AcceptCardsTermsRequestShell is a wrapper to make the object a klient.Request
type AcceptCardsTermsRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (a *AcceptCardsTermsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/terms"
	fullURL := baseURL + filledPath

	return http.NewRequest("POST", fullURL, nil)
}

// AcceptCardsTermsResponseShell is a wrapper to make the object a klient.Request
type AcceptCardsTermsResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (a *AcceptCardsTermsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// CardSetPinRequestShell is a wrapper to make the object a klient.Request
type CardSetPinRequestShell api.CardSetPinRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CardSetPinRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/set-pin/" + fmt.Sprint(c.CardID)
	fullURL := baseURL + filledPath

	pathVar0 := c.CardID
	c.CardID = ""

	defer func() {
		c.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CardSetPinResponseShell is a wrapper to make the object a klient.Request
type CardSetPinResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CardSetPinResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// GetCardPinEncryptionKeyRequestShell is a wrapper to make the object a klient.Request
type GetCardPinEncryptionKeyRequestShell api.GetCardPinEncryptionKeyRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCardPinEncryptionKeyRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/cards/get-key/" + fmt.Sprint(g.CardID)
	fullURL := baseURL + filledPath

	pathVar0 := g.CardID
	g.CardID = ""

	defer func() {
		g.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCardPinEncryptionKeyResponseShell is a wrapper to make the object a klient.Request
type GetCardPinEncryptionKeyResponseShell api.GetCardPinEncryptionKeyResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCardPinEncryptionKeyResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CardNameCheckRequestShell is a wrapper to make the object a klient.Request
type CardNameCheckRequestShell api.CardNameCheckRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CardNameCheckRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/name/check"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CardNameCheckResponseShell is a wrapper to make the object a klient.Request
type CardNameCheckResponseShell api.CardNameCheckResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CardNameCheckResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// CreateCheckoutPreviewCardByCardVariantIDRequestShell is a wrapper to make the object a klient.Request
type CreateCheckoutPreviewCardByCardVariantIDRequestShell api.CreateCheckoutPreviewCardByCardVariantIDRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateCheckoutPreviewCardByCardVariantIDRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/checkout/preview"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateCheckoutPreviewCardByCardVariantIDResponseShell is a wrapper to make the object a klient.Request
type CreateCheckoutPreviewCardByCardVariantIDResponseShell api.CreateCheckoutPreviewCardByCardVariantIDResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateCheckoutPreviewCardByCardVariantIDResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetCardByVendorCardIDRequestShell is a wrapper to make the object a klient.Request
type GetCardByVendorCardIDRequestShell api.GetSpecificCardRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCardByVendorCardIDRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/vendorCard/" + fmt.Sprint(g.VendorCardID)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetCardByVendorCardIDResponseShell is a wrapper to make the object a klient.Request
type GetCardByVendorCardIDResponseShell api.CardDetail

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCardByVendorCardIDResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetCardByInternalCardIDRequestShell is a wrapper to make the object a klient.Request
type GetCardByInternalCardIDRequestShell api.GetSpecificCardRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCardByInternalCardIDRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card-detail/" + fmt.Sprint(g.CardID)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetCardByInternalCardIDResponseShell is a wrapper to make the object a klient.Request
type GetCardByInternalCardIDResponseShell api.CardDetail

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCardByInternalCardIDResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// AccountStatusEnquiryRequestShell is a wrapper to make the object a klient.Request
type AccountStatusEnquiryRequestShell api.AccountStatusEnquiryRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (a *AccountStatusEnquiryRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/account/status"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(a)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// AccountStatusEnquiryResponseShell is a wrapper to make the object a klient.Request
type AccountStatusEnquiryResponseShell api.AccountStatusEnquiryResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (a *AccountStatusEnquiryResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(a)
}

// GetAccountStatusRequestShell is a wrapper to make the object a klient.Request
type GetAccountStatusRequestShell api.AccountStatusEnquiryRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetAccountStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/account/status/" + fmt.Sprint(g.CardID)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetAccountStatusResponseShell is a wrapper to make the object a klient.Request
type GetAccountStatusResponseShell api.AccountStatusEnquiryResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetAccountStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// TriggerOtpRequestShell is a wrapper to make the object a klient.Request
type TriggerOtpRequestShell api.TriggerOtpRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (t *TriggerOtpRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/acs/trigger-otp"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(t)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// TriggerOtpResponseShell is a wrapper to make the object a klient.Request
type TriggerOtpResponseShell api.TriggerOtpResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (t *TriggerOtpResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(t)
}

// GetAllCardDesignsRequestShell is a wrapper to make the object a klient.Request
type GetAllCardDesignsRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetAllCardDesignsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card-designs"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetAllCardDesignsResponseShell is a wrapper to make the object a klient.Request
type GetAllCardDesignsResponseShell api.GetAllCardDesignsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetAllCardDesignsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetPhysicalCardInventoryRequestShell is a wrapper to make the object a klient.Request
type GetPhysicalCardInventoryRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetPhysicalCardInventoryRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card-designs/inventory"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetPhysicalCardInventoryResponseShell is a wrapper to make the object a klient.Request
type GetPhysicalCardInventoryResponseShell api.GetPhysicalCardInventoryResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetPhysicalCardInventoryResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// OverwritePhysicalCardInventoryRequestShell is a wrapper to make the object a klient.Request
type OverwritePhysicalCardInventoryRequestShell api.OverwritePhysicalCardInventoryRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (o *OverwritePhysicalCardInventoryRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card-design/" + fmt.Sprint(o.CardDesignID) + "/overwrite"
	fullURL := baseURL + filledPath

	pathVar0 := o.CardDesignID
	o.CardDesignID = ""

	defer func() {
		o.CardDesignID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(o)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// OverwritePhysicalCardInventoryResponseShell is a wrapper to make the object a klient.Request
type OverwritePhysicalCardInventoryResponseShell api.OverwritePhysicalCardInventoryResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (o *OverwritePhysicalCardInventoryResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(o)
}

// GetCardLimitsRequestShell is a wrapper to make the object a klient.Request
type GetCardLimitsRequestShell api.GetCardLimitRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCardLimitsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card-limits/" + fmt.Sprint(g.CardID)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetCardLimitsResponseShell is a wrapper to make the object a klient.Request
type GetCardLimitsResponseShell api.GetCardLimitsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCardLimitsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetCardDetailsByCustomerRequestShell is a wrapper to make the object a klient.Request
type GetCardDetailsByCustomerRequestShell api.GetCardDetailsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCardDetailsByCustomerRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card-detail-by-customer"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCardDetailsByCustomerResponseShell is a wrapper to make the object a klient.Request
type GetCardDetailsByCustomerResponseShell api.GetCardDetailsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCardDetailsByCustomerResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateOverseasToggleRequestShell is a wrapper to make the object a klient.Request
type UpdateOverseasToggleRequestShell api.ToggleUpdateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateOverseasToggleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/" + fmt.Sprint(u.CardID) + "/overseas/toggle"
	fullURL := baseURL + filledPath

	pathVar0 := u.CardID
	u.CardID = ""

	defer func() {
		u.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateOverseasToggleResponseShell is a wrapper to make the object a klient.Request
type UpdateOverseasToggleResponseShell api.ToggleUpdateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateOverseasToggleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// UpdateCnpToggleRequestShell is a wrapper to make the object a klient.Request
type UpdateCnpToggleRequestShell api.ToggleUpdateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateCnpToggleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/" + fmt.Sprint(u.CardID) + "/cnp/toggle"
	fullURL := baseURL + filledPath

	pathVar0 := u.CardID
	u.CardID = ""

	defer func() {
		u.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateCnpToggleResponseShell is a wrapper to make the object a klient.Request
type UpdateCnpToggleResponseShell api.ToggleUpdateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateCnpToggleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// TransactionDisputeCheckRequestShell is a wrapper to make the object a klient.Request
type TransactionDisputeCheckRequestShell api.TransactionDisputeCheckRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (t *TransactionDisputeCheckRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/transaction/dispute-check"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(t)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// TransactionDisputeCheckResponseShell is a wrapper to make the object a klient.Request
type TransactionDisputeCheckResponseShell api.TransactionDisputeCheckResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (t *TransactionDisputeCheckResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(t)
}

// PhysicalCardActivationRequestShell is a wrapper to make the object a klient.Request
type PhysicalCardActivationRequestShell api.PhysicalCardActivationRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (p *PhysicalCardActivationRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/" + fmt.Sprint(p.CardID) + "/activate"
	fullURL := baseURL + filledPath

	pathVar0 := p.CardID
	p.CardID = ""

	defer func() {
		p.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(p)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// PhysicalCardActivationResponseShell is a wrapper to make the object a klient.Request
type PhysicalCardActivationResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (p *PhysicalCardActivationResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// FetchCardLimitsRequestShell is a wrapper to make the object a klient.Request
type FetchCardLimitsRequestShell api.FetchCardLimitsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (f *FetchCardLimitsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card-limits"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(f)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// FetchCardLimitsResponseShell is a wrapper to make the object a klient.Request
type FetchCardLimitsResponseShell api.FetchCardLimitsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (f *FetchCardLimitsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(f)
}

// BankBlockRequestShell is a wrapper to make the object a klient.Request
type BankBlockRequestShell api.BankBlockRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (b *BankBlockRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/card/" + fmt.Sprint(b.CardID) + "/bank-block"
	fullURL := baseURL + filledPath

	pathVar0 := b.CardID
	b.CardID = ""

	defer func() {
		b.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(b)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// BankBlockResponseShell is a wrapper to make the object a klient.Request
type BankBlockResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (b *BankBlockResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// BankUnblockRequestShell is a wrapper to make the object a klient.Request
type BankUnblockRequestShell api.BankUnblockRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (b *BankUnblockRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/card/" + fmt.Sprint(b.CardID) + "/bank-unblock"
	fullURL := baseURL + filledPath

	pathVar0 := b.CardID
	b.CardID = ""

	defer func() {
		b.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(b)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// BankUnblockResponseShell is a wrapper to make the object a klient.Request
type BankUnblockResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (b *BankUnblockResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// FreezeCardInternalRequestShell is a wrapper to make the object a klient.Request
type FreezeCardInternalRequestShell api.FreezeCardInternalRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (f *FreezeCardInternalRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/card/" + fmt.Sprint(f.CardID) + "/freeze"
	fullURL := baseURL + filledPath

	pathVar0 := f.CardID
	f.CardID = ""

	defer func() {
		f.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(f)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// FreezeCardInternalResponseShell is a wrapper to make the object a klient.Request
type FreezeCardInternalResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (f *FreezeCardInternalResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// UnfreezeCardInternalRequestShell is a wrapper to make the object a klient.Request
type UnfreezeCardInternalRequestShell api.UnfreezeCardInternalRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UnfreezeCardInternalRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/card/" + fmt.Sprint(u.CardID) + "/unfreeze"
	fullURL := baseURL + filledPath

	pathVar0 := u.CardID
	u.CardID = ""

	defer func() {
		u.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UnfreezeCardInternalResponseShell is a wrapper to make the object a klient.Request
type UnfreezeCardInternalResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UnfreezeCardInternalResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// CancelCardInternalRequestShell is a wrapper to make the object a klient.Request
type CancelCardInternalRequestShell api.CancelCardInternalRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CancelCardInternalRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/card/" + fmt.Sprint(c.CardID)
	fullURL := baseURL + filledPath

	pathVar0 := c.CardID
	c.CardID = ""

	defer func() {
		c.CardID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("DELETE", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CancelCardInternalResponseShell is a wrapper to make the object a klient.Request
type CancelCardInternalResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CancelCardInternalResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// CardActivateAndSetPinRequestShell is a wrapper to make the object a klient.Request
type CardActivateAndSetPinRequestShell api.CardActivateAndSetPinRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CardActivateAndSetPinRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/card/activate-and-set-pin"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CardActivateAndSetPinResponseShell is a wrapper to make the object a klient.Request
type CardActivateAndSetPinResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CardActivateAndSetPinResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// GetFeatureFlagsRequestShell is a wrapper to make the object a klient.Request
type GetFeatureFlagsRequestShell api.GetFeatureFlagsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetFeatureFlagsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/cards/feature_flag/" + fmt.Sprint(g.CardID)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetFeatureFlagsResponseShell is a wrapper to make the object a klient.Request
type GetFeatureFlagsResponseShell api.GetFeatureFlagsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetFeatureFlagsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateOverseasToggleInternalRequestShell is a wrapper to make the object a klient.Request
type UpdateOverseasToggleInternalRequestShell api.InternalToggleUpdateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateOverseasToggleInternalRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/overseas/toggle"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateOverseasToggleInternalResponseShell is a wrapper to make the object a klient.Request
type UpdateOverseasToggleInternalResponseShell api.InternalToggleUpdateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateOverseasToggleInternalResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// UpdateCnpToggleInternalRequestShell is a wrapper to make the object a klient.Request
type UpdateCnpToggleInternalRequestShell api.InternalToggleUpdateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateCnpToggleInternalRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal/cnp/toggle"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateCnpToggleInternalResponseShell is a wrapper to make the object a klient.Request
type UpdateCnpToggleInternalResponseShell api.InternalToggleUpdateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateCnpToggleInternalResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

var getAllCardsDescriptor = klient.EndpointDescriptor{
	Name:        "GetAllCards",
	Description: "",
	Method:      "GET",
	Path:        "/v1/cards",
}

var getSpecificCardDescriptor = klient.EndpointDescriptor{
	Name:        "GetSpecificCard",
	Description: "",
	Method:      "GET",
	Path:        "/v1/card/{cardID}",
}

var createCardDescriptor = klient.EndpointDescriptor{
	Name:        "CreateCard",
	Description: "",
	Method:      "POST",
	Path:        "/v1/card",
}

var updateCardStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateCardStatus",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/card/{cardID}",
}

var cancelCardDescriptor = klient.EndpointDescriptor{
	Name:        "CancelCard",
	Description: "",
	Method:      "DELETE",
	Path:        "/v1/card/{cardID}",
}

var getCardDetailsWithEncryptionDescriptor = klient.EndpointDescriptor{
	Name:        "GetCardDetailsWithEncryption",
	Description: "",
	Method:      "POST",
	Path:        "/v1/card/get-details",
}

var patchUpdateUserMailingAddressDescriptor = klient.EndpointDescriptor{
	Name:        "PatchUpdateUserMailingAddress",
	Description: "",
	Method:      "PATCH",
	Path:        "/v1/card/mailing-address",
}

var acceptCardsTermsDescriptor = klient.EndpointDescriptor{
	Name:        "AcceptCardsTerms",
	Description: "",
	Method:      "POST",
	Path:        "/v1/card/terms",
}

var cardSetPinDescriptor = klient.EndpointDescriptor{
	Name:        "CardSetPin",
	Description: "",
	Method:      "POST",
	Path:        "/v1/card/set-pin/{cardID}",
}

var getCardPinEncryptionKeyDescriptor = klient.EndpointDescriptor{
	Name:        "GetCardPinEncryptionKey",
	Description: "",
	Method:      "GET",
	Path:        "/v1/cards/get-key/{cardID}",
}

var cardNameCheckDescriptor = klient.EndpointDescriptor{
	Name:        "CardNameCheck",
	Description: "",
	Method:      "POST",
	Path:        "/v1/name/check",
}

var createCheckoutPreviewCardByCardVariantIDDescriptor = klient.EndpointDescriptor{
	Name:        "CreateCheckoutPreviewCardByCardVariantID",
	Description: "",
	Method:      "POST",
	Path:        "/v1/checkout/preview",
}

var getCardByVendorCardIDDescriptor = klient.EndpointDescriptor{
	Name:        "GetCardByVendorCardID",
	Description: "GetCardByVendorCardID is to get the card details by the vendor card id",
	Method:      "GET",
	Path:        "/v1/vendorCard/{vendorCardID}",
}

var getCardByInternalCardIDDescriptor = klient.EndpointDescriptor{
	Name:        "GetCardByInternalCardID",
	Description: "GetCardByInternalCardID is to get the card details by the card id",
	Method:      "GET",
	Path:        "/v1/card-detail/{cardID}",
}

var accountStatusEnquiryDescriptor = klient.EndpointDescriptor{
	Name:        "AccountStatusEnquiry",
	Description: "AccountStatusEnquiry check the status of the associated account of the card",
	Method:      "POST",
	Path:        "/v1/account/status",
}

var getAccountStatusDescriptor = klient.EndpointDescriptor{
	Name:        "GetAccountStatus",
	Description: "GetAccountStatus check the status of the associated account of the card",
	Method:      "GET",
	Path:        "/v1/account/status/{cardID}",
}

var triggerOtpDescriptor = klient.EndpointDescriptor{
	Name:        "TriggerOtp",
	Description: "TriggerOtp triggers the otp for acs validation",
	Method:      "POST",
	Path:        "/v1/acs/trigger-otp",
}

var getAllCardDesignsDescriptor = klient.EndpointDescriptor{
	Name:        "GetAllCardDesigns",
	Description: "",
	Method:      "GET",
	Path:        "/v1/card-designs",
}

var getPhysicalCardInventoryDescriptor = klient.EndpointDescriptor{
	Name:        "GetPhysicalCardInventory",
	Description: "",
	Method:      "GET",
	Path:        "/v1/card-designs/inventory",
}

var overwritePhysicalCardInventoryDescriptor = klient.EndpointDescriptor{
	Name:        "OverwritePhysicalCardInventory",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/card-design/{cardDesignID}/overwrite",
}

var getCardLimitsDescriptor = klient.EndpointDescriptor{
	Name:        "GetCardLimits",
	Description: "",
	Method:      "GET",
	Path:        "/v1/card-limits/{cardID}",
}

var getCardDetailsByCustomerDescriptor = klient.EndpointDescriptor{
	Name:        "GetCardDetailsByCustomer",
	Description: "",
	Method:      "POST",
	Path:        "/v1/card-detail-by-customer",
}

var updateOverseasToggleDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateOverseasToggle",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/card/{cardID}/overseas/toggle",
}

var updateCnpToggleDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateCnpToggle",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/card/{cardID}/cnp/toggle",
}

var transactionDisputeCheckDescriptor = klient.EndpointDescriptor{
	Name:        "TransactionDisputeCheck",
	Description: "",
	Method:      "POST",
	Path:        "/v1/transaction/dispute-check",
}

var physicalCardActivationDescriptor = klient.EndpointDescriptor{
	Name:        "PhysicalCardActivation",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/card/{cardID}/activate",
}

var fetchCardLimitsDescriptor = klient.EndpointDescriptor{
	Name:        "FetchCardLimits",
	Description: "",
	Method:      "POST",
	Path:        "/v1/card-limits",
}

var bankBlockDescriptor = klient.EndpointDescriptor{
	Name:        "BankBlock",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/internal/card/{cardID}/bank-block",
}

var bankUnblockDescriptor = klient.EndpointDescriptor{
	Name:        "BankUnblock",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/internal/card/{cardID}/bank-unblock",
}

var freezeCardInternalDescriptor = klient.EndpointDescriptor{
	Name:        "FreezeCardInternal",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/internal/card/{cardID}/freeze",
}

var unfreezeCardInternalDescriptor = klient.EndpointDescriptor{
	Name:        "UnfreezeCardInternal",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/internal/card/{cardID}/unfreeze",
}

var cancelCardInternalDescriptor = klient.EndpointDescriptor{
	Name:        "CancelCardInternal",
	Description: "",
	Method:      "DELETE",
	Path:        "/v1/internal/card/{cardID}",
}

var cardActivateAndSetPinDescriptor = klient.EndpointDescriptor{
	Name:        "CardActivateAndSetPin",
	Description: "",
	Method:      "POST",
	Path:        "/v1/card/activate-and-set-pin",
}

var getFeatureFlagsDescriptor = klient.EndpointDescriptor{
	Name:        "GetFeatureFlags",
	Description: "",
	Method:      "GET",
	Path:        "/v1/cards/feature_flag/{cardID}",
}

var updateOverseasToggleInternalDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateOverseasToggleInternal",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/internal/overseas/toggle",
}

var updateCnpToggleInternalDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateCnpToggleInternal",
	Description: "",
	Method:      "PUT",
	Path:        "/v1/internal/cnp/toggle",
}
