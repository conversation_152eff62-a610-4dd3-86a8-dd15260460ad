package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	getalldesigns "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/get_all_designs"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func TestGetAllCardDesigns(t *testing.T) {
	setMockCardDesignDataSet()

	t.Run("valid flow", func(t *testing.T) {
		ctx := setTestCtxWithUserID(userID)
		digicardCoreService := &DigicardCoreService{}
		digicardCoreService.GetAllCardDesignsClient = &getalldesigns.ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedResp := test.ExpectedCardDesignResponse()
		resp, err := digicardCoreService.GetAllCardDesigns(ctx)
		assert.NoError(t, err)
		assert.Equal(t, expectedResp, resp)
	})
}

func TestGetPhysicalCardInventory(t *testing.T) {
	cardDesignDaoMock := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	cardDesignDaoMock.On("Find", mock.Anything, mock.Anything).Return(test.GetMockCardDesigns(), nil)
	mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)
	mockRedis.On("GetBytes", mock.Anything, mock.Anything).Return(nil, redis.ErrNoData)

	t.Run("valid flow", func(t *testing.T) {
		digicardCoreService := &DigicardCoreService{}
		digicardCoreService.GetAllCardDesignsClient = &getalldesigns.ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedResp := test.ExpectedGetPhysicalCardInventoryResponse()
		resp, err := digicardCoreService.GetPhysicalCardInventory(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, expectedResp, resp)
	})
}

func setMockCardDesignDataSet() (*storage.MockICardDesignDAO, *mocks.Client) {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)

	return cardDesignMockDao, mockRedis
}
