package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	fetchCardLimitsTag = "FetchCardLimits"
)

// FetchCardLimits ...
func (d *DigicardCoreService) FetchCardLimits(ctx context.Context, req *api.FetchCardLimitsRequest) (*api.FetchCardLimitsResponse, error) {
	ctx = commonCtx.WithUserID(ctx, req.CustomerID)
	if d.LimitConfigs == nil || len(d.LimitConfigs) <= 0 {
		errorCode := api.InternalServerError
		return nil, servus.ServiceError{
			Code:     string(errorCode),
			Message:  "Preferred card limits are not available",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
	}

	_, err := logic.FetchCardByCardAndUserID(ctx, req.CustomerID, req.CardID)
	if err != nil {
		slog.FromContext(ctx).Warn(fetchCardLimitsTag, "Card not found", slog.Error(err))
		return nil, err
	}

	details, errLimit := d.getLimitDetail(ctx, d.LimitConfigs, req.CardID)
	if errLimit != nil {
		return nil, errLimit
	}

	return &api.FetchCardLimitsResponse{
		Limits: details,
	}, nil
}
