package handlers

import (
	context "context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"

	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"gitlab.com/gx-regional/dbmy/digicard/common/bgcontext"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	commonctx "gitlab.myteksi.net/dakota/common/context"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
)

// UnfreezeCardInternal - This will be used by CRM team to unfreeze the card ...
func (d *DigicardCoreService) UnfreezeCardInternal(ctx context.Context, req *api.UnfreezeCardInternalRequest) error {
	// This function is used to prevent context canceled from front end in short amount of time,
	// and cause the data in our side and 3rd party card provider is not sync
	ctx = bgcontext.BackgroundWithValue(ctx)
	var cancel context.CancelFunc
	ctx, cancel = context.WithTimeout(ctx, d.TimeoutConfig.Timeout)
	defer cancel()

	updateCardRequest := &api.UpdateCardRequest{
		CardID: req.CardID,
		Status: string(constant.CardStatusActive),
	}

	ctx = commonctx.WithUserID(ctx, req.CustomerID)

	_, err := d.UpdateCardStatusClient.UpdateCardStatus(ctx, updateCardRequest, true, false)
	d.publishInternalUnfreezeCardEvent(ctx, err)

	return err
}

func (d *DigicardCoreService) publishInternalUnfreezeCardEvent(ctx context.Context, activityErr error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(audit.CardActivityLogTag, "UnfreezeCardInternal"))
	activityLog := audit.CardActivityAuditRequest{
		Event:       customerjournalapi.CardFreezeUpdateEvent,
		Status:      customerjournalapi.AuditEventSuccess,
		Source:      customerjournalapi.TriggeredByOpsPortalSource,
		BeforeValue: map[string]interface{}{audit.CardStatusDesc: audit.FreezeStatus},
		AfterValue:  map[string]interface{}{audit.CardStatusDesc: audit.UnfreezeStatus},
	}
	if activityErr != nil {
		activityLog.AfterValue = map[string]interface{}{audit.CardStatusDesc: audit.FreezeStatus}
		activityLog.Status = customerjournalapi.AuditEventFailed
		activityLog.FailedReason = activityErr.Error()
	}
	d.CardActivityLog.SendActivityLog(ctx, activityLog)
}
