package handlers

import (
	"net/http"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	specificcard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_specific_card"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	transactionLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
	transactionLimitMock "gitlab.myteksi.net/dakota/transaction-limit/api/mock"
)

func TestGetCardLimits(t *testing.T) {
	setMockDataSet()
	digicardCoreService := getDigicardCoreService()
	mockTransctionLimit := &transactionLimitMock.TransactionLimit{}
	mockCardDao := &storage.MockICardDAO{}
	storage.CardDao = mockCardDao

	t.Run("valid flow", func(t *testing.T) {
		ctx := setTestCtxWithUserID(userID)
		digicardCoreService.TransactionLimitClient = mockTransctionLimit
		mockTransctionLimit.On("SearchTransactionLimitRulesV2", mock.Anything, mock.Anything, mock.Anything).Return(&transactionLimitAPI.GetTransactionLimitResponse{}, nil).Once()
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil).Once()
		resp, err := digicardCoreService.GetCardLimits(ctx, &api.GetCardLimitRequest{CardID: "123"})

		assert.Nil(t, err)
		assert.NotNil(t, resp)
	})

	t.Run("when limit is not configured", func(t *testing.T) {
		ctx := setTestCtxWithUserID(userID)
		digicardCoreService.LimitConfigs = []config.LimitConfig{}
		mockTransctionLimit = &transactionLimitMock.TransactionLimit{}
		digicardCoreService.TransactionLimitClient = mockTransctionLimit
		mockTransctionLimit.On("SearchTransactionLimitRulesV2", mock.Anything, mock.Anything, mock.Anything).Return(&transactionLimitAPI.GetTransactionLimitResponse{}, nil).Once()
		resp, err := digicardCoreService.GetCardLimits(ctx, &api.GetCardLimitRequest{})
		errorCode := api.InternalServerError
		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "Preferred card limits are not available",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})

	t.Run("when card is invalid", func(t *testing.T) {
		ctx := setTestCtxWithUserID(userID)
		digicardCoreService = getDigicardCoreService()
		mockTransctionLimit = &transactionLimitMock.TransactionLimit{}
		digicardCoreService.TransactionLimitClient = mockTransctionLimit

		mockTransctionLimit.On("SearchTransactionLimitRulesV2", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()

		resp, err := digicardCoreService.GetCardLimits(ctx, &api.GetCardLimitRequest{CardID: "123"})

		expectedErr := logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   "No such userID-cardID pair found",
				Path:      "UserID-CardID",
			},
		})
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})

	t.Run("when TXN limit API is failed", func(t *testing.T) {
		ctx := setTestCtxWithUserID(userID)
		digicardCoreService = getDigicardCoreService()
		mockTransctionLimit = &transactionLimitMock.TransactionLimit{}
		digicardCoreService.TransactionLimitClient = mockTransctionLimit

		mockTransctionLimit.On("SearchTransactionLimitRulesV2", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil).Once()

		resp, err := digicardCoreService.GetCardLimits(ctx, &api.GetCardLimitRequest{CardID: "123"})

		expectedErr := api.DefaultInternalServerError
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestVerifyCard(t *testing.T) {
	setMockDataSet()
	digicardCoreService := getDigicardCoreService()
	mockCardDao := &storage.MockICardDAO{}
	storage.CardDao = mockCardDao

	t.Run("valid flow", func(t *testing.T) {
		ctx := setTestCtxWithUserID(userID)
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil).Once()
		err := digicardCoreService.verifyCard(ctx, "123")

		assert.Nil(t, err)
	})
	t.Run("Invalid flow", func(t *testing.T) {
		ctx := setTestCtxWithUserID(userID)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		err := digicardCoreService.verifyCard(ctx, "123")

		assert.NotNil(t, err)
	})
}

func getDigicardCoreService() *DigicardCoreService {
	digicardCoreService := &DigicardCoreService{}
	limitDetail := &config.LimitConfig{
		Name:                "DIGICARD_DEBITCARD_LIMIT",
		Title:               "Set daily card limit",
		SubTitle:            "Set daily card limit",
		PreferredCardLimits: []int64{1000, 5000, 10000},
	}
	limitDetail2 := &config.LimitConfig{
		Name:                "DIGICARD_DEBITCARD_LIMIT2",
		Title:               "Set daily card limit",
		SubTitle:            "Set daily card limit",
		PreferredCardLimits: []int64{1000, 5000, 10000},
	}
	digicardCoreService.LimitConfigs = []config.LimitConfig{*limitDetail, *limitDetail2}
	digicardCoreService.GetSpecificCardClient = &specificcard.ClientImpl{
		StatsD: statsd.NewNoop(),
	}
	return digicardCoreService
}
