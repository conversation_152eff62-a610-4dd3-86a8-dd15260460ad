package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	specificcard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_specific_card"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	cardID = "123"
	userID = "USER_ID"
	empty  = ""
)

func TestGetCardByInternalCardID(t *testing.T) {
	setMockDataSet()
	t.Run("valid flow", func(t *testing.T) {
		req := &api.GetSpecificCardRequest{
			CardID: cardID,
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		digicardCoreService := &DigicardCoreService{}
		digicardCoreService.GetSpecificCardClient = &specificcard.ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil)
		_, err := digicardCoreService.GetCardByInternalCardID(context.Background(), req)
		assert.Equal(t, err, nil)
	})
	t.Run("missing InternalCardID", func(t *testing.T) {
		ctx := setTestCtxWithUserID(userID)
		req := &api.GetSpecificCardRequest{
			CardID:       "",
			VendorCardID: "",
		}
		digicardCoreService := &DigicardCoreService{}
		digicardCoreService.GetSpecificCardClient = &specificcard.ClientImpl{
			StatsD: statsd.NewNoop(),
		}

		var errs []servus.ErrorDetail
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "InternalCardID is missing",
			Path:      "InternalCardID",
		})
		errorCode := api.BadRequest
		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "request has invalid parameter(s).",
			HTTPCode: errorCode.HTTPStatusCode(),
			Errors:   errs,
		}
		_, err := digicardCoreService.GetCardByInternalCardID(ctx, req)
		assert.Equal(t, err, expectedErr)
	})

	t.Run("error finding internal card ID", func(t *testing.T) {
		req := &api.GetSpecificCardRequest{
			CardID:       cardID,
			VendorCardID: cardID,
		}
		digicardCoreService := &DigicardCoreService{}
		digicardCoreService.GetSpecificCardClient = &specificcard.ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		expectedErr := api.DefaultInternalServerError
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{{}}, api.DefaultInternalServerError)
		_, err := digicardCoreService.GetCardByInternalCardID(context.Background(), req)
		assert.Equal(t, err, expectedErr)
	})
}

func setMockDataSet() {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)
}

func setTestCtxWithUserID(userID string) context.Context {
	return commonCtx.WithUserID(context.Background(), userID)
}
