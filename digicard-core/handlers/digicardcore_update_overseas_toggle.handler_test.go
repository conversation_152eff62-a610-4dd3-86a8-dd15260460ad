package handlers

import (
	"context"
	"testing"

	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/toggle"
)

const (
	ToggleName          = constant.OverseasPOSTXN
	InvalidToggleName   = "OVERSEAS_POS_TXN_DUMMY"
	invalidToggleAction = "Invalid Toggle Action"
	invalidToggleType   = "Invalid Toggle Type"
)

func TestValidateRequest(t *testing.T) {
	service := DigicardCoreService{}
	t.Run("Valid flow", func(t *testing.T) {
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: ToggleName, Action: string(constant.ToggleDisabled)}
		err := service.validateOverseasToggleRequest(req)
		assert.Nil(t, err)
	})
	t.Run("When all input is missing", func(t *testing.T) {
		req := &api.ToggleUpdateRequest{}
		err := service.validateOverseasToggleRequest(req)
		expectedErrs := []servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "cardID is missing",
				Path:      "cardID",
			},
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "type is missing",
				Path:      "type",
			},
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   "invalid toggle type",
				Path:      "type",
			},
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "action is missing",
				Path:      "action",
			},
		}
		assert.NotNil(t, err)
		assert.Equal(t, expectedErrs, err)
	})
}

func TestUpdateOverseasToggle(t *testing.T) {
	ctx := setTestCtxWithUserID(userID)
	t.Run("Valid flow", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		mockCardLogService := &audit.MockCardActivityLogService{}
		service := DigicardCoreService{
			OverseasToggleUpdate: mockClient,
			CardActivityLog:      mockCardLogService,
		}
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: ToggleName, Action: string(constant.ToggleDisabled)}
		mockClient.On("IsValidAction", mock.Anything, mock.Anything).Return(nil).Once()
		mockClient.On("IsValidToggle", mock.Anything, mock.Anything).Return(nil).Once()
		mockClient.On("ActionUpdate", mock.Anything, mock.Anything).Return(&api.ToggleUpdateResponse{Code: req.Action, Type: req.Type}, nil).Once()
		mockCardLogService.On("SendActivityLog", mock.Anything, createUpdateOverseasToggleSuccessEvent()).Once()
		resp, err := service.UpdateOverseasToggle(ctx, req)
		assert.Nil(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, ToggleName, resp.Type)
		assert.Equal(t, string(constant.ToggleDisabled), resp.Code)
		mockCardLogService.AssertNumberOfCalls(t, "SendActivityLog", 1)
	})
	t.Run("When request is invalid", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		mockCardLogService := &audit.MockCardActivityLogService{}
		service := DigicardCoreService{
			OverseasToggleUpdate: mockClient,
			CardActivityLog:      mockCardLogService,
		}
		req := &api.ToggleUpdateRequest{Type: ToggleName, Action: string(constant.ToggleDisabled)}
		resp, err := service.UpdateOverseasToggle(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
	t.Run("When action is invalid", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		mockCardLogService := &audit.MockCardActivityLogService{}
		service := DigicardCoreService{
			OverseasToggleUpdate: mockClient,
			CardActivityLog:      mockCardLogService,
		}
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: ToggleName, Action: "TEST"}
		mockClient.On("IsValidAction", mock.Anything, mock.Anything).Return(logic.BuildErrorResponse(api.InvalidToggleAction, invalidToggleAction)).Once()
		resp, err := service.UpdateOverseasToggle(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
	t.Run("When toggle type is invalid", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		mockCardLogService := &audit.MockCardActivityLogService{}
		service := DigicardCoreService{
			OverseasToggleUpdate: mockClient,
			CardActivityLog:      mockCardLogService,
		}
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: InvalidToggleName, Action: string(constant.ToggleDisabled)}
		mockClient.On("IsValidAction", mock.Anything, mock.Anything).Return(nil).Once()
		mockClient.On("IsValidToggle", mock.Anything, mock.Anything).Return(logic.BuildErrorResponse(api.InvalidToggleType, invalidToggleType)).Once()
		resp, err := service.UpdateOverseasToggle(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
	t.Run("When user Id is missing", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		mockCardLogService := &audit.MockCardActivityLogService{}
		service := DigicardCoreService{
			OverseasToggleUpdate: mockClient,
			CardActivityLog:      mockCardLogService,
		}
		ctx := setTestCtxWithUserID(empty)
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: ToggleName, Action: "TEST"}
		resp, err := service.UpdateOverseasToggle(ctx, req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
}

func createUpdateOverseasToggleSuccessEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:       customerjournalapi.CardOverseasTxnToggleEvent,
		Status:      customerjournalapi.AuditEventSuccess,
		Source:      customerjournalapi.TriggeredByGXBankAppSource,
		BeforeValue: map[string]interface{}{audit.OverseasToggleDesc: constant.ToggleEnabled},
		AfterValue:  map[string]interface{}{audit.OverseasToggleDesc: constant.ToggleDisabled},
	}
}
