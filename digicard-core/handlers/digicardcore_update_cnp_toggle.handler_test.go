package handlers

import (
	"context"
	"testing"

	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/toggle"
)

const (
	CnpToggleName          = constant.CnpTXN
	InvalidCnpToggleName   = "INVALID_CNP_TOGGLE"
	invalidCnpToggleAction = "Invalid Toggle Action"
	invalidCnpToggleType   = "Invalid Toggle Type"
)

func TestValidateCnpUpdateRequest(t *testing.T) {
	service := DigicardCoreService{}
	t.Run("Valid flow", func(t *testing.T) {
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: CnpToggleName, Action: string(constant.ToggleDisabled)}
		err := service.validateCnpToggleRequest(req)
		assert.Nil(t, err)
	})
	t.Run("When all input is missing", func(t *testing.T) {
		req := &api.ToggleUpdateRequest{}
		err := service.validateCnpToggleRequest(req)
		expectedErrs := []servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "cardID is missing",
				Path:      "cardID",
			},
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "type is missing",
				Path:      "type",
			},
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   "invalid toggle type",
				Path:      "type",
			},
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "action is missing",
				Path:      "action",
			},
		}
		assert.NotNil(t, err)
		assert.Equal(t, expectedErrs, err)
	})
}

func TestUpdateCnpToggle(t *testing.T) {
	ctx := setTestCtxWithUserID(userID)
	t.Run("Valid flow", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		mockCardLogService := &audit.MockCardActivityLogService{}
		service := DigicardCoreService{
			CnpToggleUpdateClient: mockClient,
			CardActivityLog:       mockCardLogService,
		}
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: CnpToggleName, Action: string(constant.ToggleDisabled)}
		mockClient.On("IsValidAction", mock.Anything, mock.Anything).Return(nil).Once()
		mockClient.On("IsValidToggle", mock.Anything, mock.Anything).Return(nil).Once()
		mockClient.On("ActionUpdate", mock.Anything, mock.Anything).Return(&api.ToggleUpdateResponse{Code: req.Action, Type: req.Type}, nil).Once()
		mockCardLogService.On("SendActivityLog", mock.Anything, createUpdateCnpToggleSuccessEvent()).Once()
		resp, err := service.UpdateCnpToggle(ctx, req)
		assert.Nil(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, CnpToggleName, resp.Type)
		assert.Equal(t, string(constant.ToggleDisabled), resp.Code)
		mockCardLogService.AssertNumberOfCalls(t, "SendActivityLog", 1)
	})
	t.Run("When request is invalid", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		service := DigicardCoreService{
			CnpToggleUpdateClient: mockClient,
		}
		req := &api.ToggleUpdateRequest{Type: CnpToggleName, Action: string(constant.ToggleDisabled)}
		resp, err := service.UpdateCnpToggle(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
	t.Run("When action is invalid", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		service := DigicardCoreService{
			CnpToggleUpdateClient: mockClient,
		}
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: CnpToggleName, Action: "TEST"}
		expectedErr := logic.BuildErrorResponse(api.InvalidToggleAction, invalidCnpToggleAction)
		mockClient.On("IsValidAction", mock.Anything, mock.Anything).Return(expectedErr).Once()
		resp, err := service.UpdateCnpToggle(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
	t.Run("When toggle type is invalid", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		service := DigicardCoreService{
			CnpToggleUpdateClient: mockClient,
		}
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: InvalidCnpToggleName, Action: string(constant.ToggleDisabled)}
		expectedErr := logic.BuildErrorResponse(api.InvalidToggleType, invalidCnpToggleType)
		mockClient.On("IsValidAction", mock.Anything, mock.Anything).Return(nil).Once()
		mockClient.On("IsValidToggle", mock.Anything, mock.Anything).Return(expectedErr).Once()
		resp, err := service.UpdateCnpToggle(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
	t.Run("When user Id is missing", func(t *testing.T) {
		mockClient := &toggle.MockClient{}
		service := DigicardCoreService{
			CnpToggleUpdateClient: mockClient,
		}
		ctx = setTestCtxWithUserID(empty)
		req := &api.ToggleUpdateRequest{CardID: "1234", Type: CnpToggleName, Action: "TEST"}
		resp, err := service.UpdateCnpToggle(ctx, req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
}

func createUpdateCnpToggleSuccessEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:       customerjournalapi.CardOnlineTxnToggleEvent,
		Status:      customerjournalapi.AuditEventSuccess,
		Source:      customerjournalapi.TriggeredByGXBankAppSource,
		BeforeValue: map[string]interface{}{audit.CnpToggleDesc: constant.ToggleEnabled},
		AfterValue:  map[string]interface{}{audit.CnpToggleDesc: constant.ToggleDisabled},
	}
}
