package handlers

import (
	"context"
	"errors"
	"testing"

	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	pinmanager "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/pin_manager"
)

func TestCardSetPinHandler(t *testing.T) {
	testCtx := context.Background()
	testCtx = commonCtx.WithUserID(testCtx, uuid.NewString())
	mockCardSetPinClient := &pinmanager.MockCardPinManagerClient{}
	mockCardLogService := &audit.MockCardActivityLogService{}
	cardSetPinHappyCaseRequest := &api.CardSetPinRequest{
		CardID:         "cardID",
		EncryptedPin:   "encryptedPin",
		IdempotencyKey: "IdempotencyKey",
	}
	service := DigicardCoreService{
		CardPinManagerClient: mockCardSetPinClient,
		CardActivityLog:      mockCardLogService,
	}
	t.Run("happy path", func(t *testing.T) {
		mockCardSetPinClient.On("SetPin", mock.Anything, mock.Anything, true).Return(nil).Once()
		mockCardLogService.On("SendActivityLog", mock.Anything, createCardSetPinSuccessEvent()).Once()
		err := service.CardSetPin(testCtx, cardSetPinHappyCaseRequest)
		assert.Nil(t, err)
	})

	t.Run("failed path", func(t *testing.T) {
		mockCardSetPinClient.On("SetPin", mock.Anything, mock.Anything, true).Return(api.DefaultInternalServerError).Once()
		mockCardLogService.On("SendActivityLog", mock.Anything, createCardSetPinFailedEvent()).Once()
		err := service.CardSetPin(testCtx, cardSetPinHappyCaseRequest)
		assert.Error(t, err)
	})

	t.Run("missing user-id", func(t *testing.T) {
		mockCardSetPinClient.On("SetPin", mock.Anything, mock.Anything, true).Return(nil).Once()
		var err servus.ServiceError
		errors.As(service.CardSetPin(context.Background(), cardSetPinHappyCaseRequest), &err)
		assert.Error(t, err)
		assert.Equal(t, "X-Grab-Id-Userid", err.Errors[0].Path)
	})
	testCases := []struct {
		name     string
		arg      *api.CardSetPinRequest
		errorLen int
		path     string
	}{
		{
			name: "missing internal CardID",
			arg: &api.CardSetPinRequest{
				CardID:         "",
				EncryptedPin:   "encryptedPin",
				IdempotencyKey: "IdempotencyKey",
			},
			errorLen: 1,
			path:     "cardID",
		},
		{
			name: "missing all card params",
			arg: &api.CardSetPinRequest{
				CardID:         "",
				EncryptedPin:   "",
				IdempotencyKey: "",
			},
			errorLen: 3,
			path:     "cardID",
		},
		{
			name: "missing EncryptedPin",
			arg: &api.CardSetPinRequest{
				CardID:         "cardId",
				EncryptedPin:   "",
				IdempotencyKey: "IdempotencyKey",
			},
			errorLen: 1,
			path:     "encryptedPin",
		},
		{
			name: "missing IdempotencyKey",
			arg: &api.CardSetPinRequest{
				CardID:         "cardId",
				EncryptedPin:   "encrypted",
				IdempotencyKey: "",
			},
			errorLen: 1,
			path:     "idempotencyKey",
		},
	}
	for _, testCase := range testCases {
		testCase := testCase
		t.Run(testCase.name, func(t *testing.T) {
			mockCardSetPinClient.On("SetPin", mock.Anything, mock.Anything, true).Return(nil).Once()
			var respError servus.ServiceError
			errors.As(service.CardSetPin(testCtx, testCase.arg), &respError)
			assert.Equal(t, string(api.BadRequest), respError.Code)
			assert.Equal(t, "request has invalid parameter(s) or header(s).", respError.Message)
			assert.Equal(t, api.BadRequest.HTTPStatusCode(), respError.HTTPCode)
			assert.Equal(t, len(respError.Errors), testCase.errorLen)
			errorDetail := respError.Errors[0]
			assert.Equal(t, "FIELD_MISSING", errorDetail.ErrorCode)
			assert.Equal(t, testCase.path, errorDetail.Path)
		})
	}
}

func createCardSetPinSuccessEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:  customerjournalapi.CardUpdatePinEvent,
		Status: customerjournalapi.AuditEventSuccess,
		Source: customerjournalapi.TriggeredByGXBankAppSource,
	}
}

func createCardSetPinFailedEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:        customerjournalapi.CardUpdatePinEvent,
		Status:       customerjournalapi.AuditEventFailed,
		Source:       customerjournalapi.TriggeredByGXBankAppSource,
		FailedReason: api.DefaultInternalServerError.Error(),
	}
}
