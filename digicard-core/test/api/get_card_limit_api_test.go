package api_test

import (
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	transactionLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
)

var _ = Describe("GetCardLimits", func() {
	var (
		client *hcl.Client
		//errSetup error
	)

	const (
		url = "/v1/card-limits/{1234}"
	)

	BeforeEach(func() {
		mockCardDAO = &storage.MockICardDAO{}
		storage.CardDao = mockCardDAO
		client, _ = hcl.NewClient(server.URL())
		test.SetMockDataForRedisClient(mockRedis)
		test.SetMockDataForCardDesign(mockCardDesignDAO)
	})

	Context("Valid flow", func() {
		apiURL := url

		When("Card ID valid", func() {
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, "AB123"),
			}
			userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
			It("Returns 200 OK", func() {
				mockCardDAO.On("Find", mock.Anything, mock.Anything).Return(nil, nil).Once()
				mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil).Once()

				mockTransactionLimitClient.On("SearchTransactionLimitRulesV2", mock.Anything, mock.Anything, mock.Anything).Return(&transactionLimitAPI.GetTransactionLimitResponse{}, nil).Once()
				resp, err := client.Get(apiURL, userIDModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
			})
		})
	})
	Context("Invalid flow", func() {
		apiURL := url

		When("When limit config is not configured", func() {
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, "AB123"),
			}
			userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
			It("Returns 500", func() {
				service.LimitConfigs = nil
				expectedResponse := `{
								         	"code": "INTERNAL_SERVER_ERROR",
            								"message": "Preferred card limits are not available"
							      	 }`
				resp, err := client.Get(apiURL, userIDModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("When card is invalid", func() {
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, "AB123"),
			}
			userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
			It("Returns 500", func() {
				service.LimitConfigs = []config.LimitConfig{*limitDetail}
				expectedResponse := `{
										"code": "INVALID_PARAMETERS",
            							"message": "request has invalid parameter(s) or header(s).",
            							"errors": [
              								{	
                								"errorCode": "FIELD_INVALID",
                								"message": "No such userID-cardID pair found",
                								"path": "UserID-CardID"
              								}
            							]
									}`
				mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()

				mockTransactionLimitClient.ExpectedCalls = nil
				mockTransactionLimitClient.On("SearchTransactionLimitRulesV2", mock.Anything, mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
				resp, err := client.Get(apiURL, userIDModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(403))
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("When transaction limit returns error", func() {
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, "AB123"),
			}
			userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
			It("Returns 500", func() {
				service.LimitConfigs = []config.LimitConfig{*limitDetail}
				expectedResponse := `{
								         	"code": "INTERNAL_SERVER_ERROR",
            								"message": "There is a problem on our end. Please try again later."
							      	 }`
				mockCardDAO.On("Find", mock.Anything, mock.Anything).Return(nil, nil).Once()
				mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil).Once()

				mockTransactionLimitClient.ExpectedCalls = nil
				mockTransactionLimitClient.On("SearchTransactionLimitRulesV2", mock.Anything, mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
				resp, err := client.Get(apiURL, userIDModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
