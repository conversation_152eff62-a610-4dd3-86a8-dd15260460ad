package api_test

import (
	"fmt"

	"github.com/google/uuid"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("GetCardByInternalCardID", func() {
	var (
		client *hcl.Client
		//errSetup error
		url = fmt.Sprintf("/v1/card-detail/%s", "1234")
	)

	const (
		name = "ABC"
	)

	BeforeEach(func() {
		mockCardDAO = &storage.MockICardDAO{}
		storage.CardDao = mockCardDAO
		client, _ = hcl.NewClient(server.URL())
		test.SetMockDataForRedisClient(mockRedis)
		test.SetMockDataForCardDesign(mockCardDesignDAO)
	})

	Context("Valid flow", func() {
		defaultBody := &api.GetSpecificCardRequest{}
		apiURL := url

		When("Internal card ID valid", func() {
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, "AB123"),
				hcl.JSON(defaultBody),
			}
			defaultBody := &api.GetSpecificCardRequest{}
			defaultBody.VendorCardID = name
			It("Returns 200 OK", func() {
				mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{
					{
						DisplayName:  "Jon Snow",
						Status:       string(constant.CardStatusActive),
						AccountID:    "999",
						UserID:       "abcdef123",
						CardDesignID: test.TestCardDesignID,
					},
				}, nil).Once()
				resp, err := client.Get(apiURL, userIDModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))
				Expect(resp.Body.String).Should(MatchJSON(test.ExpectedCardResponseFromInternalCardJSON))
			})
		})
	})

	Context("invalid request parameters", func() {

		apiURL := fmt.Sprintf("/v1/card-detail/%s", "1234")

		When("error finding vendor card ID", func() {
			defaultBody := &api.GetSpecificCardRequest{}
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, uuid.NewString()),
				hcl.JSON(defaultBody),
			}
			defaultBody.VendorCardID = name
			It("Returns 200 OK", func() {
				mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{{}}, api.DefaultInternalServerError).Once()
				resp, err := client.Get(apiURL, userIDModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				expectedResponse := `{
								         	"code": "INTERNAL_SERVER_ERROR",
            								"message": "There is a problem on our end. Please try again later."
							      	 }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
