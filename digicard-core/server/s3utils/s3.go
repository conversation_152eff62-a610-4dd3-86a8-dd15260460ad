// Package s3utils ..
package s3utils

import (
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const logTag = "s3-client"

var (
	// S3Client ...
	S3Client s3client.S3
	// S3Bucket ...
	S3Bucket string
)

// Init ...
func Init(conf *config.AppConfig, logger slog.YallLogger) {
	client, err := createS3Client(conf)
	if err != nil {
		logger.Fatal(logTag, "failed to init s3 client", slog.Error(err))
	} else {
		S3Client = client
		S3Bucket = conf.S3BucketName
		logger.Info(logTag, "successfully init s3 client")
	}
}

func createS3Client(conf *config.AppConfig) (s3client.S3, error) {
	if conf.S3ClientDevMode {
		return s3client.NewS3ClientForDevelopmentMode()
	}
	return s3client.NewS3Client()
}
