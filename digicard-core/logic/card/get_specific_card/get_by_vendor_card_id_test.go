package specificcard

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func TestGetCardByVendorCardID(t *testing.T) {
	setMockDataSet()
	t.Run("valid flow", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		req := &api.GetSpecificCardRequest{
			CardID:       cardID,
			VendorCardID: cardID,
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil)
		_, err := c.GetCardByVendorCardID(context.Background(), req)
		assert.Equal(t, err, nil)
	})
	t.Run("missing vendorCardID", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		req := &api.GetSpecificCardRequest{
			CardID:       cardID,
			VendorCardID: "",
		}
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		var errs []servus.ErrorDetail
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "VendorCardID is missing",
			Path:      "VendorCardID",
		})
		expectedErr := logic.CustomError(http.StatusBadRequest, &errs)
		_, err := c.GetCardByVendorCardID(context.Background(), req)
		assert.Equal(t, err, expectedErr)
	})
	t.Run("error finding vendor card ID", func(t *testing.T) {
		req := &api.GetSpecificCardRequest{
			CardID:       cardID,
			VendorCardID: cardID,
		}
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		expectedErr := logic.BuildErrorResponse(api.VendorCardIDInvalid, errorVendorID)
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{{}}, api.DefaultInternalServerError)
		_, err := c.GetCardByVendorCardID(context.Background(), req)
		assert.Equal(t, err, expectedErr)
	})
}
