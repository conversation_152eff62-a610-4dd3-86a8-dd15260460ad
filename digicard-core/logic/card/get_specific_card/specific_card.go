// Package specificcard ...
package specificcard

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	card_design_utils "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	logTag        = "GetSpecificCard"
	getByVendorID = "GetCardByVendorCardID"
	digiCardTag   = "digicard_core"
	userIDMissing = "'X-Grab-Id-Userid' is missing"
	errorUserID   = "No such userID-cardID pair found"
	invalidReq    = "invalid request parameters"
	errorVendorID = "No such vendorCardID found"
)

// Client ...
type Client interface {
	GetSpecificCard(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error)
	GetCardByVendorCardID(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error)
	GetCardByInternalCardID(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error)
}

//go:generate mockery --name Client --inpackage --case=underscore

// NewClient creates a new balance client.
func NewClient() Client {
	return &ClientImpl{}
}

// ClientImpl ...
type ClientImpl struct {
	StatsD statsd.Client `inject:"statsD"`
}

// GetSpecificCard returns details of a specific card of a particular user...
func (c *ClientImpl) GetSpecificCard(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error) {
	userID := logic.GetUserID(ctx)
	if userID == "" {
		slog.FromContext(ctx).Warn(logTag, "grab user ID not found")
		c.Stats(constant.Failed, userIDMissing)
		return nil, logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "'X-Grab-Id-Userid' is missing.",
				Path:      "X-Grab-Id-Userid",
			},
		})
	}

	if errs := validateGetSpecificCardParams(req); len(errs) != 0 {
		slog.FromContext(ctx).Warn(logTag, "invalid request parameters")
		c.Stats(constant.Failed, invalidReq)
		return nil, logic.CustomError(http.StatusBadRequest, &errs)
	}

	querySearch, err := logic.FetchCardByCardAndUserID(ctx, userID, req.CardID)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "error in fetching card details", slog.Error(err))
		c.Stats(constant.Failed, errorUserID)
		return nil, logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   errorUserID,
				Path:      "UserID-CardID",
			},
		})
	}

	cardDesign, err := logic.FetchCardDesignByCardDesignID(ctx, querySearch.CardDesignID)
	if err != nil {
		return nil, err
	}
	c.Stats(constant.Success, "")
	return &api.CardDetail{
		CardID:            req.CardID,
		DisplayName:       querySearch.DisplayName,
		Status:            querySearch.Status,
		AccountID:         querySearch.AccountID,
		OrderStatus:       querySearch.OrderStatus,
		CardDesign:        card_design_utils.ToCardDesignDetail(*cardDesign, false),
		CardType:          logic.GetCardType(querySearch),
		ReasonCode:        querySearch.ReasonCode,
		ReasonDescription: querySearch.ReasonDescription,
	}, nil
}

func validateGetSpecificCardParams(req *api.GetSpecificCardRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if req.CardID == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "CardID is missing",
			Path:      "CardID",
		})
	}
	return errs
}

// Stats ...
func (c *ClientImpl) Stats(status string, statusReason string) {
	c.StatsD.Count1(digiCardTag, logTag,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}
