// Code generated by mockery v2.16.0. DO NOT EDIT.

package activate

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"

	mock "github.com/stretchr/testify/mock"
)

// MockPhysicalCardActivateClient is an autogenerated mock type for the PhysicalCardActivateClient type
type MockPhysicalCardActivateClient struct {
	mock.Mock
}

// PhysicalCardActivation provides a mock function with given fields: ctx, req
func (_m *MockPhysicalCardActivateClient) PhysicalCardActivation(ctx context.Context, req *api.PhysicalCardActivationRequest) error {
	ret := _m.Called(ctx, req)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PhysicalCardActivationRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMockPhysicalCardActivateClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockPhysicalCardActivateClient creates a new instance of MockPhysicalCardActivateClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockPhysicalCardActivateClient(t mockConstructorTestingTNewMockPhysicalCardActivateClient) *MockPhysicalCardActivateClient {
	mock := &MockPhysicalCardActivateClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
