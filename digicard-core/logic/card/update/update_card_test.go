package updatecard

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const (
	userID         = "USER_ID"
	empty          = ""
	cardID         = "1123"
	statusReason   = "anything"
	idempotencyKey = "{{$guid}}"
)

func TestInjectHeaderInfo(t *testing.T) {
	t.Run("valid flow", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		err := validateUserIDHeaderInfo(context.Background())
		assert.Equal(t, err, nil)
	})
	t.Run("missing user ID", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return empty
		}
		expectedErr := logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "'X-Grab-Id-Userid' is missing.",
				Path:      "X-Grab-Id-Userid",
			},
		})
		err := validateUserIDHeaderInfo(context.Background())
		assert.Equal(t, err, expectedErr)
	})
}

func TestUpdateCardReqParamsValidator(t *testing.T) {
	t.Run("missing card ID", func(t *testing.T) {
		request := &api.UpdateCardRequest{}
		request.Status = string(constant.CardStatusLocked)
		request.ReasonCode = statusReason
		err := validateUpdateCardRequestParams(request)
		expectedErr := &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "Card-ID is missing",
				Path:      "Card-ID",
			},
		}
		assert.Equal(t, expectedErr, err)
	})
	t.Run("missing status", func(t *testing.T) {
		request := &api.UpdateCardRequest{}
		request.CardID = cardID
		request.ReasonCode = statusReason
		err := validateUpdateCardRequestParams(request)
		expectedErr := &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "Status is missing",
				Path:      "Status",
			},
		}
		assert.Equal(t, expectedErr, err)
	})
	t.Run("invalid status", func(t *testing.T) {
		request := &api.UpdateCardRequest{}
		request.Status = logic.ErrInvalidTargetStatus
		request.CardID = cardID
		request.ReasonCode = statusReason
		err := validateUpdateCardRequestParams(request)
		expectedErr := &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.BadRequest),
				Message:   "Invalid status",
				Path:      "Status",
			},
		}
		assert.Equal(t, expectedErr, err)
	})
}

func TestConvertResponse(t *testing.T) {
	setMockDataSet()
	t.Run("valid flow", func(t *testing.T) {
		data := ExecutionData{
			Request: &api.UpdateCardRequest{},
			Card:    &storage.Card{CardDesignID: test.TestCardDesignID},
		}
		data.Status = string(constant.CardStatusActive)
		w := WorkflowImpl{
			PhysicalCardDeliveryConfig: config.PhysicalCardDeliveryConfig{},
		}
		_, err := w.convertResponse(context.Background(), &data)
		assert.Equal(t, nil, err)
	})
	t.Run("status = Failed", func(t *testing.T) {
		data := ExecutionData{
			Request: &api.UpdateCardRequest{},
			Card:    &storage.Card{CardDesignID: test.TestCardDesignID},
		}
		data.Status = constant.Failed
		w := WorkflowImpl{
			PhysicalCardDeliveryConfig: config.PhysicalCardDeliveryConfig{},
		}
		_, err := w.convertResponse(context.Background(), &data)
		var errs []servus.ErrorDetail
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: data.Status,
			Message:   data.StatusReason,
			Path:      data.StatusReasonDescription,
		})
		expectedErr := logic.CustomError(http.StatusBadRequest, &errs)
		assert.Equal(t, expectedErr, err)
	})
}

func TestExecuteUpdateCardWorkflow(t *testing.T) {
	setMockDataSet()
	//defer testtools.Restore(testtools.Pairs(&logic.GetHeaderCtx, &logic.GetUserID, &logic.GetServiceID, &wfInit, &wfGet, &wfExecute)...)
	errDummy := errors.New("simulate error")
	testUserID := uuid.NewString()
	testcardID := uuid.NewString()
	testStatus := string(constant.CardStatusLocked)
	testReasonCode := uuid.NewString()
	defaultUpdateCardRequest := &api.UpdateCardRequest{
		CardID:     testcardID,
		Status:     testStatus,
		ReasonCode: testReasonCode,
	}
	scenarios := []struct {
		userID         string
		updateCardReq  *api.UpdateCardRequest
		errStatus      error
		errReason      error
		errCardID      error
		errIdempotency error
		errInit        error
		errGet         error
		errExecute     error
		expectedErr    error
	}{
		{
			expectedErr: logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
				{
					ErrorCode: string(api.FieldMissing),
					Message:   "'X-Grab-Id-Userid' is missing.",
					Path:      "X-Grab-Id-Userid",
				},
			}),
		},
		{
			userID: testUserID,
			updateCardReq: &api.UpdateCardRequest{
				Status:     testStatus,
				ReasonCode: testReasonCode,
				CardID:     "",
			},
			expectedErr: logic.CustomError(http.StatusBadRequest, &[]servus.ErrorDetail{
				{
					ErrorCode: string(api.FieldMissing),
					Message:   "Card-ID is missing",
					Path:      "Card-ID",
				},
			}),
		},
		{
			userID:        testUserID,
			updateCardReq: defaultUpdateCardRequest,
			errInit:       errDummy,
			expectedErr:   errDummy,
		},
		{
			userID:        testUserID,
			updateCardReq: defaultUpdateCardRequest,
			errInit:       workflowengine.ErrExecutionAlreadyExists,
			errGet:        errDummy,
			expectedErr:   errDummy,
		},
		{
			userID:        testUserID,
			updateCardReq: defaultUpdateCardRequest,
			errInit:       workflowengine.ErrExecutionAlreadyExists,
		},
		{
			userID:        testUserID,
			updateCardReq: defaultUpdateCardRequest,
			errExecute:    errDummy,
			expectedErr:   logic.ErrGenericServer,
		},
		{
			userID:        testUserID,
			updateCardReq: defaultUpdateCardRequest,
			errGet:        errDummy,
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		logic.GetHeaderCtx = func(ctx context.Context) http.Header {
			headers := http.Header{}
			headers.Set(logic.GetUserID(context.Background()), userID)
			return headers
		}
		logic.GetUserID = func(ctx context.Context) string {
			return testcase.userID
		}
		wfInit = func(ctx context.Context, e workflowengine.Execution, executionData workflowengine.ExecutionData) error {
			return testcase.errInit
		}
		wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				IdempotencyKey: uuid.NewString(),
				Request:        defaultUpdateCardRequest,
				Status:         "",
				Card:           &storage.Card{Status: string(constant.CardStatusActive), CardDesignID: test.TestCardDesignID},
			}, testcase.errGet
		}
		wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				IdempotencyKey: uuid.NewString(),
				Request:        defaultUpdateCardRequest,
				Status:         "",
				Card:           &storage.Card{Status: string(constant.CardStatusActive), CardDesignID: test.TestCardDesignID},
			}, testcase.errExecute
		}

		resp, err := WorkflowImpl{}.ExecuteUpdateCardWorkflow(context.Background(), testcase.updateCardReq)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, scenario.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
		}
	}
}

func setMockDataSet() {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)
}
