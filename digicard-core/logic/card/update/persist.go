package updatecard

import (
	"context"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
)

func (w *WorkflowImpl) persistCardUpdate(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		//slog.FromContext(ctx).Debug("debug", "error in getting current context", slog.CustomTag("error", ok))
		return nil, logic.ErrInvalidContext
	}
	query := []data.Condition{
		data.EqualTo("CardID", currCtx.Card.CardID),
		data.Limit(1),
	}
	querySearch, err := storage.CardDao.Find(ctx, query...)
	if err != nil {
		slog.FromContext(ctx).Warn(logtag, "error in finding card ID", slog.Error(err))
		w.Stats(ctx, constant.Failed, err.Error())
		w.publishCardUpdateStatusEvent(ctx, err, "", currCtx.Request.Status)
		return nil, err
	}
	nextCtx := currCtx.Clone()
	updatedCardStorage := querySearch[0]
	currentStatus := updatedCardStorage.Status
	resp := nextCtx.EuronetAdapterResponse
	switch resp.Status {
	case constant.Success:
		if nextCtx.Request.Status == string(constant.CardStatusLocked) {
			updatedCardStorage.Status = string(constant.CardStatusLocked)
		} else if nextCtx.Request.Status == string(constant.CardStatusActive) {
			updatedCardStorage.Status = string(constant.CardStatusActive)
		} else if nextCtx.Request.Status == string(constant.CardStatusRetired) {
			updatedCardStorage.Status = string(constant.CardStatusRetired)
			updatedCardStorage.CancelledAt = time.Now()
		}
		// Only update the DB if the call to EN-A is successful
		updatedCardStorage.UpdatedAt = time.Now()
		err = storage.CardDao.UpdateEntity(ctx, nextCtx.Card, updatedCardStorage)
		if err != nil {
			slog.FromContext(ctx).Warn(logtag, "error updating status in db")
			w.Stats(ctx, constant.Failed, err.Error())
			w.publishCardUpdateStatusEvent(ctx, err, currentStatus, nextCtx.Request.Status)
			return nil, err
		}

		nextCtx.Status = constant.Success
		nextCtx.State = stCardPersisted
		w.Stats(ctx, logic.PersistingTag, "")
	case constant.Failed:
		nextCtx.Status = constant.Failed
		nextCtx.StatusReason = resp.StatusReason
		nextCtx.StatusReasonDescription = resp.StatusMessage
		nextCtx.State = stFailed
		w.Stats(ctx, constant.Failed, nextCtx.StatusReason)
	default:
		slog.FromContext(ctx).Warn(logTag, "invalid card update response status")
		w.publishCardUpdateStatusEvent(ctx, logic.ErrInvalidResponseStatus, currentStatus, nextCtx.Request.Status)
		return nil, logic.ErrInvalidResponseStatus
	}
	nextCtx.Card = updatedCardStorage
	w.publishCardUpdateStatusEvent(ctx, nil, currentStatus, updatedCardStorage.Status)
	return nextCtx, nil
}
