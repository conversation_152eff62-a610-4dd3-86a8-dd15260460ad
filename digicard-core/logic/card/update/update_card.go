// Package updatecard provides logic for executing update card workflow
package updatecard

import (
	"context"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	card_design_utils "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

// ExecuteUpdateCardWorkflow ...
func (w WorkflowImpl) ExecuteUpdateCardWorkflow(ctx context.Context, req *api.UpdateCardRequest) (*api.CardResponse, error) {
	data := &ExecutionData{
		State:   stInit,
		Request: req,
	}
	if err := validateUserIDHeaderInfo(ctx); err != nil {
		slog.FromContext(ctx).Warn(logtag, "invalid header(s)")
		return nil, err
	}

	if errs := validateUpdateCardRequestParams(req); len(*errs) != 0 {
		slog.FromContext(ctx).Warn(logtag, "invalid request parameters")
		return nil, logic.CustomError(http.StatusBadRequest, errs)
	}

	if err := validateCard(ctx, req.CardID); err != nil {
		return nil, err
	}

	data.IdempotencyKey = uuid.NewString()
	data.CustomerID = logic.GetUserID(ctx)
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constant.IdempotencyKeyTag, data.IdempotencyKey),
		slog.CustomTag(constant.CustomerIDTag, data.CustomerID),
		slog.CustomTag("cardID", data.Request.CardID),
		slog.CustomTag("status", data.Request.Status),
		slog.CustomTag("statusReason", data.Request.ReasonCode))

	if err := wfInit(ctx, workflowengine.Execution{
		WorkflowID: workflowID,
		RequestID:  data.IdempotencyKey,
	}, data); err != nil {
		if err != workflowengine.ErrExecutionAlreadyExists {
			slog.FromContext(ctx).Warn(logtag, "error when initiating workflow", slog.Error(err))
			return nil, err
		}

		//attempt to check if existing workflow already exists
		execData, err := wfGet(ctx, workflowengine.Execution{
			WorkflowID: workflowID,
			RequestID:  data.IdempotencyKey,
		})
		if err != nil {
			slog.FromContext(ctx).Warn(logtag, "error when getting existing workflow", slog.Error(err))
			return nil, err
		}
		return w.convertResponse(ctx, execData.(*ExecutionData))
	}
	execData, err := wfExecute(ctx, workflowengine.Execution{
		WorkflowID:     workflowID,
		RequestID:      data.IdempotencyKey,
		ExecutionEvent: evValidate,
	}, nil)
	if err != nil {
		slog.FromContext(ctx).Warn(logtag, "error when running workflow", slog.Error(err))
		return nil, logic.ErrGenericServer
	}
	return w.convertResponse(ctx, execData.(*ExecutionData))
}

func validateUserIDHeaderInfo(ctx context.Context) error {
	customerID := logic.GetUserID(ctx)
	if customerID == "" {
		slog.FromContext(ctx).Warn(logtag, "grab user ID not found")
		return logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "'X-Grab-Id-Userid' is missing.",
				Path:      "X-Grab-Id-Userid",
			},
		})
	}
	return nil
}

func validateUpdateCardRequestParams(req *api.UpdateCardRequest) *[]servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if req.CardID == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "Card-ID is missing",
			Path:      "Card-ID",
		})
	}

	if req.Status == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "Status is missing",
			Path:      "Status",
		})
	} else if ok := logic.IsValidUpdateCardStatus(req.Status); !ok {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.BadRequest),
			Message:   "Invalid status",
			Path:      "Status",
		})
	}
	return &errs
}

func validateCard(ctx context.Context, cardID string) error {
	card, cardErr := logic.FetchCardByCardAndUserID(ctx, logic.GetUserID(ctx), cardID)
	if cardErr != nil {
		slog.FromContext(ctx).Warn(logtag, "logic.FetchCardByCardAndUserID Failed", slog.Error(cardErr))
		return cardErr
	}
	if card.ReasonCode == string(constant.ReasonCodeBankBlock) {
		return servus.ServiceError{
			Code:     "CARD_BLOCKED",
			Message:  "Card is blocked by bank",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
	}
	return nil
}

func (w WorkflowImpl) convertResponse(ctx context.Context, data *ExecutionData) (*api.CardResponse, error) {
	var card *api.CardDetail
	if data.Status == constant.Failed {
		var errs []servus.ErrorDetail
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: data.Status,
			Message:   data.StatusReason,
			Path:      data.StatusReasonDescription,
		})
		return nil, logic.CustomError(http.StatusBadRequest, &errs)
	}

	cardDesign, err := logic.FetchCardDesignByCardDesignID(ctx, data.Card.CardDesignID)
	if err != nil {
		return nil, err
	}
	// showing only active and locked cards for a user

	overSeasToggle, err := dto.ConvertJSONToToggleInfo(data.Card.ToggleInfo)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "ToggleInfo JSON : error in json.Unmarshal", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	card = &api.CardDetail{
		CardID:         data.Card.CardID,
		TailCardNumber: data.Card.TailCardNumber,
		HeadCardNumber: data.Card.HeadCardNumber,
		DisplayName:    data.Card.DisplayName,
		Status:         data.Card.Status,
		AccountID:      data.Card.AccountID,
		OrderStatus:    data.Card.OrderStatus,
		CardDesign:     card_design_utils.ToCardDesignDetail(*cardDesign, false),
		DeliveryInfo:   logic.GetPhysicalCardDeliveryInfo(w.PhysicalCardDeliveryConfig, data.Card),
		CardType:       logic.GetCardType(data.Card),
		ToggleInfo:     logic.ConvertToMapOfStringToMapOfInterface(overSeasToggle),
	}

	return &api.CardResponse{
		Card:                    card,
		Status:                  data.Status,
		StatusReason:            "",
		StatusReasonDescription: "",
	}, nil
}
