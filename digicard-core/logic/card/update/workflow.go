package updatecard

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	we "gitlab.myteksi.net/dakota/workflowengine"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
)

var (
	wfInit    = we.InitExecution
	wfGet     = we.GetExecution
	wfExecute = we.Execute
)

var (
	cardStatusEventMap = map[string]customerjournalapi.Event{
		string(constant.CardStatusLocked):  customerjournalapi.CardFreezeUpdateEvent,
		string(constant.CardStatusRetired): customerjournalapi.CardCancelEvent,
		string(constant.CardStatusActive):  customerjournalapi.CardUnfreezeUpdateEvent,
	}
)

const (
	digicardCore = "digicard_core"
)

// WorkflowImpl is the implementation of the workflow
type WorkflowImpl struct {
	StatsD                     statsd.Client                     `inject:"statsD"`
	EuronetAdapterClient       euronetAdapterAPI.EuronetAdapter  `inject:"client.euronetAdapter"`
	AccountServiceClientV2     accountAPIV2.AccountService       `inject:"client.accountServiceV2"`
	PhysicalCardDeliveryConfig config.PhysicalCardDeliveryConfig `inject:"config.physicalCardDelivery"`
	CardActivityLog            audit.CardActivityLogService      `inject:"client.activityLog"`
}

// ExecutionData is the state machine internal representation
type ExecutionData struct {
	Card                    *storage.Card
	State                   we.State
	Status                  string
	StatusReason            string
	StatusReasonDescription string
	IdempotencyKey          string
	CustomerID              string
	Request                 *api.UpdateCardRequest
	EuronetAdapterResponse  *euronetAdapterAPI.UpdateCardStatusResponse
}

const (
	workflowID = "updateCardWorkflow"
	logtag     = "updateCard"
)

// states definition:
const (
	stInit             = we.StateInit
	stValidated        = we.State(100)
	stCardPersisted    = we.State(101)
	stResponseReceived = we.State(102)

	stFailed = we.State(500)
)

const (
	evNoNeed   = we.EventNoNeed
	evValidate = we.Event(101)
)

// RegisterWorkflow should be called during initialisation to register individual workflow to the workflow engine
func (w *WorkflowImpl) RegisterWorkflow() {
	updateCardWorkflow := we.NewWorkflow(workflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})
	updateCardWorkflow.AddTransition(logtag, stInit, evValidate, w.validate, nil, stValidated, stFailed)
	updateCardWorkflow.AddTransition(logtag, stValidated, evNoNeed, w.invokeVendor, nil, stResponseReceived, stFailed)
	//TODO: Move and refactor persistCardUpdate into invokeVendor state transition
	updateCardWorkflow.AddTransition(logtag, stResponseReceived, evNoNeed, w.persistCardUpdate, nil, stCardPersisted, stFailed)
	we.RegisterWorkflow(updateCardWorkflow)
}

// GetWorkflow get the current execution of the workflow
func (w WorkflowImpl) GetWorkflow(ctx context.Context, runID string) (*dto.WorkflowResponse, error) {
	execData, err := we.GetExecution(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  runID,
	})
	if err != nil {
		return nil, api.DefaultInternalServerError
	}

	return &dto.WorkflowResponse{
		State:                  execData.GetState(),
		Status:                 StateToStatus(execData.GetState()),
		InternalIdempotencyKey: execData.(*ExecutionData).Card.UserID,
		Payload:                execData.(*ExecutionData),
	}, nil
}

// StateToStatus is temporary function to convert internal system state to human readable status
func StateToStatus(state we.State) string {
	// TODO: fix status mapping and move to common
	state /= 100.0
	switch state {
	case 0, 1, 2:
		return "PROCESSING"
	case 5:
		return "FAILED"
	case 9:
		return "COMPLETED"
	}
	return "PROCESSING"
}

// Stats ...
func (w *WorkflowImpl) Stats(ctx context.Context, status string, statusReason string) {
	slog.FromContext(ctx).Debug(logtag, fmt.Sprintf("publishing %s metric", status))
	w.StatsD.Count1(digicardCore, logtag,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}

func (w *WorkflowImpl) publishCardUpdateStatusEvent(ctx context.Context, activityErr error, oldStatus, newStatus string) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(audit.CardActivityLogTag, "CardUpdateStatus"))
	event, ok := cardStatusEventMap[newStatus]
	if !ok {
		slog.FromContext(ctx).Warn(logtag, "Audit Event not found, skip publishing")
		return
	}
	source := ctx.Value(audit.CardActivitySourceKey).(customerjournalapi.Source)
	if source == 0 {
		slog.FromContext(ctx).Warn(logtag, "Audit Source not found, skip publishing")
		return
	}

	activityLogReq := audit.CardActivityAuditRequest{
		Status:      customerjournalapi.AuditEventSuccess,
		Source:      source,
		Event:       event,
		BeforeValue: map[string]interface{}{audit.CardStatusDesc: oldStatus},
		AfterValue:  map[string]interface{}{audit.CardStatusDesc: newStatus},
	}

	if activityErr != nil {
		activityLogReq.AfterValue = map[string]interface{}{audit.CardStatusDesc: oldStatus}
		activityLogReq.Status = customerjournalapi.AuditEventFailed
		activityLogReq.FailedReason = activityErr.Error()
	}
	w.CardActivityLog.SendActivityLog(ctx, activityLogReq)
}
