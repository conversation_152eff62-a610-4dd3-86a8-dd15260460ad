package updatecard

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	euronetAdapterMock "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func TestInvokeVendor(t *testing.T) {
	t.Run("valid flow", func(t *testing.T) {
		data := ExecutionData{
			Request: &api.UpdateCardRequest{},
			Card:    &storage.Card{},
		}
		mockEuronetAdapter := &euronetAdapterMock.EuronetAdapter{}
		w := WorkflowImpl{
			StatsD:               statsd.NewNoop(),
			EuronetAdapterClient: mockEuronetAdapter,
		}
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything).Return(&euronetAdapterAPI.UpdateCardStatusResponse{
			Status:             "",
			StatusReason:       "",
			StatusMessage:      "",
			ReferenceID:        "",
			ProxyNumber:        "",
			CardSequenceNumber: 0,
		}, nil)
		_, err := w.invokeVendor(context.Background(), "", &data, nil)
		assert.Equal(t, err, nil)
	})
}
