// Package getcarddetailswithencryption ...
package getcarddetailswithencryption

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	logTag        = "GetCardDetailsWithEncryption"
	digiCardTag   = "digicard_core"
	userIDMissing = "'X-Grab-Id-Userid' is missing"
	errorCardID   = "error in finding card ID"
	invalidReq    = "invalid request parameters"
)

// Client ...
type Client interface {
	GetCardDetailsWithEncryption(ctx context.Context, req *api.GetCardDetailsWithEncryptionRequest) (*api.GetCardDetailsWithEncryptionResponse, error)
}

//go:generate mockery --name Client --inpackage --case=underscore

// NewClient ...
func NewClient() Client {
	return &clientImpl{}
}

type clientImpl struct {
	StatsD               statsd.Client                    `inject:"statsD"`
	EuronetAdapterClient euronetAdapterAPI.EuronetAdapter `inject:"client.euronetAdapter"`
}

// GetCardDetailsWithEncryption returns complete card details of a specific card ...
func (c *clientImpl) GetCardDetailsWithEncryption(ctx context.Context, req *api.GetCardDetailsWithEncryptionRequest) (*api.GetCardDetailsWithEncryptionResponse, error) {
	userID := logic.GetUserID(ctx)
	if errs := validateGetCardDetailsWithEncryptionParams(userID, req); len(errs) != 0 {
		slog.FromContext(ctx).Warn(logTag, "invalid request parameters")
		c.Stats(constant.Failed, invalidReq)
		return nil, logic.CustomError(http.StatusBadRequest, &errs)
	}
	query := []data.Condition{
		data.EqualTo("CardID", req.CardID),
		data.EqualTo("UserID", userID),
		data.ContainedIn("Status", string(constant.CardStatusActive), string(constant.CardStatusLocked), string(constant.CardStatusRetired)), // Only fetch active, locked and retired cards
	}
	querySearch, err := storage.CardDao.Find(ctx, query...)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, errorCardID, slog.Error(err))
		return nil, logic.CustomError(http.StatusBadRequest, &[]servus.ErrorDetail{{
			ErrorCode: string(api.FieldInvalid),
			Message:   "Invalid Card ID",
			Path:      "Card ID",
		}})
	}
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, userID)
	//TODO: Remove this after finishing E2E encryption flow
	//https://gxsbank.atlassian.net/browse/CARDS-976
	slog.FromContext(ctx).Debug(logTag, fmt.Sprintf("DEBUG LOGS. dc-core get card details request: %+v", req))
	enReqBody := &euronetAdapterAPI.GetEncryptedCVV2CardDetailsRequest{
		CardID:        req.CardID,
		EncryptedData: req.EncryptedData,
		Iv:            req.Iv,
		Salt:          req.Salt,
		PublicKey:     req.PublicKey,
	}
	resp, err := c.EuronetAdapterClient.GetEncryptedCVV2CardDetails(ctx, enReqBody)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error calling euronet adapter getCardDetails API")
		c.Stats(constant.Failed, invalidReq)
		return nil, err
	}
	c.Stats(constant.Success, "")
	return &api.GetCardDetailsWithEncryptionResponse{
		Card: &api.CardDetailWithEncryptedCardInfo{
			CardID:            querySearch[0].CardID,
			DisplayName:       querySearch[0].DisplayName,
			Status:            querySearch[0].Status,
			HomeCurrency:      querySearch[0].Currency,
			HomeCountry:       querySearch[0].Country,
			CardProvider:      querySearch[0].Provider,
			EncryptedCardInfo: resp.EncryptedCardDetails,
			Salt:              resp.Salt,
			Iv:                resp.Iv,
			PublicKey:         resp.PublicKey,
			CardType:          logic.GetCardType(querySearch[0]),
		},
	}, nil
}

func validateGetCardDetailsWithEncryptionParams(userID string, req *api.GetCardDetailsWithEncryptionRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if userID == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   userIDMissing,
			Path:      "X-Grab-Id-Userid",
		})
	}
	if req.CardID == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "CardID is missing",
			Path:      "CardID",
		})
	}
	if req.PublicKey == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "PubKey is missing",
			Path:      "PubKey",
		})
	}
	if req.Iv == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "Iv is missing",
			Path:      "Iv",
		})
	}
	if req.Salt == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "Salt is missing",
			Path:      "Salt",
		})
	}
	return errs
}

func (c *clientImpl) Stats(status string, statusReason string) {
	c.StatsD.Count1(digiCardTag, logTag,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}
