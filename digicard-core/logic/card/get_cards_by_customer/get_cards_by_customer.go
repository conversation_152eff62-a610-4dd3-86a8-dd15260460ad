// Package getcardsbycustomer ...
package getcardsbycustomer

import (
	"context"
	"fmt"
	"net/http"
	"time"

	commonCtx "gitlab.myteksi.net/dakota/common/context"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	logTag           = "GetCardDetailsByCustomer"
	digiCardTag      = "digicard_core"
	errorUserID      = "No such CustomerID-cardID pair found"
	previousPageLink = "/v1/card-detail-by-customer/%v/pageSize=%v&endingAfter=%v"
	nextPageLink     = "/v1/card-detail-by-customer/%v/pageSize=%v&startingBefore=%v"
	defaultPageSize  = 5
	minPageSize      = 1
	maxPageSize      = 10

	//TODO hardcode physicalCardCount
	physicalCardCount = int64(1)
)

// Client ...
type Client interface {
	GetCardDetailByCustomer(ctx context.Context, req *api.GetCardDetailsRequest) (*api.GetCardDetailsResponse, error)
}

//go:generate mockery --name Client --inpackage --case=underscore

// NewClient creates a new balance client.
func NewClient() Client {
	return &ClientImpl{}
}

// ClientImpl ...
type ClientImpl struct {
	StatsD                     statsd.Client                     `inject:"statsD"`
	PhysicalCardDeliveryConfig config.PhysicalCardDeliveryConfig `inject:"config.physicalCardDelivery"`
}

// GetCardDetailByCustomer ...
//
//nolint:funlen
func (c *ClientImpl) GetCardDetailByCustomer(ctx context.Context, req *api.GetCardDetailsRequest) (*api.GetCardDetailsResponse, error) {
	var errs []servus.ErrorDetail
	if req.CustomerID == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "Customer ID is missing",
			Path:      "CustomerID",
		})

		return nil, logic.CustomError(http.StatusBadRequest, &errs)
	}
	ctx = commonCtx.WithUserID(ctx, req.CustomerID)
	cursorData, err := logic.ParsePaginationCursorData(req.StartingBefore, req.EndingAfter)
	if err != nil {
		return nil, logic.CustomError(http.StatusBadRequest, &[]servus.ErrorDetail{
			{ErrorCode: string(api.FieldInvalid), Message: "Failed to parse cursor data", Path: "EndingAfter"},
		})
	}

	if req.PageSize < minPageSize {
		req.PageSize = defaultPageSize
	}
	if req.PageSize > maxPageSize {
		req.PageSize = maxPageSize
	}
	filters := cardListDBFilters(req, cursorData)
	querySearch, err := storage.CardDao.Find(ctx, filters...)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("error in fetching card details for the customer : %s", req.CustomerID), slog.Error(err))
		c.Stats(constant.Failed, errorUserID)
		if err == data.ErrNoData {
			return nil, logic.CustomError(http.StatusNotFound, &[]servus.ErrorDetail{{ErrorCode: string(api.FieldInvalid), Message: errorUserID, Path: "CustomerID-CardID"}})
		}
		return nil, logic.CustomError(http.StatusInternalServerError, &[]servus.ErrorDetail{{ErrorCode: string(api.InternalServerError), Message: "Failed to retrieve the card details"}})
	}

	var cards []api.ExposeCardDetail
	for _, card := range querySearch {
		cardDetail := api.ExposeCardDetail{
			CardID:                  card.CardID,
			CardProxyNumber:         card.VendorCardID,
			CardExpiryDate:          card.ExpiryDate,
			CardStatus:              card.Status,
			PhysicalCardOrderStatus: card.OrderStatus,
			DisplayName:             card.DisplayName,
			ReasonCode:              card.ReasonCode,
			ReasonDescription:       card.ReasonDescription,
			DeliveryInfo:            logic.GetPhysicalCardDeliveryInfo(c.PhysicalCardDeliveryConfig, card),
			CardLastFourDigit:       card.TailCardNumber,
			CardCreationDate:        card.CreatedAt.Format(time.RFC3339),
			AccountID:               card.AccountID,
			PhysicalCardCount:       physicalCardCount,
		}

		if !card.ActivatedAt.IsZero() {
			cardDetail.CardIssueDate = card.ActivatedAt.Format(time.RFC3339)
		}

		if !card.PhysicalCardActivatedAt.IsZero() {
			cardDetail.PhysicalCardActivationTime = card.PhysicalCardActivatedAt.Format(time.RFC3339)
		}

		if !card.CancelledAt.IsZero() {
			cardDetail.CardCancellationTime = card.CancelledAt.Format(time.RFC3339)
		}

		if !card.PhysicalCardOrderedDate.IsZero() {
			cardDetail.PhysicalCardOrderDate = card.PhysicalCardOrderedDate.Format(time.RFC3339)
		}

		cards = append(cards, cardDetail)
	}
	c.Stats(constant.Success, "")
	paginationParams := logic.MapPaginationParameters(req.CustomerID, req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, req.PageSize)
	links := logic.PaginationLinks(querySearch, paginationParams, cursorData, previousPageLink, nextPageLink)
	return &api.GetCardDetailsResponse{Links: links, Data: cards}, nil
}

// Stats ...
func (c *ClientImpl) Stats(status string, statusReason string) {
	c.StatsD.Count1(digiCardTag, logTag,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}

// cardListDBFilters creates filter condition
func cardListDBFilters(req *api.GetCardDetailsRequest, cursorData dto.PaginationCursor) []data.Condition {
	// add default filters
	filters := []data.Condition{
		data.EqualTo("UserID", req.CustomerID),
		data.Limit(int(1 * req.PageSize)),
	}

	switch {
	case req.EndingAfter == "" && req.StartingBefore == "":
		filters = append(filters,
			data.DescendingOrder("CreatedAt"),
			data.DescendingOrder("ID"))
	case req.EndingAfter != "":
		filters = append(filters,
			data.GreaterThan("ID", cursorData.ID),
			data.AscendingOrder("CreatedAt"),
			data.AscendingOrder("ID"),
			data.UnEqualTo("CardID", cursorData.CardID),
		)
	case req.StartingBefore != "":
		filters = append(filters,
			data.LessThan("ID", cursorData.ID),
			data.DescendingOrder("CreatedAt"),
			data.DescendingOrder("ID"),
			data.UnEqualTo("CardID", cursorData.CardID),
		)
	}
	startingDate, endingDate := computeDateRange(req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, cursorData)
	if startingDate != "" {
		filters = append(filters, data.GreaterThanOrEqualTo("CreatedAt", startingDate))
	}
	if endingDate != "" {
		filters = append(filters, data.LessThanOrEqualTo("CreatedAt", endingDate))
	}
	return filters
}

// computeDateRange will compute the time range based on input params and cursor.
func computeDateRange(startingBefore string, endingAfter string, startDate string, endDate string, cursorData dto.PaginationCursor) (string, string) {
	var startingDate, endingDate string
	switch {
	case endingAfter == "" && startingBefore == "":
		startingDate = startDate
		endingDate = endDate
	case endingAfter != "":
		startingDate = cursorData.Date
		endingDate = endDate
	case startingBefore != "":
		startingDate = startDate
		endingDate = cursorData.Date
	}
	return startingDate, endingDate
}
