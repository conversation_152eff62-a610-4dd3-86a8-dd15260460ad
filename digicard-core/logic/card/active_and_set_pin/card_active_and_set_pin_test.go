package activeandsetpin

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"testing"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	we "gitlab.myteksi.net/dakota/workflowengine"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.myteksi.net/dakota/workflowengine"

	physicalCardActivation "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/activate"
	cardPinManager "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/pin_manager"
)

const (
	userID         = "USER_ID"
	cardID         = "1123"
	idempotencyKey = "{{$guid}}"
)

func TestExecuteUpdateCardWorkflow(t *testing.T) {
	setMockDataSet()
	errDummy := errors.New("simulate error")
	testUserID := uuid.NewString()
	testcardID := uuid.NewString()
	testEncryptedPin := uuid.NewString()
	defaultCardActivateAndSetPinRequest := &api.CardActivateAndSetPinRequest{
		CardID:         testcardID,
		IdempotencyKey: idempotencyKey,
		EncryptedPin:   testEncryptedPin,
	}
	scenarios := []struct {
		userID               string
		activateCardReq      *api.CardActivateAndSetPinRequest
		getWorkflowState     workflowengine.State
		executeWorkflowState workflowengine.State
		errStatus            error
		errReason            error
		errCardID            error
		errIdempotency       error
		errInit              error
		errGet               error
		errExecute           error
		expectedErr          error
		activateCardErr      error
		description          string
	}{
		{
			description:     "Error init workflow",
			userID:          testUserID,
			activateCardReq: defaultCardActivateAndSetPinRequest,
			errInit:         errDummy,
			expectedErr:     errDummy,
		},
		{
			description:     "Error executing activate card wf",
			userID:          testUserID,
			activateCardReq: defaultCardActivateAndSetPinRequest,
			errExecute:      errDummy,
			expectedErr:     logic.ErrGenericServer,
		},
		{
			description:      "Execution already exist - prev state stuck stInit",
			userID:           testUserID,
			activateCardReq:  defaultCardActivateAndSetPinRequest,
			errInit:          we.ErrExecutionAlreadyExists,
			getWorkflowState: stInit,
		},
		{
			description:      "Execution already exist - prev state stActivateCardFailed",
			userID:           testUserID,
			activateCardReq:  defaultCardActivateAndSetPinRequest,
			errInit:          we.ErrExecutionAlreadyExists,
			getWorkflowState: stActivateCardFailed,
		},
		{
			description:      "Execution already exist - prev stuck stActivateCardCompleted",
			userID:           testUserID,
			activateCardReq:  defaultCardActivateAndSetPinRequest,
			errInit:          we.ErrExecutionAlreadyExists,
			getWorkflowState: stActivateCardCompleted,
		},
		{
			description:      "Execution already exist - prev state stSetPinFailed",
			userID:           testUserID,
			activateCardReq:  defaultCardActivateAndSetPinRequest,
			errInit:          we.ErrExecutionAlreadyExists,
			getWorkflowState: stSetPinFailed,
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		logic.GetHeaderCtx = func(ctx context.Context) http.Header {
			headers := http.Header{}
			headers.Set(logic.GetUserID(context.Background()), userID)
			return headers
		}
		logic.GetUserID = func(ctx context.Context) string {
			return testcase.userID
		}
		wfInit = func(ctx context.Context, e workflowengine.Execution, executionData workflowengine.ExecutionData) error {
			return testcase.errInit
		}
		wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				IdempotencyKey: uuid.NewString(),
				Request:        defaultCardActivateAndSetPinRequest,
				State:          testcase.getWorkflowState,
			}, testcase.errGet
		}
		wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
			return &ExecutionData{}, testcase.errExecute
		}

		mockPhysicalCardActivationClient := &physicalCardActivation.MockPhysicalCardActivateClient{}
		mockCardPinManagerClient := &cardPinManager.MockCardPinManagerClient{}

		workflow := WorkflowImpl{
			StatsD: statsd.NewNoop(),
			FeatureFlags: &config.FeatureFlags{
				EnableActivationCodeCheck: false,
			},
			PhysicalCardActivateClient: mockPhysicalCardActivationClient,
			CardPinManagerClient:       mockCardPinManagerClient,
		}

		err := workflow.ExecuteCardActivateAndSetPinWorkflow(context.Background(), testcase.activateCardReq)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, scenario.expectedErr, err, description)
		} else {
			assert.NoError(t, err, description)
		}
	}
}

func setMockDataSet() {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)
}
