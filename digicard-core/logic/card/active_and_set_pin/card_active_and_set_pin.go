// Package activeandsetpin ...
package activeandsetpin

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/bgcontext"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/notification"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
)

// ExecuteCardActivateAndSetPinWorkflow ...
func (w *WorkflowImpl) ExecuteCardActivateAndSetPinWorkflow(ctx context.Context, req *api.CardActivateAndSetPinRequest) error {
	userID := commonCtx.GetUserID(ctx)
	requestID := req.CardID
	data := &ExecutionData{
		CustomerID: userID,
		Request:    req,
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("cardID", req.CardID),
		slog.CustomTag("customerID", userID))

	if err := wfInit(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  requestID,
	}, data); err != nil {
		if !errors.Is(err, we.ErrExecutionAlreadyExists) {
			slog.FromContext(ctx).Error(logCardActivateAndSetPin, "error when initiating workflow", slog.Error(err))
			return err
		}

		return reTriggerActivateAndSetPin(ctx, requestID, req)
	}

	return triggerActivateCardWf(ctx, requestID, req)
}

func reTriggerActivateAndSetPin(ctx context.Context, requestID string, req *api.CardActivateAndSetPinRequest) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  requestID,
	})

	if err != nil {
		slog.FromContext(ctx).Error(logCardActivateAndSetPin, "error when getting existing workflow for card active and set pin", slog.Error(err))
		return logic.ErrGenericServer
	}

	switch execData.GetState() {
	case stActivateCardFailed, stInit:
		return triggerReActivateCardWf(ctx, requestID, req)
	case stSetPinFailed, stActivateCardCompleted:
		return triggerReSetPinCardWf(ctx, requestID, req)
	default:
		errorCode := api.ResourceConflict
		return &servus.ServiceError{
			Code:     string(errorCode),
			Message:  "Something went wrong. Please try again later or visit our Help Centre.",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
	}
}

func (w *WorkflowImpl) activate(ctx context.Context, transitionID string, executionData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := executionData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Error(logCardActivateAndSetPin, "Invalid context passed in persistRequest state")
		return nil, logic.ErrInvalidContext
	}

	nextCtx := currCtx.Clone()

	if err := w.PhysicalCardActivateClient.PhysicalCardActivation(ctx, convertPhysicalCardActivateReq(currCtx.Request)); err != nil {
		slog.FromContext(ctx).Error(logCardActivateAndSetPin, err.Error())
		nextCtx.State = stActivateCardFailed
		errResp := err.(servus.ServiceError)
		nextCtx.ErrorResponse = &errResp
		return nextCtx, nil
	}

	// Setting error response back to nil because it is a successful response again
	nextCtx.ErrorResponse = nil
	nextCtx.State = stActivateCardCompleted
	return nextCtx, nil
}

func (w *WorkflowImpl) setPin(ctx context.Context, transitionID string, executionData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := executionData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Error(logCardActivateAndSetPin, "Invalid context passed in persistRequest state")
		return nil, logic.ErrInvalidContext
	}

	reqParam, ok := params.(*api.CardActivateAndSetPinRequest)
	if !ok {
		slog.FromContext(ctx).Warn(logCardActivateAndSetPin, "Wrong params passed in setPin state")
		return nil, logic.ErrInvalidParameters
	}

	nextCtx := currCtx.Clone()

	if err := w.CardPinManagerClient.SetPin(ctx, convertCardSetPinReq(reqParam), false); err != nil {
		slog.FromContext(ctx).Error(logCardActivateAndSetPin, err.Error())
		nextCtx.State = stSetPinFailed
		errResp := err.(servus.ServiceError)
		nextCtx.ErrorResponse = &errResp
		return nextCtx, nil
	}

	card, err := logic.FetchCardByCardAndUserID(ctx, logic.GetUserID(ctx), nextCtx.Request.CardID)
	if err != nil {
		slog.FromContext(ctx).Warn(logCardActivateAndSetPin, "CARD_NOT_FOUND", slog.Error(err))
		return nil, err
	}
	newCard := *card
	newCard.SetPinAt = time.Now()
	newCard.UpdatedAt = time.Now()

	err = storage.CardDao.UpdateEntity(ctx, card, &newCard)
	if err != nil {
		slog.FromContext(ctx).Warn(logCardActivateAndSetPin, "storage.CardDao.UpdateEntity : set pin successful but failed in updated set_pin_at", slog.Error(err))
		nextCtx.State = stSetPinCompleted
		return nextCtx, nil
	}

	// Send push notification on successful set pin
	if errPush := w.pushSetPinActivateNotification(ctx, &newCard); errPush != nil {
		return nil, errPush
	}

	nextCtx.State = stSetPinCompleted
	return nextCtx, nil
}
func convertPhysicalCardActivateReq(req *api.CardActivateAndSetPinRequest) *api.PhysicalCardActivationRequest {
	return &api.PhysicalCardActivationRequest{
		CardID: req.CardID,
	}
}

func convertCardSetPinReq(req *api.CardActivateAndSetPinRequest) *api.CardSetPinRequest {
	return &api.CardSetPinRequest{
		CardID:         req.CardID,
		EncryptedPin:   req.EncryptedPin,
		IdempotencyKey: req.IdempotencyKey,
	}
}

//nolint:dupl
func triggerActivateCardWf(ctx context.Context, requestID string, req *api.CardActivateAndSetPinRequest) error {
	execData, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		RequestID:      requestID,
		ExecutionEvent: evActivateCard,
	}, req)
	if err != nil {
		slog.FromContext(ctx).Warn(logCardActivateAndSetPin, "error when running card activation workflow", slog.Error(err))
		return logic.ErrGenericServer
	}
	currCtx, ok := execData.(*ExecutionData)
	if ok && currCtx.ErrorResponse != nil {
		slog.FromContext(ctx).Error(logCardActivateAndSetPin, currCtx.ErrorResponse.Message)
		return currCtx.ErrorResponse
	}

	return nil
}

//nolint:dupl
func triggerReActivateCardWf(ctx context.Context, requestID string, req *api.CardActivateAndSetPinRequest) error {
	execData, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		RequestID:      requestID,
		ExecutionEvent: evReActivateCard,
	}, req)
	if err != nil {
		slog.FromContext(ctx).Warn(logCardActivateAndSetPin, "error when running card activation workflow", slog.Error(err))
		return logic.ErrGenericServer
	}
	currCtx, ok := execData.(*ExecutionData)
	if ok && currCtx.ErrorResponse != nil {
		slog.FromContext(ctx).Error(logCardActivateAndSetPin, currCtx.ErrorResponse.Message)
		return currCtx.ErrorResponse
	}
	return nil
}

//nolint:dupl
func triggerReSetPinCardWf(ctx context.Context, requestID string, req *api.CardActivateAndSetPinRequest) error {
	execData, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		RequestID:      requestID,
		ExecutionEvent: evReSetPinCard,
	}, req)
	if err != nil {
		slog.FromContext(ctx).Warn(logCardActivateAndSetPin, "error when running set pin card workflow", slog.Error(err))
		return logic.ErrGenericServer
	}
	currCtx, ok := execData.(*ExecutionData)
	if ok && currCtx.ErrorResponse != nil {
		slog.FromContext(ctx).Error(logCardActivateAndSetPin, currCtx.ErrorResponse.Message)
		return currCtx.ErrorResponse
	}
	return nil
}

// push notification and email
func (w *WorkflowImpl) pushSetPinActivateNotification(ctx context.Context, card *storage.Card) error {
	slog.FromContext(ctx).Info(logCardActivateAndSetPin, fmt.Sprintf("Notification - Activate and set pin success"))

	bgCtx := bgcontext.BackgroundWithValue(ctx)
	notificationReqPush := &notification.Request{
		RecipientID: card.UserID,
		TemplateID:  notification.CardActivatedTemplate,
		Params: map[string]string{
			notification.PreferredName: card.DisplayName,
		},
	}
	notificationReqEmail := &notification.Request{
		RecipientID: card.UserID,
		TemplateID:  notification.CardActivatedEmailTemplate,
		Params: map[string]string{
			notification.PreferredName: card.DisplayName,
		},
	}

	gconcurrent.Go(bgCtx, logCardActivateAndSetPin, pushNotify(w.PushNotifier, notificationReqPush))
	gconcurrent.Go(bgCtx, logCardActivateAndSetPin, emailNotify(w.EmailNotifier, notificationReqEmail))

	return nil
}

func pushNotify(notifier notification.Notifier, req *notification.Request) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		err := notifier.Notify(ctx, req)
		if err != nil {
			slog.FromContext(ctx).Error(logCardActivateAndSetPin, fmt.Sprintf("Error when sending push notification: %s - template_id: %s", err, req.TemplateID))
			return err
		}
		slog.FromContext(ctx).Info(logCardActivateAndSetPin, fmt.Sprintf("Push notification successful, template_id: %s", req.TemplateID))
		return nil
	}
}

// buildEmailNotificationRequest ...
func emailNotify(notifier notification.Notifier, req *notification.Request) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		err := notifier.Notify(ctx, req)
		if err != nil {
			slog.FromContext(ctx).Error(logCardActivateAndSetPin, fmt.Sprintf("Error when sending email notification: %s - template_id: %s", err, req.TemplateID))
			return err
		}
		slog.FromContext(ctx).Info(logCardActivateAndSetPin, fmt.Sprintf("Email notification successful, template_id: %s", req.TemplateID))
		return nil
	}
}
