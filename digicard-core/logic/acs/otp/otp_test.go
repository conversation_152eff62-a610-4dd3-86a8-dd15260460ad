package otp

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	customerMasterAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
	pigeon "gitlab.myteksi.net/dbmy/pigeon/api/mock"
)

const (
	cardID = "1234"
	userID = "USER_ID"
)

func Test_clientImpl_TriggerOtp(t *testing.T) {
	testEmail := "<EMAIL>"
	testPhone := "+6512341234"
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO
	mockPigeon := &pigeon.Pigeon{}
	mockCustomerMaster := &customerMasterMock.CustomerMaster{}
	c := &clientImpl{PigeonClient: mockPigeon, CustomerMasterDBMYClient: mockCustomerMaster}
	happyCustomerData := &customerMasterAPI.Customer{
		Contacts: []*customerMasterAPI.ContactDetail{
			{
				ContactType: nil,
				Email:       &testEmail,
				PhoneNumber: &testPhone,
			},
		},
	}
	jsonData, _ := json.Marshal(happyCustomerData)
	happyCustomerMasterResp := &customerMasterAPI.GetCustomerResponse{Customer: &customerMasterAPI.CustomerData{Data: jsonData}}
	card := &storage.Card{
		Status:                  string(constant.CardStatusActive),
		CardID:                  cardID,
		OrderStatus:             "ORDERED",
		PhysicalCardOrderedDate: time.Now().AddDate(0, 0, -3),
		PostScript:              []byte(`{"ActivationCode": "J6A7B", "DeliveryAddress": null}`),
	}
	tests := []struct {
		name     string
		req      *api.TriggerOtpRequest
		mockFunc func()
		wantErr  error
	}{
		{
			name: "happy path",
			req: &api.TriggerOtpRequest{
				UserID: userID,
				CardID: cardID,
				Otp:    "123456",
			},
			mockFunc: func() {
				mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
				mockPigeon.On("Sms", mock.Anything, mock.Anything).Return(nil, nil).Once()
				mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(happyCustomerMasterResp, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "logic.FetchCardByCardAndUserID Failed",
			req: &api.TriggerOtpRequest{
				UserID: userID,
				CardID: cardID,
				Otp:    "123456",
			},
			mockFunc: func() {
				mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
			},
			wantErr: servus.ServiceError{
				Code:     string(api.CardNotFound),
				Message:  "Card not found",
				HTTPCode: api.BadRequest.HTTPStatusCode(),
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()
			err := c.TriggerOtp(context.Background(), tt.req)
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}
