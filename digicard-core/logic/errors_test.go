package logic

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestCustomError(t *testing.T) {
	t.Run("valid", func(t *testing.T) {
		httpCode := 400
		errs := &[]servus.ErrorDetail{{}}
		expectedErr := servus.ServiceError{
			HTTPCode: httpCode,
			Code:     "INVALID_PARAMETERS",
			Message:  "request has invalid parameter(s) or header(s).",
			Errors:   *errs,
		}
		err := CustomError(httpCode, errs)
		assert.Equal(t, expectedErr, err)
	})
}

func TestBuildErrorResponse(t *testing.T) {
	t.Run("valid", func(t *testing.T) {
		Code := api.ErrorCode(rune(1))
		expectedErr := servus.ServiceError{
			HTTPCode: Code.HTTPStatusCode(),
			Code:     string(Code),
			Message:  "",
		}
		err := BuildErrorResponse(Code, "")
		assert.Equal(t, expectedErr, err)
	})
}
