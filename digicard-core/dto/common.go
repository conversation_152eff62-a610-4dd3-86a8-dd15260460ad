package dto

import (
	"encoding/json"
	"errors"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
)

// GetDefaultToggleInfo ...
func GetDefaultToggleInfo() *map[string]string {
	toggleInfo := make(map[string]string)
	for key := range constant.ValidToggles {
		toggleInfo[key] = string(constant.ToggleDisabled)
	}
	return &toggleInfo
}

// ConvertToggleInfoToJSON ...
func ConvertToggleInfoToJSON(data map[string]string) (json.RawMessage, error) {
	if data != nil {
		toggleInfoJSON, err := json.Marshal(data)
		if err != nil {
			return nil, err
		}
		return toggleInfoJSON, nil
	}
	return nil, errors.New("toggle info is empty")
}

// ConvertJSONToToggleInfo ...
func ConvertJSONToToggleInfo(data json.RawMessage) (map[string]string, error) {
	toggleInfo := map[string]string{}
	if data != nil {
		err := json.Unmarshal(data, &toggleInfo)
		if err != nil {
			return toggleInfo, err
		}
	}
	return toggleInfo, nil
}

// ConvertPostScriptToJSON ...
func ConvertPostScriptToJSON(postScript *CardPostScript) (json.RawMessage, error) {
	if postScript != nil {
		postScriptJSON, err := json.Marshal(postScript)
		if err != nil {
			return nil, err
		}
		return postScriptJSON, nil
	}
	return nil, errors.New("postscript is empty")
}

// ConvertJSONToPostScript ...
func ConvertJSONToPostScript(postScriptJSON json.RawMessage) (*CardPostScript, error) {
	postScript := &CardPostScript{}
	if postScriptJSON != nil {
		err := json.Unmarshal(postScriptJSON, &postScript)
		if err != nil {
			return postScript, err
		}
	}
	return postScript, nil
}

var (
	// EnquiryTypeCardStatusMap ...
	EnquiryTypeCardStatusMap = map[constant.EnquiryType][]constant.CardStatus{
		constant.AccountStatusEnquiry: {constant.CardStatusActive},
		constant.DisputeRefund:        {constant.CardStatusActive, constant.CardStatusRetired, constant.CardStatusLocked},
		constant.MerchantRefund:       {constant.CardStatusActive, constant.CardStatusRetired, constant.CardStatusLocked},
		constant.CaptureClear:         {constant.CardStatusActive, constant.CardStatusRetired, constant.CardStatusLocked},
		constant.DirectTransferClear:  {constant.CardStatusActive, constant.CardStatusLocked},
		constant.AuthExpiry:           {constant.CardStatusActive, constant.CardStatusRetired, constant.CardStatusLocked},
	}
)

// GroupTag is the type of transaction
type GroupTag string

const (
	// CardIssuanceCharge represents type for charge transaction
	CardIssuanceCharge GroupTag = "CARD_ISSUANCE_CHARGE"
)
