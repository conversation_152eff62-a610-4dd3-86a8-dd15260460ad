package constant

// TxnStatus defines the list of transactional statuses being used between digicard services.
type TxnStatus string

const (
	// TxnProcessing is the status to indicate request is being processed
	TxnProcessing TxnStatus = "PROCESSING"
	// TxnFailed is the status from payment core to indicate request has failed
	TxnFailed TxnStatus = "FAILED"
	// TxnAuthorised is the status to indicate auth has been completed and waiting for upstream action //TODO: refactor to authori'z'ed
	TxnAuthorised TxnStatus = "AUTHORISED"
	// TxnCanceled is the status to indicate the transaction is canceled by the upstream //TODO: refactor to reverted
	TxnCanceled TxnStatus = "CANCELED"
	// TxnReverted is the status to indicate the transaction is canceled by the upstream
	TxnReverted TxnStatus = "REVERTED"
	// TxnCompleted is the status to indicate the transaction has been completed successfully
	TxnCompleted TxnStatus = "COMPLETED"
	// TxnUnknown is the status from payment core to indicate undefined behavior
	TxnUnknown TxnStatus = "UNKNOWN"
	// TxnError indicates some unexpected exception has occurred
	TxnError TxnStatus = "ERROR"
)

// InternalTxnStatus defines the list of transaction statuses used internally for digicard services.
type InternalTxnStatus string

const (
	// PartialCaptureCompleted is the status to that the partial capture is successful
	PartialCaptureCompleted InternalTxnStatus = "PARTIAL_CAPTURE_COMPLETED"
)

type FeeChargeType string

const (
	// FeeWaive ...
	FeeWaive = "WAIVE"
	// FeeCharge ...
	FeeCharge = "CHARGE"
)
